// circle detector
// Copyright (C) UNIC Technologies. All rights reserved.
// History:
// (1) 20120711: <NAME_EMAIL>
// (2) 20121117: change nSmoothRadius to fScale by <PERSON>

#include <vector>
#include <deque>
using namespace std;
#include <cmath>
#include <cstdlib>
#include <opencv2/opencv.hpp>
#include "uvision.h"
#include "EdgeDetector.h"
using namespace EdgeDetector;
#include "CircleDetectorCore.h"

#pragma warning(disable : 4018)

#ifndef MIN_CIRCLE_RADIUS
#define MIN_CIRCLE_RADIUS 6
#endif

#ifndef PI
#define PI 3.141592653589793238462643383
#endif

CircleDetector::CircleDetector()
{
	m_alignX = m_alignY = m_alignAngle = 0;
	m_alignRefX = m_alignRefY = 0;
	m_alignScale = 1;

	ClearResults();
}

CircleDetector::~CircleDetector()
{

}

CBaseInspector::Type CircleDetector::GetType() const
{
	return Type_ImageInspector;
}

void CircleDetector::GetNameAndDescription(std::wstring &name, std::wstring &description) const
{
	name = L"Circle detector";
	description = L"Circle detector";
}

int CircleDetector::GetNumImages() const
{
	return 1;
}

bool CircleDetector::GetImageInfo(int i, std::wstring &name, std::wstring &description) const
{
	if (i != 0)
		return false;

	name = L"Input image";
	description = L"Input image";

	return true;
}

bool CircleDetector::SetImage(int imageId, void *image, int width, int height, int bytesPerLine, int bitsPerPixel)
{
	ClearResults();
	
	if (imageId == 0 && width > 0 && height > 0 && image != NULL && (bitsPerPixel == 8 || bitsPerPixel == 24 || bitsPerPixel == 32))
	{
		return SetTestImage((unsigned char *)image, width, height, bytesPerLine, bitsPerPixel);
	}
	else
		return false;


}

int CircleDetector::GetNumRegionUsages() const
{
	return 1;
}

bool CircleDetector::GetRegionUsageInfo(int i, std::wstring &name, std::wstring &description, int &usage, int &typeOption) const
{
	if (i == 0)
	{
		name = L"ROI";
		description = L"ROI";
		usage = RoiUsage_Include;
		typeOption = CRegion::Type_Rect;
	}
	else
		return false;

	return true;
}

bool CircleDetector::SetRegions(const std::vector<CRegion> &regions)
{
	ClearResults();

	//if (regions.size() < 1)
	//	return false;

	m_rois = regions;

	return true;
}

int CircleDetector::GetNumParameters() const
{
	return 11;
}

bool CircleDetector::GetParameter(int i, CParameter &param) const
{
	if (i >= GetNumParameters() || i < 0)
		return false;

	 if(i == 0)
	{
		param.SetName(L"Circle polarity");
		param.SetDescription(L"Circle polarity");
		param.ClearEnumItems();
		param.AddEnumItem(CircleDetector::kPolarityAny, L"Any");
		param.AddEnumItem(CircleDetector::kPolarityBright, L"Bright");
		param.AddEnumItem(CircleDetector::kPolarityDark, L"Dark");
		param.SetEnumDefault(CircleDetector::Param().ePolarity);
		param.SetEnum(m_param.ePolarity);
	}
	else if (i == 1)
	{
		param.SetName(L"Edge threshold");
		param.SetDescription(L"Edge threshold");
		param.SetIntDefault(Param().nEdgeTh);
		param.SetInt(m_param.nEdgeTh);
		param.SetIntLimits(0, 255);
	}
	else if (i == 2)
	{
		param.SetName(L"Smooth radius");
		param.SetDescription(L"Smooth radius");
		param.SetIntDefault(Param().smoothRadius);
		param.SetInt(m_param.smoothRadius);
		param.SetIntLimits(0, 255);
	}
	else if (i == 3)
	{
		param.SetName(L"Min diameter");
		param.SetDescription(L"Min diameter(mm)");
		param.SetFloatDefault(Param().fRealMinDiameter);
		param.SetFloat(m_param.fRealMinDiameter);
		param.SetFloatLimits(0, 10000000);
	}
	else if (i == 4)
	{
		param.SetName(L"Max diameter");
		param.SetDescription(L"Max diameter(mm)");
		param.SetFloatDefault(Param().fRealMaxDiameter);
		param.SetFloat(m_param.fRealMaxDiameter);
		param.SetFloatLimits(0, 10000000);
	}
	else if (i == 5)
	{
		param.SetName(L"Epsilon");
		param.SetDescription(L"Epsilon");
		param.SetFloatDefault(Param().fEpsilon);
		param.SetFloat(m_param.fEpsilon);
		param.SetFloatLimits(0.0, 100.0);
	}
	else if (i == 6)
	{
		param.SetName(L"Min score");
		param.SetDescription(L"Minimum score");
		param.SetIntDefault(Param().nMinScore);
		param.SetInt(m_param.nMinScore);
		param.SetIntLimits(0, 100);
	}
	else if (i == 7)
	{
		param.SetName(L"Down sample times");
		param.SetDescription(L"Down sample times");
		param.SetIntDefault(Param().nDownSample2sqr);
		param.SetInt(m_param.nDownSample2sqr);
		param.SetIntLimits(0, 100);
	}
	else if (i == 8)
	{
		param.SetName(L"Image channel");
		param.SetDescription(L"Image channel");
		param.ClearEnumItems();
		param.AddEnumItem(CircleDetector::Channel_Gray, L"Gray");
		param.AddEnumItem(CircleDetector::Channel_Red, L"Red");
		param.AddEnumItem(CircleDetector::Channel_Green, L"Green");
		param.AddEnumItem(CircleDetector::Channel_Blue, L"Blue");
		param.SetEnumDefault(CircleDetector::Param().channel);
		param.SetEnum(m_param.channel);
	}
	else if (i == 9)
	{
		param.SetName(L"Pixel size");
		param.SetDescription(L"Pixel size(mm)");
		param.SetFloatDefault(Param().fPixelSize);
		param.SetFloat(m_param.fPixelSize);
		param.SetFloatLimits(0.0, 100.0);
	}
	else if (i == 10)
	{
		param.SetName(L"Dump image");
		param.SetDescription(L"Dump image");
		param.SetBoolDefault(CircleDetector::Param().bDumpImage);
		param.SetBool(m_param.bDumpImage);
	}

	return true;
}

bool CircleDetector::SetParameter(int i, const CParameter &param)
{
	ClearResults();

	if (i >= GetNumParameters() || i < 0)
		return false;

	if(i == 0)
		m_param.ePolarity = static_cast<CircleDetector::Polarity>(param.GetEnum());
	else if (i == 1)
		m_param.nEdgeTh = param.GetInt();
	else if (i == 2)
		m_param.smoothRadius = param.GetInt();
	else if (i == 3)
		m_param.fRealMinDiameter  = param.GetFloat();
	else if (i == 4)
		m_param.fRealMaxDiameter = param.GetFloat();
	else if (i == 5)
		m_param.fEpsilon = param.GetFloat();
	else if (i == 6)
		m_param.nMinScore = param.GetInt();
	else if (i == 7)
		m_param.nDownSample2sqr = param.GetInt();
	else if (i == 8)
		m_param.channel = static_cast<CircleDetector:: Channel>(param.GetEnum());
	else if (i == 9)
		m_param.fPixelSize = param.GetFloat();
	else if (i == 10)
		m_param.bDumpImage = param.GetBool();
	return true;
}

int CircleDetector::SetPara(const Param &para)
{
    m_param = para;
    return 0;
}

int CircleDetector::GetNumTemplates()const
{
	return 0;
}

bool CircleDetector::GetTemplateInfo(int i, std::wstring &name, std::wstring &description) const
{
	return false;
}

bool CircleDetector::SetTemplate(int i, const std::wstring &pathName)
{
	return true;
}

int CircleDetector::GetNumReferenceInputs() const
{
	return 0;
}

bool CircleDetector::GetReferenceInputInfo(int i, CResultItem::Type &type) const
{
	return true;
}

bool CircleDetector::SetReferenceInput(int i, const CResultItem &referenceInput)
{
	return true;
}

int CircleDetector::GetNumResultItems() const
{
	return 1;
}

bool CircleDetector::GetResultItemInfo(int i, CResultItem &resultItem) const
{
	if (i != 0)
		return false;

	resultItem.SetName(L"Circle");
	resultItem.SetDescription(L"Circle");

	//CRegion::Circle circle;
	resultItem.SetRegionType(CRegion::Type_Circle);

	return true;
}

bool CircleDetector::GetResultItems(std::vector<CResultItem> &resultItems) const
{
	resultItems.clear();

	CResultItem resultItem;

	if (m_circles.size() < 1)
	{
		resultItem.SetName(L"Result");
		resultItem.SetDescription(L"");
		resultItem.SetStatus(CResultItem::Status_NG);
		resultItem.SetString(L"Error");
		resultItems.push_back(resultItem);
	}
	else
	{
		for (int i = 0; i < m_circles.size(); i++)
		{
			resultItem.SetStatus(CResultItem::Status_OK);

			CRegion region;
			const CircleDetector::Circle& circle = m_circles[i];
			CRegion::Circle regionCircle;
			regionCircle.center.x = circle.fX;
			regionCircle.center.y = circle.fY;
			regionCircle.radius = circle.fR;
			region.SetCircle(regionCircle);
			resultItem.SetRegion(region);
			resultItem.SetDescription(L"pixel");
			resultItems.push_back(resultItem);

			float fRealDiameter;
			fRealDiameter = m_param.fPixelSize * circle.fR * 2.0;
			resultItem.SetFloat(fRealDiameter);
			resultItem.SetDescription(L"diameter(mm)");
			resultItems.push_back(resultItem);

			CRegion::Point realCenterPoint;
			realCenterPoint.x = circle.fX * m_param.fPixelSize;
			realCenterPoint.y = circle.fY * m_param.fPixelSize;
			region.SetPoint(realCenterPoint);
			resultItem.SetRegion(region);
			resultItem.SetDescription(L"center(mm)");
			resultItems.push_back(resultItem);
		}


	}

	return true;
}

bool CircleDetector::GetRegistrationResult(float &x, float &y, float &angle, float &refX, float &refY, float &scale) const
{
	return false;
}

bool CircleDetector::SetRegistrationInfo(float x, float y, float angle, float refX, float refY, float scale)
{
	m_alignX = x;
	m_alignY = y;
	m_alignAngle = angle;
	m_alignRefX = refX;
	m_alignRefY = refY;
	m_alignScale = scale;
	return true;
}

bool CircleDetector::Inspect()
{
	ClearResults();

	int w = m_testImg.width();
	int h = m_testImg.height();

	// extract the target channel
	Img testImg(w, h);
	if (m_testImg.depth() == 1 || m_param.channel == Channel_Red)
		testImg.assign(m_testImg.data(), w, h);
	else if (m_param.channel == Channel_Green)
		testImg.assign(m_testImg.data() + w*h, w, h);
	else if (m_param.channel == Channel_Blue)
		testImg.assign(m_testImg.data() + w*h*2, w, h);
	else if (m_param.channel == Channel_Gray)
	{
		for (int i = 0; i < w * h; i++)
			testImg(i) = (306 * m_testImg(i) + 601 * m_testImg(i+w*h) + 117 * m_testImg(i+w*h*2) + 512)>>10;
	}

	if (m_param.bDumpImage)
	{
		SaveImg(testImg.data(), w, h, "circle_org.bmp");
	}

	// get roi
	URect roi;
	if (m_rois.size() < 1)
		roi.setRect(0, 0, w, h);
	else
	{
		roi.setRect(100000, 100000, 0, 0);
		for (int i = 0; i < m_rois.size(); i++)
		{
			CRegion &region = m_rois[i];
			if (region.GetType() == CRegion::Type_Rect)
			{
				CRegion::Rect rt;
				region.GetRect(rt);
				Template2Test(m_alignX, m_alignY, m_alignAngle, m_alignRefX, m_alignRefY, m_alignScale, rt.topLeft.x, rt.topLeft.y);
				roi |= URectF(rt.topLeft.x, rt.topLeft.y, rt.topLeft.x + rt.width, rt.topLeft.y + rt.height).toRect();
			}
		}
	}
	roi &= URect(0, 0, w, h);
	if (roi.width() < 5 || roi.height() < 5)
		return true;

	Img cropImg(roi.width(), roi.height());
	for (int i = 0; i < roi.height(); i++)
		memcpy(&cropImg(0, i), &testImg(roi.left(), roi.top() + i), sizeof(unsigned char) * roi.width());
	
	Img zoomImg;
	for (int i = 0; i < m_param.nDownSample2sqr; i++)
	{
		DownSampleImage(cropImg, zoomImg);
		cropImg.copy(zoomImg);
	}
	Detect(cropImg.data(), cropImg.width(), cropImg.height());
	// retrieve and translate the result circles
	int scale = (1<<m_param.nDownSample2sqr);
	for (int i = 0; i < m_circles.size(); i++)
	{
		Circle& circle = m_circles[i];
		circle.fR *= scale;
		circle.fX *= scale;
		circle.fY *= scale;
		circle.fX += roi.left();
		circle.fY += roi.top();
	}

	return true;
}


// fit circle using 3 points
bool CircleDetector::FitCircle3Points(float fX[], float fY[], float &fCenterX, float &fCenterY, float &fRadius)
{
	float sr1, sr2, sr3;      // square of radius
	float Y12, Y13, X12, X13;

	const float eps = 0.0001f;

	// solve equations
	Y12 = fY[0] - fY[1];
	Y13 = fY[0] - fY[2];
	X12 = fX[0] - fX[1];
	X13 = fX[0] - fX[2];

	float fCenterXScale = X12 * Y13 - X13 * Y12;
	if (fabs(fCenterXScale) < eps)
		return false;

	sr1 = fX[0]*fX[0] + fY[0]*fY[0];
	sr2 = fX[1]*fX[1] + fY[1]*fY[1];
	sr3 = fX[2]*fX[2] + fY[2]*fY[2];


	fCenterX = (sr1 - sr2) * Y13 - (sr1 - sr3) * Y12;
	fCenterX = fCenterX / fCenterXScale * 0.5f;
	if (fabs(Y12) >= eps)
		fCenterY = (float)((sr1 - sr2 - 2.0 * X12 * fCenterX) / (2.0 * Y12));
	else if (fabs(Y13) >= eps)
		fCenterY = (float)((sr1 - sr3 - 2.0 * X13 * fCenterX) / (2.0 * Y13));
	else
		return false;

	fRadius = 0;
	for (int i = 0; i < 3; i++)
	{
		fRadius += sqrtf((float)((fX[i]-fCenterX)*(fX[i]-fCenterX) + (fY[i]-fCenterY)*(fY[i]-fCenterY)));
	}
	fRadius /= 3;

	return true;
}

// Cholesky decomposition
static int CholeskyDecomposition(double a[], int n, double *det)
{ 
	int i,j,k,u,l;
	double d;
	if ((a[0]+1.0==1.0)||(a[0]<0.0))
	{ 
		/*printf("fail\n");*/ 
		return(-2);
	}
	a[0]=sqrt(a[0]);
	d=a[0];
	for (i=1; i<=n-1; i++)
	{ 
		u=i*n; a[u]=a[u]/a[0];
	}
	for (j=1; j<=n-1; j++)
	{ 
		l=j*n+j;
		for (k=0; k<=j-1; k++)
		{ 
			u=j*n+k; 
			a[l]=a[l]-a[u]*a[u];
		}
		if ((a[l]+1.0==1.0)||(a[l]<0.0))
		{ 
			/*printf("fail\n");*/ 
			return(-2);
		}
		a[l]=sqrt(a[l]);
		d=d*a[l];
		for (i=j+1; i<=n-1; i++)
		{ 
			u=i*n+j;
			for (k=0; k<=j-1; k++)
				a[u]=a[u]-a[i*n+k]*a[j*n+k];
			a[u]=a[u]/a[l];
		}
	}
	*det=d*d;
	for (i=0; i<=n-2; i++)
		for (j=i+1; j<=n-1; j++)
			a[i*n+j]=0.0;
	return(2);
}

// fit circle by least squares (minimizing algebraic distance)
bool CircleDetector::FitCircleLS(vector<float> &points, float &fCenterX, float &fCenterY, float &fRadius)
{
	if (points.size() < 3)
		return false;
	// Circle equation: x^2 + y^2 + ax + by + c = 0
	// =>Circle center: nX0 = -a/2, nY0 = -b/2
	// =>Circle radius: sqrtf(nX0^2+nY0^2-c)

	int i;
	double A[3][3], b[3];
	A[0][0] = A[0][1] = A[0][2] = 0;
	A[1][0] = A[1][1] = A[1][2] = 0;
	A[2][0] = A[2][1] = A[2][2] = 0;
	b[0] = b[1] = b[2] = 0;
	for (i = 0; i < points.size() - 1; i += 2)
	{
		double x, y, xy, x2, x3, y2, y3, x2y, xy2;
		x = points[i];
		y = points[i+1];
		xy = x * y;
		x2 = x * x;
		y2 = y * y;
		x3 = x2 * x;
		y3 = y2 * y;
		x2y = x2 * y;
		xy2 = x * y2;
		// A*x = b
		// A = [Sum_x2 Sum_xy Sum_x; Sum_xy Sum_y2 Sum_y; Sum_x Sum_y Sum_1]
		// b = [Sum_x3+Sum_xy2; Sum_x2y+Sum_y3; Sum_x2+Sum_y2]
		A[0][0] += x2;
		A[0][1] += xy;
		A[0][2] += x;
		A[1][0] += xy;
		A[1][1] += y2;
		A[1][2] += y;
		A[2][0] += x;
		A[2][1] += y;
		b[0] -= x3 + xy2;
		b[1] -= x2y + y3;
		b[2] -= x2 + y2;
	}
	A[2][2] = (double) (points.size()/2);

	// A is (1) square matrix (2) symmetric (3) and positive-definite
	// =>Cholesky decomposition: A=L*L', and L is a lower triangular matrix,
	// L' is its transpose
	double det;
	int nRet;
	nRet = CholeskyDecomposition(&A[0][0], 3, &det);
	if (nRet <= 0 || A[0][0] == 0 || A[1][1] == 0 || A[2][2] == 0)
		return false;

	// solve L*y=b
	double x[3], y[3];
	y[0] = b[0]/A[0][0];
	y[1] = (b[1] - y[0]*A[1][0])/A[1][1];
	y[2] = (b[2] - y[0]*A[2][0] - y[1]*A[2][1])/A[2][2];

	// solve L'*x=y
	x[2] = y[2]/A[2][2];
	x[1] = (y[1] - x[2]*A[2][1])/A[1][1];
	x[0] = (y[0] - x[2]*A[2][0] - x[1]*A[1][0])/A[0][0];

	fCenterX = float(-0.5 * x[0]);
	fCenterY = float(-0.5 * x[1]);
	fRadius = sqrtf(fCenterX*fCenterX + fCenterY*fCenterY - float(x[2]));

	return true;
}


// fit circle by weighted least squares (minimizing algebraic distance)
bool CircleDetector::FitCircleWLS(vector<float> &points, float &fCenterX, float &fCenterY, float &fRadius)
{
	if (points.size() < 3)
		return false;

	// Circle equation: x^2 + y^2 + ax + by + c = 0
	// =>Circle center: nX0 = -a/2, nY0 = -b/2
	// =>Circle radius: sqrtf(nX0^2+nY0^2-c)

	const float eps = 0.00001f;

	int i;
	double A[3][3], b[3];
	A[0][0] = A[0][1] = A[0][2] = 0;
	A[1][0] = A[1][1] = A[1][2] = 0;
	A[2][0] = A[2][1] = A[2][2] = 0;
	b[0] = b[1] = b[2] = 0;
	double dWSum = 0;
	for (i = 0; i < points.size() - 2; i += 3)
	{
		double x, y, xy, x2, x3, y2, y3, x2y, xy2;
		x = points[i];
		y = points[i+1];
		xy = x * y;
		x2 = x * x;
		y2 = y * y;
		x3 = x2 * x;
		y3 = y2 * y;
		x2y = x2 * y;
		xy2 = x * y2;
		// A*x = b
		// A = [Sum_x2 Sum_xy Sum_x; Sum_xy Sum_y2 Sum_y; Sum_x Sum_y Sum_1]
		// b = [Sum_x3+Sum_xy2; Sum_x2y+Sum_y3; Sum_x2+Sum_y2]
		double dW = points[i+2];

		A[0][0] += dW * x2;
		A[0][1] += dW * xy;
		A[0][2] += dW * x;
		A[1][0] += dW * xy;
		A[1][1] += dW * y2;
		A[1][2] += dW * y;
		A[2][0] += dW * x;
		A[2][1] += dW * y;
		b[0] -= dW * (x3 + xy2);
		b[1] -= dW * (x2y + y3);
		b[2] -= dW * (x2 + y2);

		dWSum += dW;
	}
	A[2][2] = (double) dWSum;

	// A is (1) square matrix (2) symmetric (3) and positive-definite
	// =>Cholesky decomposition: A=L*L', and L is a lower triangular matrix,
	// L' is its transpose
	double det;
	int nRet;
	nRet = CholeskyDecomposition(&A[0][0], 3, &det);
	if (nRet <= 0 || fabs(A[0][0]) < eps || fabs(A[1][1]) < eps || fabs(A[2][2]) < eps)
		return false;

	// solve L*y=b
	double x[3], y[3];
	y[0] = b[0]/A[0][0];
	y[1] = (b[1] - y[0]*A[1][0])/A[1][1];
	y[2] = (b[2] - y[0]*A[2][0] - y[1]*A[2][1])/A[2][2];

	// solve L'*x=y
	x[2] = y[2]/A[2][2];
	x[1] = (y[1] - x[2]*A[2][1])/A[1][1];
	x[0] = (y[0] - x[2]*A[2][0] - x[1]*A[1][0])/A[0][0];

	fCenterX = float(-0.5 * x[0]);
	fCenterY = float(-0.5 * x[1]);
	fRadius = sqrtf(fCenterX*fCenterX + fCenterY*fCenterY - float(x[2]));

	return true;
}



void CircleDetector::DownSampleImage(Img &src, Img &dst)//LYY
{
	int srcW, srcH;
	srcW = src.width();
	srcH = src.height();
	int dstW, dstH;
	dstW = srcW/2;
	dstH = srcH/2;
	dst.resize(dstW, dstH);

	int offset = 0;
	for (int j = 0; j < dstH; j++)
	{
		int y0 = j * 2;
		for (int i = 0; i < dstW; i++, offset++)
		{
			int x0 = i * 2;
			dst(offset) = (src(x0, y0) + src(x0+1, y0) + src(x0, y0+1) + src(x0+1, y0+1))>>2;
		}
	}

	return;
}

// smooth 1d array by mean filter
// src - source array
// dst - destination array
// srcStep - index nStep of elements in src
// dstStep - index nStep of elements in dst
// n - length of 1d array
// r - radius of the local neighborhood
void CircleDetector::MeanSmooth1d(const int *src, int *dst, int srcStep, int dstStep, int n, int r)
{
	int i;  
	int sum, count;
	const int *head = src;
	sum = 0;
	for (i = 0; i < r; i++, head += srcStep)
		sum += *head;

	for (i = 0; i <= r; i++, head += srcStep, dst += dstStep)
	{
		sum += *head;
		count = i + r + 1;
		*dst = (int)(sum/count);
	}
	count = r*2+1;
	const int *tail = src;
	for (; i < n - r; i++, head += srcStep, tail += srcStep, dst += dstStep)
	{
		sum += *head - *tail;
		*dst = (int)(sum/count);
	}
	for (; i < n; i++, tail += srcStep, dst += dstStep)
	{
		sum -= *tail;
		count = n - i + r;
		*dst = (int)(sum/count);
	}
}


// fit one circle to an edge
// points - edge points
// fMinRadius - min circle radius
// fMaxRadius - max circle radius
// fEpsilon - circle error ratio
// circle - found circle
// return - 0 for success
#if 0
bool CircleDetector::FitCircleRANSAC(vector<EdgePoint> &points, float fMinRadius, float fMaxRadius, 
									 float fEpsilon, Circle &circle)
{
	// init output
	circle.nScore = 0;

	// internal parameters
	const int nMinChainLength = (int)(fMinRadius + 0.5f); // min chain length: 60-degree arc
	const int nMinSampleDist = max(2, int(fMinRadius/4 + 0.5f));
	const int nMinNumFits = 32;
	const int nMaxNumRands = nMinNumFits << 3;
	const float fMinRadiusLimit = fMinRadius*0.8f;
	const float fMaxRadiusLimit = fMaxRadius*1.25f;

	// check input
	if (points.size() < nMinChainLength) 
		return false;


	vector<Circle> candiCircles;
	candiCircles.reserve(nMinNumFits);

	int nFitCount = 0;
	int nSampleDist = min(int(points.size()/4), int(fMaxRadius + 0.5f));
	srand(points.size());
	while (nSampleDist >= nMinSampleDist && nFitCount < nMinNumFits)
	{
		for (int i = 0; i < nMaxNumRands && nFitCount < nMinNumFits; i++)
		{
			// draw 3 random sample points
			float fSampleX[3], fSampleY[3];
			int nSampleIndex[3];
			nSampleIndex[0] = rand() % points.size();
			fSampleX[0] = points[nSampleIndex[0]].fSubX;
			fSampleY[0] = points[nSampleIndex[0]].fSubY;

			nSampleIndex[1] = rand() % points.size();
			fSampleX[1] = points[nSampleIndex[1]].fSubX;
			fSampleY[1] = points[nSampleIndex[1]].fSubY;

			if (abs(nSampleIndex[0] - nSampleIndex[1]) < nSampleDist)
				continue;

			nSampleIndex[2] = rand() % points.size();
			fSampleX[2] = points[nSampleIndex[2]].fSubX;
			fSampleY[2] = points[nSampleIndex[2]].fSubY;

			if (abs(nSampleIndex[0] - nSampleIndex[2]) < nSampleDist 
				|| abs(nSampleIndex[1] - nSampleIndex[2]) < nSampleDist)
				continue;

			nFitCount++;

			Circle candiCircle;
			if (!FitCircle3Points(fSampleX, fSampleY, candiCircle.fX, candiCircle.fY, candiCircle.fR))
				continue;
			if (candiCircle.fR < fMinRadiusLimit || candiCircle.fR > fMaxRadiusLimit)
				continue;

			candiCircles.push_back(candiCircle);
		}

		if (nFitCount >= nMinNumFits)
			break;

		nSampleDist >>= 1;
	}

	if (candiCircles.size() < 1)
		return false;

	// find circle with most inliers
	int nMaxInlierCount = 0;
	int nMaxInlierIndex = -1;
	float fMaxInlierWeight = - (points.size() * 1000000.0f);
	for (int i = 0; i < candiCircles.size(); i++)
	{
		Circle &circle = candiCircles[i];
		float fRadiusError = max(1.0f, circle.fR * fEpsilon);
		float fDist2Min = (circle.fR - fRadiusError) * (circle.fR - fRadiusError);
		float fDist2Max = (circle.fR + fRadiusError) * (circle.fR + fRadiusError);
		int nInlierCount = 0;
		float fInlierWeight = 0;
		for (int j = 0; j < points.size(); j++)
		{
			EdgePoint &point = points[j];

			float fDx = point.nX - circle.fX;
			float fDy = point.nY - circle.fY;
			float fDist2 = fDx * fDx + fDy * fDy;
			if (fDist2 >= fDist2Min && fDist2 <= fDist2Max)
			{
				nInlierCount++;
				fInlierWeight -= fDist2;
			}
		}

		if ((nInlierCount > nMaxInlierCount) 
			|| (nInlierCount == nMaxInlierCount && fInlierWeight > fMaxInlierWeight))
		{
			nMaxInlierCount = nInlierCount;
			fMaxInlierWeight = fInlierWeight;
			nMaxInlierIndex = i;
		}
	}

	if (nMaxInlierIndex < 0
		|| nMaxInlierCount * 2 < candiCircles[nMaxInlierIndex].fR)
		return false;

	circle.fX = candiCircles[nMaxInlierIndex].fX;
	circle.fY = candiCircles[nMaxInlierIndex].fY;
	circle.fR = candiCircles[nMaxInlierIndex].fR;

	// do weighted-least-squares fitting with inliers
	vector<float> inlierPoints;
	inlierPoints.reserve(nMaxInlierCount * 3);

	float fRadiusError = max(1.0f, circle.fR * fEpsilon);

	float fDist2Min = (circle.fR - fRadiusError) * (circle.fR - fRadiusError);
	float fDist2Max = (circle.fR + fRadiusError) * (circle.fR + fRadiusError);
	for (int i = 0; i < points.size(); i++)
	{
		EdgePoint &point = points[i];

		float fDx = point.nX - circle.fX;
		float fDy = point.nY - circle.fY;
		float fDist2 = fDx * fDx + fDy * fDy;
		if (fDist2 >= fDist2Min && fDist2 <= fDist2Max)
		{
			inlierPoints.push_back(point.fSubX);
			inlierPoints.push_back(point.fSubY);
			inlierPoints.push_back(float(point.nMag));
		}
	}
	float fWlsX, fWlsY, fWlsR;
	if (FitCircleWLS(inlierPoints, fWlsX, fWlsY, fWlsR))
	{
		circle.fX = fWlsX;
		circle.fY = fWlsY;
		circle.fR = fWlsR;
	}

	return (circle.fR >= fMinRadius && circle.fR <= fMaxRadius);
}
#endif

bool CircleDetector::FitCircleRANSAC(vector<EdgePoint> &points, float fMinRadius, float fMaxRadius, 
									 float fEpsilon, Circle &circle)
{
	// init output
	circle.nScore = 0;

	// internal parameters
	const int nMinChainLength = (int)(fMinRadius/2 + 0.5f); // min chain length: 30-degree arc
	const int nMinSampleDist = max(2, int(fMinRadius/8 + 0.5f));
	const int nMinNumFits = 64;
	const int nMaxNumRands = nMinNumFits << 4;
	const float fMinRadiusLimit = fMinRadius*0.8f;
	const float fMaxRadiusLimit = fMaxRadius*1.25f;

	// check input
	if (points.size() < nMinChainLength) 
		return false;


	vector<Circle> candiCircles;
	candiCircles.reserve(nMinNumFits);

	int nFitCount = 0;
	int nSampleDist = min(int(points.size()/4), int(fMaxRadius + 0.5f));
	srand(points.size());
	while (nSampleDist >= nMinSampleDist && nFitCount < nMinNumFits)
	{
		for (int i = 0; i < nMaxNumRands && nFitCount < nMinNumFits; i++)
		{
			// draw 3 random sample points
			float fSampleX[3], fSampleY[3];
			int nSampleIndex[3];
			nSampleIndex[0] = rand() % points.size();
			fSampleX[0] = points[nSampleIndex[0]].fSubX;
			fSampleY[0] = points[nSampleIndex[0]].fSubY;

			nSampleIndex[1] = rand() % points.size();
			fSampleX[1] = points[nSampleIndex[1]].fSubX;
			fSampleY[1] = points[nSampleIndex[1]].fSubY;

			if (abs(nSampleIndex[0] - nSampleIndex[1]) < nSampleDist)
				continue;

			nSampleIndex[2] = rand() % points.size();
			fSampleX[2] = points[nSampleIndex[2]].fSubX;
			fSampleY[2] = points[nSampleIndex[2]].fSubY;

			if (abs(nSampleIndex[0] - nSampleIndex[2]) < nSampleDist 
				|| abs(nSampleIndex[1] - nSampleIndex[2]) < nSampleDist)
				continue;

			nFitCount++;

			Circle candiCircle;
			if (!FitCircle3Points(fSampleX, fSampleY, candiCircle.fX, candiCircle.fY, candiCircle.fR))
				continue;
			if (candiCircle.fR < fMinRadiusLimit || candiCircle.fR > fMaxRadiusLimit)
				continue;

			candiCircles.push_back(candiCircle);
		}

		if (nFitCount >= nMinNumFits)
			break;

		nSampleDist >>= 1;
	}

	if (candiCircles.size() < 1)
		return false;

	// find circle with most inliers
	int nMaxInlierCount = 0;
	int nMaxInlierIndex = -1;
	float fMaxInlierWeight = - (points.size() * 1000000.0f);
	for (int i = 0; i < candiCircles.size(); i++)
	{
		Circle &circle = candiCircles[i];
		float fRadiusError = max(1.0f, circle.fR * fEpsilon);
		float fDist2Min = (circle.fR - fRadiusError) * (circle.fR - fRadiusError);
		float fDist2Max = (circle.fR + fRadiusError) * (circle.fR + fRadiusError);
		int nInlierCount = 0;
		float fInlierWeight = 0;
		for (int j = 0; j < points.size(); j++)
		{
			EdgePoint &point = points[j];

			float fDx = point.nX - circle.fX;
			float fDy = point.nY - circle.fY;
			float fDist2 = fDx * fDx + fDy * fDy;
			if (fDist2 >= fDist2Min && fDist2 <= fDist2Max)
			{
				nInlierCount++;
				fInlierWeight -= fDist2;
			}
		}

		if ((nInlierCount > nMaxInlierCount) 
			|| (nInlierCount == nMaxInlierCount && fInlierWeight > fMaxInlierWeight))
		{
			nMaxInlierCount = nInlierCount;
			fMaxInlierWeight = fInlierWeight;
			nMaxInlierIndex = i;
		}
	}

	if (nMaxInlierIndex < 0
		|| nMaxInlierCount * 3 < candiCircles[nMaxInlierIndex].fR)
		return false;

	circle.fX = candiCircles[nMaxInlierIndex].fX;
	circle.fY = candiCircles[nMaxInlierIndex].fY;
	circle.fR = candiCircles[nMaxInlierIndex].fR;

	// do weighted-least-squares fitting with inliers
	vector<float> inlierPoints;
	inlierPoints.reserve(nMaxInlierCount * 3);

	float fRadiusError = max(1.0f, circle.fR * fEpsilon);

	float fDist2Min = (circle.fR - fRadiusError) * (circle.fR - fRadiusError);
	float fDist2Max = (circle.fR + fRadiusError) * (circle.fR + fRadiusError);
	for (int i = 0; i < points.size(); i++)
	{
		EdgePoint &point = points[i];

		float fDx = point.nX - circle.fX;
		float fDy = point.nY - circle.fY;
		float fDist2 = fDx * fDx + fDy * fDy;
		if (fDist2 >= fDist2Min && fDist2 <= fDist2Max)
		{
			inlierPoints.push_back(point.fSubX);
			inlierPoints.push_back(point.fSubY);
			inlierPoints.push_back(float(point.nMag));
		}
	}
	float fWlsX, fWlsY, fWlsR;
	if (FitCircleWLS(inlierPoints, fWlsX, fWlsY, fWlsR))
	{
		circle.fX = fWlsX;
		circle.fY = fWlsY;
		circle.fR = fWlsR;
	}

	return (circle.fR >= fMinRadius && circle.fR <= fMaxRadius);
}


void CircleDetector::Detect(unsigned char *pImg, int nImgWidth, int nImgHeight)
{
	// init output
	m_circles.clear();
	if (nImgWidth < 5 || nImgHeight < 5)
		return;
	float fDownSpl = (1<<m_param.nDownSample2sqr);
	int nLowEdgeTh = m_param.nEdgeTh;
	int nHighEdgeTh = nLowEdgeTh * 2;
	int nMinEdgeLen = 8; 

	unsigned char *pMagImg, *pDirImg, *pNmsImg;
	pMagImg = new unsigned char[nImgWidth * nImgHeight * 3];
	pDirImg = pMagImg + nImgWidth * nImgHeight;
	pNmsImg = pDirImg + nImgWidth * nImgHeight;
	
	BoxSmooth(pImg, nImgWidth, nImgHeight, m_param.smoothRadius*2+1, m_param.smoothRadius*2+1);
	// compute gradient
	ComputeGradient(pImg, pMagImg, pDirImg, nImgWidth, nImgHeight, nLowEdgeTh);
	
	// suppress non maximum
	SuppressNonMaximum(pMagImg, pDirImg, pNmsImg, nImgWidth, nImgHeight);
	
	// link edges
	LinkEdges(pNmsImg, pDirImg, nImgWidth, nImgHeight, nLowEdgeTh, nHighEdgeTh, nMinEdgeLen, 32, m_edgeChains);
	
	// get sub-pixel edges
	GetSubpixelEdge(pMagImg, pDirImg, nImgWidth, nImgHeight, m_edgeChains);

	if (m_param.bDumpImage)
	{
		SaveImg(pImg, nImgWidth, nImgHeight, "circle_img.bmp");
		SaveImg(pMagImg, nImgWidth, nImgHeight, "circle_edge.bmp");
		SaveImg(pNmsImg, nImgWidth, nImgHeight, "circle_Nms.bmp");
		unsigned char *pChainImg = new unsigned char[nImgWidth * nImgHeight];
		DumpEdgeChains(pChainImg, nImgWidth, nImgHeight, m_edgeChains);
		SaveImg(pChainImg, nImgWidth, nImgHeight, "circle_chain.bmp");
		delete [] pChainImg;
	}

	float fMinRadius, fMaxRadius;
	fMinRadius = m_param.fRealMinDiameter/m_param.fPixelSize/2.0;
	fMaxRadius = m_param.fRealMaxDiameter/m_param.fPixelSize/2.0;
	
	// fit ONE circle to each edge
	for (size_t i = 0; i < m_edgeChains.size(); i++)
	{
		Circle circle;
		// find circle by RANSAC fitting
		if (FitCircleRANSAC(m_edgeChains[i].points, fMinRadius/fDownSpl, fMaxRadius/fDownSpl, m_param.fEpsilon/fDownSpl, circle))
		{
            GetValidCirclePoints(pNmsImg, pDirImg, nImgWidth, nImgHeight, m_param.ePolarity, m_param.fEpsilon/fDownSpl, circle,
                  m_edgeChains[i].points);

            if (circle.nScore < m_param.nMinScore || circle.fR < fMinRadius/fDownSpl || circle.fR > fMaxRadius/fDownSpl)
            {
                continue;
            }

            // fine match, find more precise circle
            GetSubpixelEdge(pMagImg, pDirImg, nImgWidth, nImgHeight, m_edgeChains[i].points);
            int nBackScore = circle.nScore;
            float fFineMinR = max(fMinRadius/fDownSpl, circle.fR*99/100);
            float fFineMaxR = min(fMaxRadius/fDownSpl, circle.fR*101/100);
            FitCircleRANSAC(m_edgeChains[i].points, fFineMinR, fFineMaxR, m_param.fEpsilon/fDownSpl, circle);
            circle.nScore = nBackScore;

            if (circle.nScore >= m_param.nMinScore && circle.fR >= fMinRadius/fDownSpl && circle.fR <= fMaxRadius/fDownSpl)
            {
                m_circles.push_back(circle);
            }
		}
	}

	// post processing
	if (m_param.nMaxCircles > 0)
	{
		RemoveWeakCircles(m_circles, m_param.nMaxCircles);
	}

    // refine circles
    if (m_circles.size() < 1)   // try to find circle by two points
    {
       NULL;
    }

	delete [] pMagImg;
	return;
}

void CircleDetector::DetectHough(unsigned char *pImg, int nImgWidth, int nImgHeight)
{
	if (nImgWidth < 5 || nImgHeight < 5)
		return;
	float fMinRadius, fMaxRadius;
	fMinRadius = m_param.fRealMinDiameter / m_param.fPixelSize / 2.0;
	fMaxRadius = m_param.fRealMaxDiameter / m_param.fPixelSize / 2.0;
	cv::Mat img = cv::Mat(nImgHeight, nImgWidth, CV_8UC1, pImg);
	vector<cv::Vec3f> circles;
	cv::HoughCircles(img, circles, cv::HOUGH_GRADIENT, 1, nImgHeight/8, 128, 20, fMinRadius, fMaxRadius);
	m_circles.clear();
	for(int i = 0;i<circles.size();i++)
	{
		Circle circle;
		circle.fX = circles[i][0];
		circle.fY = circles[i][1];
		circle.fR = circles[i][2];
		m_circles.push_back(circle);
	}

}

//void CircleDetector::DownSampleDetect(unsigned char *pImg, int nImgWidth, int nImgHeight)
//{
//	// init output
//	m_circles.clear();
//
//	if (nImgWidth < 5 || nImgHeight < 5)
//		return;
//
//	int nLowEdgeTh = m_param.nEdgeTh;
//	int nHighEdgeTh = nLowEdgeTh * 2;
//	int nMinEdgeLen = 8/2; 
//
//	unsigned char *pMagImg, *pDirImg, *pNmsImg;
//	pMagImg = new unsigned char[nImgWidth * nImgHeight * 3];
//	pDirImg = pMagImg + nImgWidth * nImgHeight;
//	pNmsImg = pDirImg + nImgWidth * nImgHeight;
//
//	// compute gradient
//	ComputeGradient(pImg, pMagImg, pDirImg, nImgWidth, nImgHeight, nLowEdgeTh);
//	// suppress non maximum
//	SuppressNonMaximum(pMagImg, pDirImg, pNmsImg, nImgWidth, nImgHeight);
//	// link edges
//	LinkEdges(pNmsImg, pDirImg, nImgWidth, nImgHeight, nLowEdgeTh, nHighEdgeTh, nMinEdgeLen, 32, m_edgeChains);
//	// get sub-pixel edges
//	GetSubpixelEdge(pMagImg, pDirImg, nImgWidth, nImgHeight, m_edgeChains);
//
//	if (m_param.bDumpImage)
//	{
//		SaveImg(pImg, nImgWidth, nImgHeight, "circle_img.bmp");
//		unsigned char *pChainImg = new unsigned char[nImgWidth * nImgHeight];
//		DumpEdgeChains(pChainImg, nImgWidth, nImgHeight, m_edgeChains);
//		SaveImg(pChainImg, nImgWidth, nImgHeight, "circle_chain.bmp");
//		delete [] pChainImg;
//	}
//
//	// fit ONE circle to each edge
//	for (size_t i = 0; i < m_edgeChains.size(); i++)
//	{
//		Circle circle;
//		// find circle by RANSAC fitting
//		if (FitCircleRANSAC(m_edgeChains[i].points, m_param.fMinRadius/2, m_param.fMaxRadius/2, m_param.fEpsilon/2, circle))
//		{
//			// verify the circle
//			if (m_param.ePolarity != kPolarityAny)
//			{
//				VerifyCircle(pNmsImg, pDirImg, nImgWidth, nImgHeight, m_param.ePolarity, m_param.fEpsilon/2, circle);
//				if (circle.nScore >= m_param.nMinScore && circle.fR >= m_param.fMinRadius/2 && circle.fR <= m_param.fMaxRadius/2)
//				{
//					m_circles.push_back(circle);
//				}
//			}
//			else
//			{
//				VerifyCircle(pNmsImg, pDirImg, nImgWidth, nImgHeight, kPolarityBright, m_param.fEpsilon/2, circle);
//				if (circle.nScore >= m_param.nMinScore && circle.fR >= m_param.fMinRadius/2 && circle.fR <= m_param.fMaxRadius/2)
//				{
//					m_circles.push_back(circle);
//				}
//				VerifyCircle(pNmsImg, pDirImg, nImgWidth, nImgHeight, kPolarityDark, m_param.fEpsilon/2, circle);
//				if (circle.nScore >= m_param.nMinScore && circle.fR >= m_param.fMinRadius/2 && circle.fR <= m_param.fMaxRadius/2)
//				{
//					m_circles.push_back(circle);
//				}
//			}
//
//		}
//	}
//	for (int i = 0; i < m_circles.size(); i++)
//	{
//		Circle& circle = m_circles[i];
//		circle.fR *= 2;
//		circle.fX *= 2;
//		circle.fY *= 2;
//	}
//
//	// post processing
//	if (m_param.nMaxCircles > 0)
//	{
//		RemoveWeakCircles(m_circles, m_param.nMaxCircles);
//	}
//
//	delete [] pMagImg;
//	return;
//}



// get n largest circles
void CircleDetector::RemoveWeakCircles(vector<Circle> &circles, int n)
{
	int N = min(n, int(circles.size()));
	for (int i = 0; i < N; i++)
	{
		Circle &circle1 = circles[i];
		for (int j = i + 1; j < circles.size(); j++)
		{
			Circle &circle2 = circles[j];
			if (circle1.nScore < circle2.nScore)
			{
				Circle t;
				t = circle1;
				circle1 = circle2;
				circle2 = t;
			}
		}
	}

	if (N < circles.size())
	{
		circles.erase(circles.begin() + N, circles.end());
	}
	return;
}

inline unsigned char DirDiff(unsigned char dir1, unsigned char dir2)
{
	int diff = abs(dir1 - dir2);
	diff = min(diff, 256 - diff);
	return (unsigned char)diff;
}

inline unsigned char DirReverse(unsigned char dir)
{
	int dir2 = dir + 128;
	if (dir2 >= 256)
		dir2 -= 256;
	return (unsigned char)dir2;
}

#if 0
// verifyCircle circle
// magImg - gradient magnitude
// dirImg - gradient direction
// ePolarity - bright/dark circle
// fEpsilon - radius error ratio
// circle - found circle
void CircleDetector::VerifyCircle(unsigned char *pMagImg, unsigned char *pDirImg, int nImgWidth, int nImgHeight, 
								  Polarity ePolarity, float fEpsilon, Circle &circle)
{
	// internal parameters
	const int nMaxRadiusError = max(1, int(circle.fR * fEpsilon + 0.5f));
	const int nMaxAngleError = 256>>4;


	const int offset[9][2] = {{1, 0}, {1, 1}, {0, 1}, {-1, 1}, {-1, 0}, {-1, -1}, {0, -1}, {1, -1}, {1, 0}};

	int nAngleSteps = int(2 * PI * circle.fR + 0.5f); // TODO: 
	float fAngleStep = 2 * (float)PI / nAngleSteps;
	const float fRadian2uchar = 128/(float)PI;

	int nMatchCount = 0;
	float fAngle = 0;
	for (int nStep = 0; nStep < nAngleSteps; nStep++, fAngle += fAngleStep)
	{
		int nX0 = int(circle.fX + circle.fR * cos(fAngle) + 0.5f);
		int nY0 = int(circle.fY + circle.fR * sin(fAngle) + 0.5f);
		if ((unsigned)nX0 >= (unsigned)nImgWidth || (unsigned)nY0 >= (unsigned)nImgHeight)
			continue;

		unsigned char ucDir, ucDir0;
		ucDir0 = int(fAngle * fRadian2uchar + 0.5f);
		if (ePolarity == kPolarityBright)
			ucDir0 = DirReverse(ucDir0);
		if (pMagImg[nY0 * nImgWidth + nX0] > 0)
		{
			ucDir = pDirImg[nY0 * nImgWidth + nX0];
			if ((DirDiff(ucDir, ucDir0) <= nMaxAngleError)
				|| (ePolarity == kPolarityAny && DirDiff(DirReverse(ucDir), ucDir0) <= nMaxAngleError))
			{
				nMatchCount++;
				continue;
			}
		}

		unsigned char ucBin = (ucDir0 + 16)/32;
		int nStepX, nStepY;
		nStepX = offset[ucBin][0];
		nStepY = offset[ucBin][1];
		for (int k = 1; k <= nMaxRadiusError; k++)
		{
			int nDx = nStepX * k;
			int nDy = nStepY * k;

			int nX = nX0 + nDx;
			int nY = nY0 + nDy;
			if ((unsigned)nX >= (unsigned)nImgWidth || (unsigned)nY >= (unsigned)nImgHeight)
				continue;
			if (pMagImg[nY * nImgWidth + nX] > 0)
			{
				ucDir = pDirImg[nY * nImgWidth + nX];
				if ((DirDiff(ucDir, ucDir0) <= nMaxAngleError)
					|| (ePolarity == kPolarityAny && DirDiff(DirReverse(ucDir), ucDir0) <= nMaxAngleError))				{
					nMatchCount++;
					break;
				}
			}

			nX = nX0 - nDx;
			nY = nY0 - nDy;
			if ((unsigned)nX >= (unsigned)nImgWidth || (unsigned)nY >= (unsigned)nImgHeight)
				continue;
			if (pMagImg[nY * nImgWidth + nX] > 0)
			{
				ucDir = pDirImg[nY * nImgWidth + nX];
				if ((DirDiff(ucDir, ucDir0) <= nMaxAngleError)
					|| (ePolarity == kPolarityAny && DirDiff(DirReverse(ucDir), ucDir0) <= nMaxAngleError))				{
						nMatchCount++;
						break;
				}
			}

		}

	}

	circle.nScore = nMatchCount * 100 / nAngleSteps;

	if (ePolarity != kPolarityAny)
		circle.ePolarity = ePolarity;
	else
	{

	}

	return;
}

#endif


// verifyCircle circle
// magImg - gradient magnitude
// dirImg - gradient direction
// ePolarity - bright/dark circle
// fEpsilon - radius error ratio
// circle - found circle

void CircleDetector::VerifyCircle(unsigned char *pMagImg, unsigned char *pDirImg, int nImgWidth, int nImgHeight, 
								  Polarity ePolarity, float fEpsilon, Circle &circle)
{
	// internal parameters
	const int nMaxRadiusError = max(1, int(circle.fR * fEpsilon + 0.5f));
	const int nMaxAngleError = 256>>4;


	const int offset[9][2] = {{1, 0}, {1, 1}, {0, 1}, {-1, 1}, {-1, 0}, {-1, -1}, {0, -1}, {1, -1}, {1, 0}};

	int nAngleSteps = int(2 * PI * circle.fR + 0.5f); // TODO: 
	float fAngleStep = 2 * (float)PI / nAngleSteps;
	const float fRadian2uchar = 128/(float)PI;

	int nMatchCount = 0;
	float fAngle = 0;
	for (int nStep = 0; nStep < nAngleSteps; nStep++, fAngle += fAngleStep)
	{
		int nX0 = int(circle.fX + circle.fR * cos(fAngle) + 0.5f);
		int nY0 = int(circle.fY + circle.fR * sin(fAngle) + 0.5f);
		if ((unsigned)nX0 >= (unsigned)nImgWidth || (unsigned)nY0 >= (unsigned)nImgHeight)
			continue;

		unsigned char ucDir, ucDir0;
		ucDir0 = int(fAngle * fRadian2uchar + 0.5f);
		if (ePolarity == kPolarityBright)
			ucDir0 = DirReverse(ucDir0);
		if (pMagImg[nY0 * nImgWidth + nX0] > 0)
		{
			ucDir = pDirImg[nY0 * nImgWidth + nX0];
			if (DirDiff(ucDir, ucDir0) <= nMaxAngleError)
			{
				nMatchCount++;
				continue;
			}
		}

		unsigned char ucBin = (ucDir0 + 16)/32;
		int nStepX, nStepY;
		nStepX = offset[ucBin][0];
		nStepY = offset[ucBin][1];
		for (int k = 1; k <= nMaxRadiusError; k++)
		{
			int nDx = nStepX * k;
			int nDy = nStepY * k;

			int nX = nX0 + nDx;
			int nY = nY0 + nDy;
			if ((unsigned)nX >= (unsigned)nImgWidth || (unsigned)nY >= (unsigned)nImgHeight)
				continue;
			if (pMagImg[nY * nImgWidth + nX] > 0)
			{
				ucDir = pDirImg[nY * nImgWidth + nX];
				if (DirDiff(ucDir, ucDir0) <= nMaxAngleError)
				{
					nMatchCount++;
					break;
				}
			}

			nX = nX0 - nDx;
			nY = nY0 - nDy;
			if ((unsigned)nX >= (unsigned)nImgWidth || (unsigned)nY >= (unsigned)nImgHeight)
				continue;
			if (pMagImg[nY * nImgWidth + nX] > 0)
			{
				ucDir = pDirImg[nY * nImgWidth + nX];
				if (DirDiff(ucDir, ucDir0) <= nMaxAngleError)
				{
					nMatchCount++;
					break;
				}
			}
		}
	}

	circle.nScore = nMatchCount * 100 / nAngleSteps;
	circle.ePolarity = ePolarity;

	return;
}

// verifyCircle circle
// magImg - gradient magnitude
// dirImg - gradient direction
// ePolarity - bright/dark circle
// fEpsilon - radius error ratio
// circle - found circle

void CircleDetector::GetValidCirclePoints(unsigned char *pNmsImg, unsigned char *pDirImg, int nImgWidth, int nImgHeight, 
                                  Polarity ePolarity, float fEpsilon, Circle &circle, vector<EdgePoint> &points)
{
    // internal parameters
    const int nMaxRadiusError = max(5, int(circle.fR * fEpsilon + 0.5f));
    const int nMaxAngleError = 256>>3;

    int nAngleSteps = int(2 * PI * circle.fR + 0.5f); // TODO: 
    float fAngleStep = 2 * (float)PI / nAngleSteps;
    const float fRadian2uchar = 128/(float)PI;

    int nMatchCount = 0;
    float fAngle = 0;
    points.clear();
	int show[3000] = { 0 };
    // find circle points
    for (int nStep = 0; nStep < nAngleSteps; nStep++, fAngle += fAngleStep)
    {
        int nX0 = int(circle.fX + circle.fR * cos(fAngle) + 0.5f);
        int nY0 = int(circle.fY + circle.fR * sin(fAngle) + 0.5f);

        if ((unsigned)nX0 >= (unsigned)nImgWidth || (unsigned)nY0 >= (unsigned)nImgHeight)
            continue;

        unsigned char ucDir, ucDir0;
        ucDir0 = int(fAngle * fRadian2uchar + 0.5f);
        if (ePolarity == kPolarityBright)
            ucDir0 = DirReverse(ucDir0);
		
        int nBestX = 0, nBestY = 0, nMaxEdge = 0;
        for (int nDeltaR = 1; nDeltaR <= nMaxRadiusError*2+1; nDeltaR++)
        {
            int nDelta = nDeltaR/2;
            if (nDelta*2 < nDeltaR)
                nDelta = -nDelta;
            int nNewX = int(circle.fX + (circle.fR+nDelta) * cos(fAngle));
            int nNewY = int(circle.fY + (circle.fR+nDelta) * sin(fAngle));

            if ((unsigned)nNewX >= (unsigned)nImgWidth-1 || (unsigned)nNewY >= (unsigned)nImgHeight-1)
                continue;

			int nX[4] = {nNewX, nNewX, nNewX+1, nNewX+1};
			int nY[4] = {nNewY, nNewY+1, nNewY, nNewY+1};
			for (int k = 0; k < 4; k++)
			{
				if (pNmsImg[nY[k] * nImgWidth + nX[k]] > nMaxEdge)
				{
					nMaxEdge = pNmsImg[nY[k] * nImgWidth + nX[k]];
					nBestX = nX[k];
					nBestY = nY[k];
					break;
				}
			}
			if (nMaxEdge > 0)
				break;
        }

        if (nMaxEdge <= 0)
            continue;

        // check dir
        ucDir = pDirImg[nBestY * nImgWidth + nBestX];
		show[nStep] = ucDir;
        if (DirDiff(ucDir, ucDir0) > nMaxAngleError)
        {
            if (ePolarity == kPolarityAny)
            {
                ucDir = DirReverse(ucDir);
                if (DirDiff(ucDir, ucDir0) > nMaxAngleError)
                {
                    continue;
                }
            }
            else
            {
                continue;
            }
        }
        
        // record points
        EdgePoint pt;
        pt.nX = nBestX;
        pt.nY = nBestY;
        pt.nDir = ucDir;
        pt.nMag = nMaxEdge;
		points.push_back(pt);
        nMatchCount ++;       
    }

    circle.nScore = nMatchCount * 100 / nAngleSteps;
    circle.ePolarity = ePolarity;

    return;
}

bool CircleDetector::SetTestImage( unsigned char *img, int imgWidth, int imgHeight, int bytesPerLine, int bitsPerPixel )
{
	if (img == NULL || imgWidth <= 0 || imgHeight <= 0 || (bitsPerPixel != 8 && bitsPerPixel != 24 && bitsPerPixel != 32))
		return false;

	int bytesPerPixel = bitsPerPixel/8;
	m_testImg.resize(imgWidth, imgHeight, bytesPerPixel);


	if (bytesPerPixel == 1)
	{
		for (int y = 0, lineOffset = 0; y < imgHeight; y++, lineOffset += bytesPerLine)
			memcpy(&m_testImg(0, y), img + lineOffset, sizeof(unsigned char) * imgWidth);
	}
	else
	{
		for (int y = 0, lineOffset = 0; y < imgHeight; y++, lineOffset += bytesPerLine)
		{
			for (int x = 0, pixelOffset = lineOffset; x < imgWidth; x++, pixelOffset += bytesPerPixel)
			{
				m_testImg(x, y, 0) = img[pixelOffset];
				m_testImg(x, y, 1) = img[pixelOffset + 1];
				m_testImg(x, y, 2) = img[pixelOffset + 2];
			}
		}
	}

	return true;
}



extern "C" __declspec(dllexport) void __cdecl EnumDllInspectors(std::vector<std::wstring> &names, std::vector<std::wstring> &descriptions)
{
	names.clear();
	descriptions.clear();

	std::wstring name, description;
	CircleDetector inspector;
	inspector.GetNameAndDescription(name, description);

	names.push_back(name);
	descriptions.push_back(description);
}

extern "C" __declspec(dllexport) CBaseInspector* __cdecl CreateDllInspector(const std::wstring &inspectorName)
{
	std::wstring name, description;

	CircleDetector inspector;
	inspector.GetNameAndDescription(name, description);

	if (inspectorName.compare(name) == 0)
	{
		return new CircleDetector;
	}

	return NULL;
}

extern "C" __declspec(dllexport) void __cdecl DestroyDllInspector(CBaseInspector *inspector)
{
	if (inspector != 0)
		delete inspector;
}

