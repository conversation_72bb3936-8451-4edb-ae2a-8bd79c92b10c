/**
 * OpenCV.js Precision Test
 * Compare OpenCV.js segmentation with simple algorithm
 */

import { Tmap6Parser } from './src/parsers/tmap6-parser.js';
import sharp from 'sharp';

async function testSegmentationPrecision() {
  console.log('🔬 OpenCV.js Precision Test');
  console.log('═'.repeat(50));

  const parser = new Tmap6Parser();
  const filePath = 'E:\\TMAP\\Test_1.TMAP';

  try {
    // Parse the file
    console.log('📁 Parsing TMAP file...');
    await parser.parseFile(filePath);

    // Get macro-label image for reference
    const macroLabelData = await parser.getMacroLabelImage(false);
    if (!macroLabelData) {
      throw new Error('Failed to get macro-label image');
    }

    const macroLabelMetadata = await sharp(macroLabelData).metadata();
    console.log(`📐 Macro-Label dimensions: ${macroLabelMetadata.width}x${macroLabelMetadata.height}`);

    // Extract label and macro images
    const labelData = await parser.getLabelImage(false);
    const macroData = await parser.getMacroImage(false);

    if (!labelData || !macroData) {
      throw new Error('Failed to extract label or macro image');
    }

    // Get dimensions
    const labelMetadata = await sharp(labelData).metadata();
    const macroMetadata = await sharp(macroData).metadata();

    console.log('\n📊 Segmentation Results:');
    console.log('─'.repeat(30));
    console.log(`Label region:  ${labelMetadata.width}x${labelMetadata.height}`);
    console.log(`Macro region:  ${macroMetadata.width}x${macroMetadata.height}`);
    console.log(`Total width:   ${labelMetadata.width + macroMetadata.width}`);
    console.log(`Original width: ${macroLabelMetadata.width}`);

    // Calculate precision
    const totalExtracted = labelMetadata.width + macroMetadata.width;
    const precision = (totalExtracted / macroLabelMetadata.width) * 100;
    const pixelDifference = Math.abs(totalExtracted - macroLabelMetadata.width);

    console.log('\n🎯 Precision Analysis:');
    console.log('─'.repeat(30));
    console.log(`Pixel difference: ${pixelDifference} pixels`);
    console.log(`Coverage precision: ${precision.toFixed(2)}%`);
    console.log(`Boundary accuracy: ${pixelDifference <= 2 ? '✅ Excellent' : pixelDifference <= 5 ? '✅ Good' : '⚠️ Needs improvement'}`);

    // Calculate file sizes
    const labelSize = (labelData.length / 1024).toFixed(1);
    const macroSize = (macroData.length / 1024).toFixed(1);
    const totalSize = (labelData.length + macroData.length) / 1024;

    console.log('\n💾 File Size Analysis:');
    console.log('─'.repeat(30));
    console.log(`Label image: ${labelSize} kB`);
    console.log(`Macro image: ${macroSize} kB`);
    console.log(`Total extracted: ${totalSize.toFixed(1)} kB`);

    // Quality assessment
    console.log('\n🏆 Quality Assessment:');
    console.log('─'.repeat(30));
    
    if (pixelDifference === 0) {
      console.log('🌟 Perfect segmentation - pixel-perfect boundary detection!');
    } else if (pixelDifference <= 2) {
      console.log('🎯 Excellent segmentation - within 2 pixels of perfect!');
    } else if (pixelDifference <= 5) {
      console.log('✅ Good segmentation - within acceptable range');
    } else {
      console.log('⚠️ Segmentation needs improvement');
    }

    // Performance comparison
    const startTime = Date.now();
    await parser.getLabelImage(false);
    await parser.getMacroImage(false);
    const endTime = Date.now();
    const extractionTime = endTime - startTime;

    console.log('\n⚡ Performance Metrics:');
    console.log('─'.repeat(30));
    console.log(`Extraction time: ${extractionTime}ms`);
    console.log(`Processing speed: ${(totalSize / (extractionTime / 1000)).toFixed(2)} kB/s`);

    console.log('\n✨ Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testSegmentationPrecision().catch(console.error);
