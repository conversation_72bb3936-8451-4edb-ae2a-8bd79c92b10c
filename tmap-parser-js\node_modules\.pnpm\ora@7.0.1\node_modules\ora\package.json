{"name": "ora", "version": "7.0.1", "description": "Elegant terminal spinner", "license": "MIT", "repository": "sindresorhus/ora", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=16"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "dependencies": {"chalk": "^5.3.0", "cli-cursor": "^4.0.0", "cli-spinners": "^2.9.0", "is-interactive": "^2.0.0", "is-unicode-supported": "^1.3.0", "log-symbols": "^5.1.0", "stdin-discarder": "^0.1.0", "string-width": "^6.1.0", "strip-ansi": "^7.1.0"}, "devDependencies": {"@types/node": "^20.4.5", "ava": "^5.3.1", "get-stream": "^7.0.1", "transform-tty": "^1.0.11", "tsd": "^0.28.1", "xo": "^0.55.0"}, "xo": {"rules": {"@typescript-eslint/no-redundant-type-constituents": "off"}}}