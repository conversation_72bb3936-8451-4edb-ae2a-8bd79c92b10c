// Generic algorithm interface
// Copyright (C) UNIC Technologies. All rights reserved.
// History:
// (1) 20131028: <NAME_EMAIL>

#ifndef ALG_INTERFACE_H
#define ALG_INTERFACE_H

#include <vector>
#include <string>
#include <climits> // INT_MIN/MAX
#include <cfloat> // FLT_MIN/MAX, DBL_MIN/MAX
#include <cmath>

class CRegion
{
public:
	enum Type
	{
		Type_Invalid = 0,
		Type_Point = 0x01,
		Type_Line = 0x02,
		Type_Rect = 0x04,
		Type_RotatedRect = 0x08,
		Type_Circle = 0x10,
		Type_Ellipse = 0x20,
		Type_RotatedEllipse = 0x40,
		Type_CircleArc = 0x80,
		Type_EllipseArc = 0x100,
		Type_RotatedEllipseArc = 0x200,
		Type_Polygon = 0x400, // closed
		Type_Polyline = 0x800 // open
	};

	struct Point { float x, y; };
	struct Line { Point p1, p2; };
	struct Rect { Point topLeft; float width, height; };
	struct RotatedRect { Point topLeft; float width, height, angle; };
	struct Circle { Point center; float radius; };
	struct Ellipse { Point center; float majorRadius, minorRadius; };
	struct RotatedEllipse { Point center; float majorRadius, minorRadius, angle; };
	struct CircleArc { Point center; float radius, startAngle, endAngle; };
	struct EllipseArc { Point center; float majorRadius, minorRadius, startAngle, endAngle; };
	struct RotatedEllipseArc { Point center; float majorRadius, minorRadius, angle, startAngle, endAngle; };

	CRegion(Type type = Type_Invalid, int usage = 0, int imageId = 0) { m_type = type; m_usage = usage; m_imageId = imageId; }

	void SetType(Type type) { m_type = type; }
	Type GetType() const { return m_type; }

	void SetUsage(int usage) { m_usage = usage; }
	int GetUsage() const { return m_usage; }

	void SetImageId(int imageId) { m_imageId = imageId; }
	int GetImageId() const { return m_imageId; }

	// point
	void SetPoint(const Point &p) { m_type = Type_Point; m_data.point = p; }
	void GetPoint(Point &p) const { p = m_data.point; }

	// line
	void SetLine(const Line &line) { m_type = Type_Line; m_data.line = line; }
	void GetLine(Line &line) const { line = m_data.line; }

	// rectangle
	void SetRect(const Rect &rect) { m_type = Type_Rect; m_data.rect = rect; }
	void GetRect(Rect &rect) const { rect = m_data.rect; }

	// rotated rectangle
	void SetRotatedRect(const RotatedRect &rotatedRect) { m_type = Type_RotatedRect; m_data.rotatedRect = rotatedRect; }
	void GetRotatedRect(RotatedRect &rotatedRect) const { rotatedRect = m_data.rotatedRect; }

	// circle
	void SetCircle(const Circle &circle) { m_type = Type_Circle; m_data.circle = circle; }
	void GetCircle(Circle &circle) const { circle = m_data.circle; }

	// ellipse
	void SetEllipse(const Ellipse &ellipse) { m_type = Type_Ellipse; m_data.ellipse = ellipse; }
	void GetEllipse(Ellipse &ellipse) const { ellipse = m_data.ellipse; }

	// rotated ellipse
	void SetRotatedEllipse(const RotatedEllipse &rotatedEllipse) { m_type = Type_RotatedEllipse; m_data.rotatedEllipse = rotatedEllipse; }
	void GetRotatedEllipse(RotatedEllipse &rotatedEllipse) const { rotatedEllipse = m_data.rotatedEllipse; }
	
	// circle arc
	void SetCircleArc(const CircleArc &circleArc) { m_type = Type_CircleArc; m_data.circleArc = circleArc; }
	void GetCircleArc(CircleArc &circleArc) const { circleArc = m_data.circleArc; }

	// ellipse arc
	void SetEllipseArc(const EllipseArc &ellipseArc) { m_type = Type_EllipseArc; m_data.ellipseArc = ellipseArc; }
	void GetEllipseArc(EllipseArc &ellipseArc) { ellipseArc = m_data.ellipseArc; }

	// rotated ellipse arc
	void SetRotatedEllipseArc(const RotatedEllipseArc &rotatedEllipseArc) { m_type = Type_RotatedEllipseArc; m_data.rotatedEllipseArc = rotatedEllipseArc; }
	void GetRotatedEllipseArc(RotatedEllipseArc &rotatedEllipseArc) { rotatedEllipseArc = m_data.rotatedEllipseArc; }

	// polygon
	void SetPolygon(const std::vector<Point> &points) { m_type = Type_Polygon; m_points = points; }
	void GetPolygon(std::vector<Point> &points) const { points = m_points; }

	void ClearPolygonPoints() { m_type = Type_Polygon; m_points.clear(); }
	void AddPolygonPoint(const Point &point) { m_type = Type_Polygon; m_points.push_back(point); }
	size_t GetNumPolygonPoints() const { return m_points.size(); }
	void GetPolygonPoint(size_t i, Point &point) const { point = m_points[i]; }

	// polyline
	void SetPolyline(const std::vector<Point> &points) { m_type = Type_Polyline; m_points = points; }
	void GetPolyline(std::vector<Point> &points) { points = m_points; }

	void ClearPolylinePoints() { m_type = Type_Polyline; m_points.clear(); }
	void AddPolylinePoint(const Point &point) { m_type = Type_Polyline; m_points.push_back(point); }
	size_t GetNumPolylinePoints() const { return m_points.size(); }
	void GetPolylinePoint(size_t i, Point &point) const { point = m_points[i]; }

private:
	Type m_type;
	int m_usage;
	int m_imageId;

	union
	{
		Point point;
		Line line;
		Rect rect;
		RotatedRect rotatedRect;
		Circle circle;
		Ellipse ellipse;
		RotatedEllipse rotatedEllipse;
		CircleArc circleArc;
		EllipseArc ellipseArc;
		RotatedEllipseArc rotatedEllipseArc;
	} m_data;

	std::vector<Point> m_points;
};

class CParameter
{
public:
	enum Type
	{
		Type_Invalid = 0,
		Type_Bool = 1,
		Type_Int = 2,
		Type_Float = 3,
		Type_Double = 4,
		Type_Enum = 5,
		Type_String = 6
	};

public:
	CParameter(Type type = Type_Invalid) { m_type = type; }

	// common
	Type GetType() const { return m_type; }
	void SetName(const std::wstring &name) { m_name = name; }
	std::wstring GetName() const { return m_name; }
	void SetDescription(const std::wstring &description) { m_description = description; }
	std::wstring GetDescription() const { return m_description; }

	// bool
	void SetBool(bool value) { m_type = Type_Bool; m_data.boolData.value = value; }
	void SetBoolDefault(bool value) { m_type = Type_Bool; m_data.boolData.defaultValue = value; }
	bool GetBool() const { return m_data.boolData.value; }
	bool GetBoolDefault() const { return m_data.boolData.defaultValue; }

	// int
	void SetInt(int value) { m_type = Type_Int; m_data.intData.value = value; }
	void SetIntDefault(int value) { m_type = Type_Int; m_data.intData.defaultValue = value; }
	void SetIntLimits(int minValue, int maxValue)  { m_type = Type_Int; m_data.intData.minValue = minValue; m_data.intData.maxValue = maxValue; }
	int GetInt() const { return m_data.intData.value; }
	int GetIntDefault() const { return m_data.intData.defaultValue; }
	void GetIntLimits(int &minValue, int &maxValue) { minValue = m_data.intData.minValue; maxValue = m_data.intData.maxValue; } 

	// float
	void SetFloat(float value) { m_type = Type_Float; m_data.floatData.value = value; }
	void SetFloatDefault(float value) { m_type = Type_Float; m_data.floatData.defaultValue = value; }
	void SetFloatLimits(float minValue, float maxValue)  { m_type = Type_Float; m_data.floatData.minValue = minValue; m_data.floatData.maxValue = maxValue; }
	float GetFloat() const { return m_data.floatData.value; }
	float GetFloatDefault() const { return m_data.floatData.defaultValue; }
	void GetFloatLimits(float &minValue, float &maxValue) { minValue = m_data.floatData.minValue; maxValue = m_data.floatData.maxValue; } 

	// double
	void SetDouble(double value) { m_type = Type_Double; m_data.doubleData.value = value; }
	void SetDoubleDefault(double value) { m_type = Type_Double; m_data.doubleData.defaultValue = value; }
	void SetDoubleLimits(double minValue, double maxValue)  { m_type = Type_Double; m_data.doubleData.minValue = minValue; m_data.doubleData.maxValue = maxValue; }
	double GetDouble() const { return m_data.doubleData.value; }
	double GetDoubleDefault() const { return m_data.doubleData.defaultValue; }
	void GetDoubleLimits(double &minValue, double &maxValue) { minValue = m_data.doubleData.minValue; maxValue = m_data.doubleData.maxValue; } 

	// enum
	void ClearEnumItems() { m_enumItems.clear(); }
	void AddEnumItem(int value, const std::wstring &description) { m_enumItems.push_back(std::pair<int, std::wstring>(value, description)); }
	void SetEnum(int value) { m_type = Type_Enum; m_data.intData.value = value; }
	void SetEnumDefault(int value) { m_type = Type_Enum; m_data.intData.defaultValue = value; }
	int GetEnum() const { return m_data.intData.value; }
	int GetEnumDefault() const { return m_data.intData.defaultValue; }

	// called by SDK user
	int GetEnumNumItems() const { return m_enumItems.size(); }
	bool GetEnumDescriptionByIndex(size_t i, std::wstring &description) 
	{
		if (i < m_enumItems.size())
		{
			description = m_enumItems[i].second;
			return true;
		}
		else
			return false;
	}
	void SetEnumByIndex(int i) 
	{
		if (i >= 0 && i < (int)m_enumItems.size())
			m_data.intData.value = m_enumItems[i].first; 
	}
	int GetEnumIndex() const
	{ 
		int idx = -1;
		for (size_t i = 0; i < m_enumItems.size(); i++)
		{
			if (m_enumItems[i].first == m_data.intData.value)
			{
				idx = i;
				break;
			}
		}
		return idx;
	}

	int GetEnumDefaultIndex() const 
	{ 
		int idx = -1;
		for (size_t i = 0; i < m_enumItems.size(); i++)
		{
			if (m_enumItems[i].first == m_data.intData.defaultValue)
			{
				idx = i;
				break;
			}
		}
		return idx;
	}

	// string
	void SetString(const std::wstring &string) { m_type = Type_String; m_stringValue = string; }
	void SetStringDefault(const std::wstring &string) { m_type = Type_String; m_stringDefault = string; }
	std::wstring GetString() const { return m_stringValue; }
	std::wstring GetStringDefault() const { return m_stringDefault; }

private:
	// common
	Type m_type;
	std::wstring m_name;
	std::wstring m_description;

	struct BoolData { bool value, defaultValue; };
	struct IntData { int value, defaultValue, minValue, maxValue; };
	struct FloatData { float value, defaultValue, minValue, maxValue; };
	struct DoubleData { double value, defaultValue, minValue, maxValue; };

	// bool, int, float, double
	union Data
	{
		BoolData boolData;
		IntData intData;
		FloatData floatData;
		DoubleData doubleData;
	} m_data;

	// enum
	std::vector<std::pair<int, std::wstring>> m_enumItems;

	// string
	std::wstring m_stringValue;
	std::wstring m_stringDefault;
};

class CResultItem
{
public:
	enum Type
	{
		Type_Invalid = 0,
		Type_Bool,
		Type_Int,
		Type_Float,
		Type_Double,
		Type_String,
		Type_Region
	};

	enum Status
	{
		Status_NA = -1,
		Status_OK = 0,
		Status_NG = 1
	};

public:
	CResultItem(Type type = Type_Invalid) { m_type = type; };

	void SetType(Type type) { m_type = type; }
	Type GetType() const { return m_type; }

	void SetRegionType(CRegion::Type type) { m_region.SetType(type); }
	CRegion::Type GetRegionType() const { return m_region.GetType(); }

	void SetStatus(Status status) { m_status = status; }
	Status GetStatus() const { return m_status; }

	void SetName(const std::wstring &name) { m_name = name; }
	std::wstring GetName() const { return m_name; }

	void SetDescription(const std::wstring &description) { m_description = description; }
	std::wstring GetDescription() const { return m_description; }

	void SetBool(bool value) { m_type = Type_Bool; m_basicData.boolValue = value; }
	bool GetBool() const { return m_basicData.boolValue; }

	void SetInt(int value) { m_type = Type_Int; m_basicData.intValue = value; }
	int GetInt() const { return m_basicData.intValue; }

	void SetFloat(float value) { m_type = Type_Float; m_basicData.floatValue = value; }
	float GetFloat() const { return m_basicData.floatValue; }

	void SetDouble(double value) { m_type = Type_Double; m_basicData.doubleValue = value; }
	double GetDouble() const { return m_basicData.doubleValue; }

	void SetString(const std::wstring &value) { m_type = Type_String; m_string = value; }
	std::wstring GetString() const { return m_string; }

	void SetRegion(const CRegion &region) { m_type = Type_Region; m_region = region; }
	void GetRegion(CRegion &region) const { region = m_region; }

private:
	Type m_type;
	Status m_status;
	std::wstring m_name;
	std::wstring m_description;

	union
	{
		bool boolValue;
		int intValue;
		float floatValue;
		double doubleValue;
	} m_basicData;

	std::wstring m_string;
	CRegion m_region;
};

class CBaseInspector
{
public:
	enum Type
	{
		Type_ImageInspector = 0,
		Type_ImageRegistrator,
	};

public:
	virtual Type GetType() const { return Type_ImageRegistrator; }

	// name and description of inspector
	virtual void GetNameAndDescription(std::wstring &name, std::wstring &description) const = 0;
	
	// input image
	virtual int GetNumImages() const { return 1; }
	virtual bool GetImageInfo(int i, std::wstring &name, std::wstring &description) const { name = L"Input image"; description = L""; return true; }
	virtual bool SetImage(int i, void *image, int width, int height, int bytesPerLine, int bitsPerPixel) { return true; }

	// input regions
	virtual int GetNumRegionUsages() const { return 0; }
	virtual bool GetRegionUsageInfo(int i, std::wstring &name, std::wstring &description, int &usage, int &typeOption) const { return true; }
	virtual bool SetRegions(const std::vector<CRegion> &regions) { return true; }

	// parameters
	virtual int GetNumParameters() const { return 0; }
	virtual bool GetParameter(int i, CParameter &param) const { return true; }
	virtual bool SetParameter(int i, const CParameter &param) { return true; }

	// input templates
	virtual int GetNumTemplates() const { return 0; }
	virtual bool GetTemplateInfo(int i, std::wstring &name, std::wstring &description) const { return true; }
	virtual bool SetTemplate(int i, const std::wstring &pathName) { return true; }

	// reference input
	virtual int GetNumReferenceInputs() const { return 0; }
	virtual bool GetReferenceInputInfo(int i, CResultItem::Type &type) const { return true; }
	virtual bool SetReferenceInput(int i, const CResultItem &referenceInput) { return true; }

	// do inspection
	virtual bool Inspect() = 0;

	// get results
	virtual int GetNumResultItems() const { return 0; }
	virtual bool GetResultItemInfo(int i, CResultItem &resultItem) const { return true; }
	virtual bool GetResultItems(std::vector<CResultItem> &resultItems) const = 0;

	// image registration
	virtual bool GetRegistrationResult(float &x, float &y, float &angle, float &refX, float &refY, float &scale) const { return true; }
	virtual bool SetRegistrationInfo(float x, float y, float angle, float refX, float refY, float scale) { return true; }

	static void Template2Test(float alignX, float alignY, float alignAngle, float alignRefX, float alignRefY, float alignScale, float &x, float &y)
	{
		float dx = (x - alignRefX) * alignScale;
		float dy = (y - alignRefY) * alignScale;

		float cosT = cos(alignAngle);
		float sinT = sin(alignAngle);

		x = (dx * cosT - dy * sinT) + alignX;
		y = (dy * cosT + dx * sinT) + alignY;
	}
};

#endif // ALG_INTERFACE_H
