#ifndef HX_OCR_ANALYZER_H__
#define HX_OCR_ANALYZER_H__


#ifdef ALGO_ADAPTER
#define LABELOCR_DLL_API __declspec(dllexport)
#else
#define LABELOCR_DLL_API __declspec(dllimport)
#endif

#include <memory>
#include <vector>
#include <string>
#include <algorithm>
#include <mutex>


class LABELOCR_DLL_API OcrAnalyzer
{
public:
    ~OcrAnalyzer();
    std::string run(const std::string& labelPath);
	std::string run(unsigned char*pRaw, int rows, int cols);
	static std::shared_ptr<OcrAnalyzer> instance();

private:
	OcrAnalyzer();

	static std::shared_ptr<OcrAnalyzer> m_ins;
	static std::mutex m_mutex;

};

#endif
