#include "DeblurAnalyzer.h"
#include "DeblurReport.h"
#include "log/Logger4cpp.h"
#include "opencv2/opencv.hpp"
#include "interface/ITFAdapter.h"


std::shared_ptr<DeblurAnalyzer> DeblurAnalyzer::m_ins;
std::mutex DeblurAnalyzer::m_mutex;




std::shared_ptr<DeblurAnalyzer> DeblurAnalyzer::instance()
{
	if (m_ins == NULL)
	{
		std::unique_lock<std::mutex> lock(m_mutex);
		if (m_ins == NULL)
		{
			m_ins.reset(new DeblurAnalyzer());
		}
	}

	return m_ins;
}


DeblurAnalyzer::DeblurAnalyzer()
{
	LOG_INFO( __func__ << " version v2.0");
}


DeblurAnalyzer::~DeblurAnalyzer()
{
}


bool DeblurAnalyzer::run(unsigned char*pRaw,int rows,int cols)
{
	if (pRaw == nullptr || rows == 0 || cols == 0)
	{
		cout << "parameters failed" << endl;
		return false;
	}
	string label = "";
	std::cout << "start analyzeTmap." << std::endl;
	std::shared_ptr<TFAdapter> tf = TFAdapter::create(NetId::NI_DEBLUR, 1);

	std::cout << "start create tfadapter end." << std::endl;

	auto report = make_shared<DeblurReport>();

	std::cout << "width:" << cols << " height:" << rows << std::endl;
	PathologyImageItemPtr imgItem(new PathologyImageItem(cols, rows, 0, 0, cols*rows / 8, 0));
	memcpy(imgItem->rawBuffer, pRaw, rows*cols * 3);
	

	tf->run(imgItem, report);
	cv::Mat img;
	report->getDeblurImg(img);

	memset(pRaw, 0, rows*cols * 3);
	memcpy(pRaw, img.data, rows*cols * 3);

	return true;
}

