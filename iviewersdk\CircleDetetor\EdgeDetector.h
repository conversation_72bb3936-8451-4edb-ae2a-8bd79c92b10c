// edge detector
// Copyright (C) UNIC Technologies. All rights reserved.
// History:
// (1) 20120711: <NAME_EMAIL>
// (2) 20121116: added ZoomImage functions by Steve
// (3) 20130313: changed border in nms from 1 to 2 by <PERSON>

#ifndef EDGE_DETECTOR_H
#define EDGE_DETECTOR_H

#include <vector>
using namespace std;

namespace EdgeDetector {

	struct EdgePoint
	{
		int nX, nY; // integer-pixel position
		int nMag; // gradient magnitude in the range [0, 255]
		int nDir; // gradient direction in the range [0, 255]
		float fSubX, fSubY; // sub-pixel position
	};

	struct EdgeChain
	{
		vector<EdgePoint> points;
	};

	enum MagType
	{
		kMagSqrt,
		kMagAbs
	};

	// smooth image by binomial filter
	// NOTE: pSrcImg and pDstImg can point to the same buffer
	void BinomialSmooth(unsigned char *pSrcImg, unsigned char *pDstImg, int nImgWidth, int nImgHeight, int nMaskWidth, int nMaskHeight);
	// smooth image by box filter
	void BoxSmooth(unsigned char *pSrcImg, unsigned char *pDstImg, int nImgWidth, int nImgHeight, int nMaskWidth, int nMaskHeight);
	void BoxSmooth(unsigned char *pImg, int nImgWidth, int nImgHeight, int nMaskWidth, int nMaskHeight);

	// compute image gradient
	void ComputeGradient(unsigned char *pInImg, unsigned char *pMagImg, unsigned char *pDirImg, int nImgWidth, int nImgHeight, int nLowTh, MagType magType = kMagSqrt);
	void ComputeGradient(unsigned char *pInImg, unsigned char *pMskImg, unsigned char *pMagImg, unsigned char *pDirImg, int nImgWidth, int nImgHeight, int nLowTh, MagType magType = kMagSqrt);
	// suppress non maximum
	void SuppressNonMaximum(unsigned char *pMagImg, unsigned char *pDirImg, unsigned char *pNmsImg, int nImgWidth, int nImgHeight);
	// hysteresis thresholding
	void HysteresisThreshold(unsigned char *pMagIn, unsigned char *pMagOut, int nImgWidth, int nImgHeight, int nLowTh, int nHighTh);
	// link edge points to edge chains
	void LinkEdges(unsigned char *pNmsImg, unsigned char *pDirImg, int nImgWidth, int nImgHeight, int nLowTh, int nHighTh, int nMinLength, int nDirTol, vector<EdgeChain> &chains);
	
	// get sub-pixel edge positions for edge chains
	void GetSubpixelEdge(unsigned char *pMagImg, unsigned char *pDirImg, int nImgWidth, int nImgHeight, vector<EdgeChain> &chains);
	void GetSubpixelEdge(unsigned char *pMagImg, unsigned char *pDirImg, int nWidth, int nHeight, vector<EdgePoint> &points);
	// get sub-pixel edge position for an edge point
	void GetSubpixelEdge(unsigned char *pMagImg, int nImgWidth, int nImgHeight, int nX, int nY, float &fX, float &fY);
	
	// break chain to line segments
	// terminations[2n] - start index
	// terminations[2n+1] - end index
	void BreakChain2Lines(vector<EdgePoint> &points, int nStartIndex, int nEndIndex, int nMinLength, vector<int> &terminations);

	// fit line via least-squares method
	// points[2n] - x coordinates
	// points[2n+1] - y coordinates
	bool FitLineLS(vector<float> &points, float &a, float &b, float &c);

	// draw line on a gray-scale image via Bresenham method
	void BresenhamLine(unsigned char *pImg, int w, int h, int x0, int y0, int x1, int y1, unsigned char label);
	void BresenhamCircle(unsigned char *pImg, int w, int h, int nX, int nY, int nR, unsigned char label);

	// dump edge chains to image
	void DumpEdgeChains(unsigned char *pInImg, int nImgWidth, int nImgHeight, vector<EdgeChain> &chains);

	void ComputeAngle(const int nDx, const int nDy, unsigned char &ucAngle);
	int GetAngleSin(unsigned char ucAngle);
	int GetAngleCos(unsigned char ucAngle);

	int GetZoomSize(int srcSize, float scale);
	void ZoomImage(unsigned char *srcImg, int srcWidth, int srcHeight, unsigned char *dstImg, int dstWidth, int dstHeight);
	void ZoomImage(unsigned char *srcImg, int srcWidth, int srcHeight, unsigned char *dstImg, float scaleWidth, float scaleHeight);
};


#endif // EDGE_DETECTOR_H
