/**
* @date         2014-07-16
* @filename     AlgoExample.cpp
* @purpose      example class for using CAlgoInterface class
* @version      1.0
* @history      initial draft
* <AUTHOR> UNIC, Beijing, China
* @copyright    UNIC Technologies, 2005-2014. All rights reserved.
*/

#include "./AlgoInterface.h"
#include "./AlgoCircleDetector.h"
#include "./CircleDetectorCore.h"

/**
* @method       CAlgoExample::CAlgoExample
* @access       public
* @brief        construct function
* @param        void
* @return
* <AUTHOR> <EMAIL>
* @date         2012-07-18
* @history      initial draft
*/
CAlgoCircleDetector::CAlgoCircleDetector(void)
{
    // initialization
    m_pucImgSrc = NULL;
    m_pucImgTpl = NULL;
    m_nWidth = 0;
    m_nHeight = 0;
    m_nImgSize = 0;
    m_bHasTemplate = false;
    m_pCircDetect = new CircleDetector();

    m_fCircleX = 0.0f;
    m_fCircleY = 0.0f;
    m_fCircleR = 0.0f;
}

/**
* @method       CAlgoExample::~CAlgoExample
* @access       public
* @brief        destruct function
* @param        void
* @return
* <AUTHOR> Lei, <EMAIL>
* @date         2012-07-18
* @history      initial draft
*/
CAlgoCircleDetector::~CAlgoCircleDetector(void)
{
    mdelete(m_pucImgSrc);

    delete m_pCircDetect;
} //lint !e1740

/**
* @method       CAlgoExample::PackPara
* @access       private
* @brief        pack parameters
* @param        void
* @return       void
* <AUTHOR> Lei, <EMAIL>
* @date         2012-07-18
* @history      initial draft
*/
int CAlgoCircleDetector::PackPara(void)
{
    StartPackPara();

    AddGroupPara("Circle Polarity");
    const int nPolarity = 3;
    const char *apcPolaritys[nPolarity] = { "Bright", "Dark", "Any" };
    UI_RADIO_S radioPolarity;
    for (int i = 0; i < nPolarity; ++i)
    {
        radioPolarity.bChecked = i == m_para.ePolarity;
        AddElemPara(radioPolarity, apcPolaritys[i]);
    }

    AddGroupPara("Image Channel");
    const int N = 4;
    const char *apcImageChannel[N] = { "Gray", "Red", "Green", "Blue" };
    UI_RADIO_S radioChannel;
    for (int i = 0; i < N; ++i)
    {
        radioChannel.bChecked = i == m_para.channel;
        AddElemPara(radioChannel, apcImageChannel[i]);
    }

    AddGroupPara("Parameters");
    UI_EDIT_S stEditEdgeTh;
    stEditEdgeTh.nMax = 255;
    stEditEdgeTh.nMin = 0;
    stEditEdgeTh.nValue = m_para.nEdgeTh;
    AddElemPara(stEditEdgeTh, "Edge Threshold");

    UI_EDIT_S stSmoothR;
    stSmoothR.nMax = 255;
    stSmoothR.nMin = 0;
    stSmoothR.nValue = m_para.smoothRadius;
    AddElemPara(stSmoothR, "Smooth Radius");

    UI_EDIT_F_S stMinDiameter;
    stMinDiameter.fMax = 10000000.0f;
    stMinDiameter.fMin = 0.0f;
    stMinDiameter.fValue = m_para.fRealMinDiameter;
    AddElemPara(stMinDiameter, "Min Diameter");

    UI_EDIT_F_S stMaxDiameter;
    stMaxDiameter.fMax = 10000000.0f;
    stMaxDiameter.fMin = 0.0f;
    stMaxDiameter.fValue = m_para.fRealMaxDiameter;
    AddElemPara(stMaxDiameter, "Max Diameter");

    UI_EDIT_F_S stEpsilon;
    stEpsilon.fMax = 100.0f;
    stEpsilon.fMin = 0.0f;
    stEpsilon.fValue = m_para.fEpsilon;
    AddElemPara(stEpsilon, "Epsilon");

    UI_EDIT_S stMinScore;
    stMinScore.nMax = 100;
    stMinScore.nMin = 0;
    stMinScore.nValue = m_para.nMinScore;
    AddElemPara(stMinScore, "Min Score");

    UI_EDIT_S stDownSampleTimes;
    stDownSampleTimes.nMax = 100;
    stDownSampleTimes.nMin = 0;
    stDownSampleTimes.nValue = m_para.nDownSample2sqr;
    AddElemPara(stDownSampleTimes, "Down Sample Times");

    UI_EDIT_F_S stPixelSize;
    stPixelSize.fMax = 100.0f;
    stPixelSize.fMin = 0.0f;
    stPixelSize.fValue = m_para.fPixelSize;
    AddElemPara(stPixelSize, "Pixel Size");

    UI_CHECK_S check;
    check.bChecked = m_para.bDumpImage;
    AddElemPara(check, "Dump Image");

    return 0;
}

/**
* @method       CAlgoExample::UnpackPara
* @access       private
* @brief        unpack parameters
* @param        void
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-07-16
* @history      initial draft
*/
int CAlgoCircleDetector::UnpackPara(void)
{
    AddGroupPara("Circle Polarity");
    UI_RADIO_S radioPolarity;
    const int nPolarity = 3;
    const char *apcPolaritys[nPolarity] = { "Bright", "Dark", "Any" };
    for (int i = 0; i < nPolarity; ++i)
    {
        GetValuePara("Circle Polarity", apcPolaritys[i], &radioPolarity);
        if (radioPolarity.bChecked)
        {
            m_para.ePolarity = (CircleDetector::Polarity)i;
            break;
        }
    }

    AddGroupPara("Image Channel");
    UI_RADIO_S radioChannel;
    const int nChannel = 4;
    const char *apcImageChannel[nChannel] = { "Gray", "Red", "Green", "Blue" };
    for (int i = 0; i < nChannel; ++i)
    {
        GetValuePara("Image Channel", apcImageChannel[i], &radioChannel);
        if (radioChannel.bChecked)
        {
            m_para.channel = (CircleDetector::Channel)i;
            break;
        }
    }

    AddGroupPara("Parameters");
    UI_EDIT_S stEditEdgeTh;
    GetValuePara("Parameters", "Edge Threshold", &stEditEdgeTh);
    m_para.nEdgeTh = (uchar)stEditEdgeTh.nValue;

    UI_EDIT_S stSmoothR;
    GetValuePara("Parameters", "Smooth Radius", &stSmoothR);
    m_para.smoothRadius = stSmoothR.nValue;

    UI_EDIT_F_S stMinDiameter;
    GetValuePara("Parameters", "Min Diameter", &stMinDiameter);
    m_para.fRealMinDiameter = stMinDiameter.fValue;

    UI_EDIT_F_S stMaxDiameter;
    GetValuePara("Parameters", "Max Diameter", &stMaxDiameter);
    m_para.fRealMaxDiameter = stMaxDiameter.fValue;

    UI_EDIT_F_S stEpsilon;
    GetValuePara("Parameters", "Epsilon", &stEpsilon);
    m_para.fEpsilon = stEpsilon.fValue;

    UI_EDIT_S stMinScore;
    GetValuePara("Parameters", "Min Score", &stMinScore);
    m_para.nMinScore = stMinScore.nValue;

    UI_EDIT_S stDownSampleTimes;
    GetValuePara("Parameters", "Down Sample Times", &stDownSampleTimes);
    m_para.nDownSample2sqr = stDownSampleTimes.nValue;

    UI_EDIT_F_S stPixelSize;
    GetValuePara("Parameters", "Pixel Size", &stPixelSize);
    m_para.fPixelSize = stPixelSize.fValue;

    UI_CHECK_S check;
    GetValuePara("Parameters", "Dump Image", &check);
    m_para.bDumpImage = check.bChecked;

    return 0;
}

/**
* @method       CAlgoExample::SetImage
* @access       virtual private
* @brief        set extra images (template or something)
* @param        const int nIndex
* @param        const UN_IMAGE_INFO_S * pstImg
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-16
* @history      initial draft
*/
int CAlgoCircleDetector::SetImage(const int nIndex, const UN_IMAGE_INFO_S *pstImg)
{
    m_bHasTemplate = false;
    if (0 != nIndex || NULL == pstImg)
    {
        return 1;
    }

    int nRv = SetImage(pstImg, true);
    m_bHasTemplate = 0 == nRv;

    return nRv;
}

/**
* @method       CAlgoExample::DoInspect
* @access       public
* @brief        do the main job
* @param        const UN_IMAGE_INFO_S * pstImg
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-16
* @history      initial draft
*/
int CAlgoCircleDetector::DoInspect(const UN_IMAGE_INFO_S *pstImg)
{
    // check input parameters
    if (NULL == pstImg || NULL == pstImg->pucBuffer)
    {
        return 1;
    }

    // allocate memory and copy gray image
    int nRv = SetImage(pstImg, false);
    if (0 != nRv)
    {
        return nRv;
    }

    // do the job
    m_fCircleX = m_fCircleY = m_fCircleR = 0.0f;

    const int nChannel = pstImg->nPitch / pstImg->nWidth;
    m_pCircDetect->SetImage(0, pstImg->pucBuffer, pstImg->nWidth, pstImg->nHeight,
        pstImg->nPitch, nChannel * 8);

    std::vector<CRegion> regions;
    CRegion::Rect rct;
    rct.topLeft.x = pstImg->nLeft;
    rct.topLeft.y = pstImg->nTop;
    rct.width = pstImg->nRight - pstImg->nLeft + 1;
    rct.height = pstImg->nBottom - pstImg->nTop + 1;
    CRegion ROIRegion;
    ROIRegion.SetRect(rct);
    regions.push_back(ROIRegion);
    m_pCircDetect->SetRegions(regions);

    m_pCircDetect->SetPara(m_para);

    bool bRev = m_pCircDetect->Inspect();
    if (!bRev)
    {
        return 1;
    }

    std::vector<CResultItem> RstCricle;
    bRev = m_pCircDetect->GetResultItems(RstCricle);
    if (!bRev || 0 >= RstCricle.size())
    {
        return 1;
    }

    CRegion region;
    CRegion::Circle circle;
    RstCricle[0].GetRegion(region);
    region.GetCircle(circle);
    m_fCircleX = circle.center.x;
    m_fCircleY = circle.center.y;
    m_fCircleR = circle.radius;

    return 0;
}

/**
* @method       CAlgoExample::PackResult
* @access       private
* @brief        pack results
* @param        void
* @return       void
* <AUTHOR> Lei, <EMAIL>
* @date         2012-07-18
* @history      initial draft
*/
int CAlgoCircleDetector::PackResult(void)
{
    StartPackResult();
    AddGroupResult("CircleDetect Result");
    AddElemResult(m_fCircleX, "Circle X");
    AddElemResult(m_fCircleY, "Circle Y");
    AddElemResult(m_fCircleR, "Circle Radius");

    AddGroupResult("CircleDetect Real Result");
    float fRealDiameter = m_para.fPixelSize * m_fCircleR * 2.0;
    AddElemResult(fRealDiameter, "Real Diameter");

    float fRealCircleX = m_para.fPixelSize * m_fCircleX;
    AddElemResult(fRealCircleX, "Real Circle X");

    float fRealCircleY = m_para.fPixelSize * m_fCircleY;
    AddElemResult(fRealCircleY, "Real Circle Y");

    return 0;
}

/**
* @method       CAlgoExample::PackDisplay
* @access       private
* @brief        pack display
* @param        void
* @return       void
* <AUTHOR> Lei, <EMAIL>
* @date         2012-07-18
* @history      initial draft
*/
int CAlgoCircleDetector::PackDisplay(void)
{
    StartPackDisp();
    AddGroupDisp("Circle");

    UN_CIRCLE_F_S stCircleF;
    stCircleF.fX = m_fCircleX;
    stCircleF.fY = m_fCircleY;
    stCircleF.fRadius = m_fCircleR;
    AddElemDisp(stCircleF, "Circle");

    return 0;
}

/**
* @method       CAlgoExample::SetImage
* @access       private
* @brief        allocate memory and copy gray image
* @param        const UN_IMAGE_INFO_S * pstImg
* @param        const bool bTemplate
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-16
* @history      initial draft
*/
int CAlgoCircleDetector::SetImage(const UN_IMAGE_INFO_S *pstImg, const bool bTemplate)
{
    // check input image
    if (NULL == pstImg || NULL == pstImg->pucBuffer || IMAGE_MIN_SIZE > pstImg->nWidth
        || IMAGE_MIN_SIZE > pstImg->nHeight || pstImg->nPitch < pstImg->nWidth)
    {
        return 1;
    }

    // check image's channel
    const int nChannel = pstImg->nPitch / pstImg->nWidth;
    if (1 != nChannel && 3 != nChannel && 4 != nChannel)
    {
        return 1;
    }

    // allocate memory
    if (m_nWidth != pstImg->nWidth || m_nHeight != pstImg->nHeight)
    {
        mdelete(m_pucImgSrc);
        // memory was released, so there is no template
        m_bHasTemplate = false;

        // allocate memory for saving image
        m_pucImgSrc = mnew uchar[pstImg->nWidth * pstImg->nHeight * 2];
        if (NULL == m_pucImgSrc)
        {
            return 1;
        }

        // update image size
        m_nWidth = pstImg->nWidth;
        m_nHeight = pstImg->nHeight;
        m_nImgSize = pstImg->nWidth * pstImg->nHeight;
        m_pucImgTpl = m_pucImgSrc + m_nImgSize;
        memset(m_pucImgSrc, 0, sizeof(uchar) * m_nImgSize * 2);
    }

    // copy image data (template or source)
    uchar *pucImg = bTemplate ? m_pucImgTpl : m_pucImgSrc;
    CopyGrayData(pstImg->pucBuffer, pucImg, m_nWidth, m_nHeight,
        pstImg->nPitch, 0, m_nWidth, 0, m_nHeight);

    return 0;
}

/**
* @method       CAlgoExample::GetPara
* @access       public
* @brief        get current parameters
* @param        Para & para
* @return       void
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-16
* @history      initial draft
*/
void CAlgoCircleDetector::GetPara(CircleDetector::Param &para) const
{
    para = m_para;
}

/**
* @method       CAlgoExample::SetPara
* @access       public
* @brief        set parameters
* @param        const Para & para
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-16
* @history      initial draft
*/
int CAlgoCircleDetector::SetPara(const CircleDetector::Param &para)
{
    // you could check the validation of input parameters
    m_para = para;
    return 0;
}

/**
* @method       CAlgoExample::GetResults
* @access       public
* @brief        function for others to get the results of your algorithm
* @param        int & nBlackPixels
* @param        int & nBrightPixels
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
bool CAlgoCircleDetector::GetResults(float &fCircleX, float &fCircleY, float &fCircleR) const
{
    fCircleX = m_fCircleX;
    fCircleY = m_fCircleY;
    fCircleR = m_fCircleR;

    return true;
}

/**
* @method       CAlgoExample::IsRoiOk
* @access       private
* @brief        check ROI
* @param        const int nWidth
* @param        const int nHeight
* @param        const int nLeft
* @param        const int nRight
* @param        const int nTop
* @param        const int nBottom
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-16
* @history      initial draft
*/
bool CAlgoCircleDetector::IsRoiOk(const int nWidth, const int nHeight, const int nLeft,
    const int nRight, const int nTop, const int nBottom) const
{
    if (0 > nLeft || 0 > nTop || nWidth < nRight
        || nHeight < nBottom || nLeft >= nRight || nTop >= nBottom)
    {
        return false;
    }

    return true;
}

/**
* @method       CAlgoExample::CopyGrayData
* @access       private
* @brief        get gray image data of source image
* @param        const uchar * pucIn
* @param        uchar * pucOut
* @param        const int nWidth
* @param        const int nHeight
* @param        const int nPitch
* @param        const int nLeft
* @param        const int nRight
* @param        const int nTop
* @param        const int nBottom
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-16
* @history      initial draft
*/
bool CAlgoCircleDetector::CopyGrayData(const uchar *pucIn, uchar *pucOut, const int nWidth,
    const int nHeight, const int nPitch, const int nLeft,
    const int nRight, const int nTop, const int nBottom) const
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucIn || NULL == pucOut
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || !IsRoiOk(nWidth, nHeight, nLeft, nRight, nTop, nBottom)
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    pucIn += nTop * nPitch + nLeft * nChannels;
    if (1 == nChannels)
    {
        for (int i = nTop; i < nBottom; i++)
        {
            memcpy(pucOut, pucIn, sizeof(uchar) * ((uint)(nRight - nLeft)));
            pucOut += nRight - nLeft;
            pucIn += nPitch;
        }
    }
    else if (3 == nChannels || 4 == nChannels)
    {
        for (int i = nTop; i < nBottom; i++)
        {
            for (int j = 0; j < (nRight - nLeft); j++, pucIn += nChannels, pucOut++)
            {
                *pucOut = (uchar)((306 * (*(pucIn + 2))
                    + 601 * (*(pucIn + 1))
                    + 117 * (*pucIn) + 512) >> 10); //lint !e702
            }

            pucIn += nPitch - (nRight - nLeft) * nChannels;
        }
    }

    return true;
} //lint !e429 !e593

/**
* @method       CreateGapObject
* @access       public
* @brief        access point for loading DLL
* @return       void * __cdecl
* <AUTHOR> Lei, <EMAIL>
* @date         2012-07-18
* @history      initial draft
*/
extern "C" __declspec(dllexport) void * __cdecl CreateGapObject()
{
    return mnew CAlgoCircleDetector();
}

/**
* @method       DestroyGapObject
* @access       public
* @brief        destroy an object
* @param        CAlgoInterface * & pAlgo
* @return       bool __cdecl
* <AUTHOR> Lei, <EMAIL>
* @date         2013-03-04
* @history      initial draft
*/
extern "C" __declspec(dllexport) bool __cdecl DestroyGapObject(CAlgoInterface *&pAlgo)
{
    CAlgoCircleDetector *pThis = dynamic_cast<CAlgoCircleDetector *>(pAlgo);
    if (NULL == pThis)
    {
        return false;
    }

    delete pThis;
    pAlgo = NULL;
    return true;
}