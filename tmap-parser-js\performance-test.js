#!/usr/bin/env node

/**
 * Performance test for TMAP image extraction
 * Tests the extract-images command with different configurations
 */

import { spawn } from 'child_process';
import fs from 'fs/promises';
import path from 'path';

const TEST_FILE = process.argv[2] || 'E:\\TMAP\\Test_1.TMAP';
const OUTPUT_DIR = './perf_test_output';

async function runCommand(command, args) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const child = spawn('node', [command, ...args], {
      stdio: ['inherit', 'pipe', 'pipe']
    });
    
    let stdout = '';
    let stderr = '';
    
    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    child.on('close', (code) => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      if (code === 0) {
        resolve({ stdout, stderr, duration, success: true });
      } else {
        reject({ stdout, stderr, duration, success: false, code });
      }
    });
  });
}

async function cleanupOutput() {
  try {
    await fs.rm(OUTPUT_DIR, { recursive: true, force: true });
  } catch (error) {
    // Ignore cleanup errors
  }
}

async function getDirectorySize(dirPath) {
  try {
    const files = await fs.readdir(dirPath);
    let totalSize = 0;
    
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stats = await fs.stat(filePath);
      totalSize += stats.size;
    }
    
    return totalSize;
  } catch (error) {
    return 0;
  }
}

async function runPerformanceTest() {
  console.log('🚀 TMAP Image Extraction Performance Test');
  console.log('═'.repeat(50));
  console.log(`Test File: ${TEST_FILE}`);
  console.log(`Output Directory: ${OUTPUT_DIR}`);
  console.log('');
  
  // Test 1: Basic extraction without performance metrics
  console.log('📋 Test 1: Basic Image Extraction');
  console.log('-'.repeat(30));
  
  await cleanupOutput();
  
  try {
    const result1 = await runCommand('src/cli.js', [
      'extract-images',
      TEST_FILE,
      '--output-dir', OUTPUT_DIR,
      '--prefix', 'test1'
    ]);
    
    const outputSize = await getDirectorySize(OUTPUT_DIR);
    
    console.log(`✅ Success: ${result1.duration}ms`);
    console.log(`📁 Output Size: ${(outputSize / 1024).toFixed(2)} KB`);
    console.log('');
  } catch (error) {
    console.log(`❌ Failed: ${error.duration}ms`);
    console.log(`Error: ${error.stderr}`);
    console.log('');
  }
  
  // Test 2: Extraction with performance metrics
  console.log('📋 Test 2: Extraction with Performance Analysis');
  console.log('-'.repeat(30));
  
  await cleanupOutput();
  
  try {
    const result2 = await runCommand('src/cli.js', [
      'extract-images',
      TEST_FILE,
      '--output-dir', OUTPUT_DIR,
      '--prefix', 'test2',
      '--performance'
    ]);
    
    const outputSize = await getDirectorySize(OUTPUT_DIR);
    
    console.log(`✅ Success: ${result2.duration}ms`);
    console.log(`📁 Output Size: ${(outputSize / 1024).toFixed(2)} KB`);
    console.log('');
    
    // Extract performance metrics from output
    const lines = result2.stdout.split('\n');
    const totalTimeLine = lines.find(line => line.includes('Total Time'));
    const parseTimeLine = lines.find(line => line.includes('Parse Time'));
    const successRateLine = lines.find(line => line.includes('Success Rate'));
    
    if (totalTimeLine) {
      console.log('📊 Performance Metrics:');
      console.log(`   ${totalTimeLine.trim()}`);
      if (parseTimeLine) console.log(`   ${parseTimeLine.trim()}`);
      if (successRateLine) console.log(`   ${successRateLine.trim()}`);
      console.log('');
    }
  } catch (error) {
    console.log(`❌ Failed: ${error.duration}ms`);
    console.log(`Error: ${error.stderr}`);
    console.log('');
  }
  
  // Test 3: Multiple runs for consistency
  console.log('📋 Test 3: Consistency Test (3 runs)');
  console.log('-'.repeat(30));
  
  const runs = [];
  
  for (let i = 1; i <= 3; i++) {
    await cleanupOutput();
    
    try {
      const result = await runCommand('src/cli.js', [
        'extract-images',
        TEST_FILE,
        '--output-dir', OUTPUT_DIR,
        '--prefix', `run${i}`
      ]);
      
      const outputSize = await getDirectorySize(OUTPUT_DIR);
      runs.push({ duration: result.duration, size: outputSize, success: true });
      console.log(`   Run ${i}: ${result.duration}ms, ${(outputSize / 1024).toFixed(2)} KB`);
    } catch (error) {
      runs.push({ duration: error.duration, size: 0, success: false });
      console.log(`   Run ${i}: FAILED (${error.duration}ms)`);
    }
  }
  
  // Calculate statistics
  const successfulRuns = runs.filter(r => r.success);
  if (successfulRuns.length > 0) {
    const avgDuration = successfulRuns.reduce((sum, r) => sum + r.duration, 0) / successfulRuns.length;
    const avgSize = successfulRuns.reduce((sum, r) => sum + r.size, 0) / successfulRuns.length;
    const minDuration = Math.min(...successfulRuns.map(r => r.duration));
    const maxDuration = Math.max(...successfulRuns.map(r => r.duration));
    
    console.log('');
    console.log('📈 Statistics:');
    console.log(`   Average Duration: ${avgDuration.toFixed(1)}ms`);
    console.log(`   Min Duration: ${minDuration}ms`);
    console.log(`   Max Duration: ${maxDuration}ms`);
    console.log(`   Average Output Size: ${(avgSize / 1024).toFixed(2)} KB`);
    console.log(`   Success Rate: ${(successfulRuns.length / runs.length * 100).toFixed(1)}%`);
  }
  
  console.log('');
  console.log('✨ Performance test completed!');
  
  // Cleanup
  await cleanupOutput();
}

// Run the test
runPerformanceTest().catch(error => {
  console.error('Performance test failed:', error);
  process.exit(1);
});
