/**
* @date         2015-11-20
* @filename     Frequency.cpp
* @purpose      functions in frequency domain
* @version      1.0
* @history      initial draft
* <AUTHOR> UNIC, Beijing, China
* @copyright    <EMAIL>, UNIC Technologies Inc, 2005-2015. All rights reserved.
*/

#include <opencv2/opencv.hpp>
#include "./CommonEx.h"
#include "./Templates.h"

using namespace cv;

extern bool g_bExpired;

// it's better to make small image in the center of padded image
// but for cvDFT, should copy image onto the padded image from (0,0)
// or the transformed back image will be shifted
#define FFT_NO_OFFSET

/*
   Direct Fourier transform
   dir =  1 gives forward transform
   dir = -1 gives reverse transform
*/
bool DFT(double *x1, double *y1, int m, int dir)
{
   long i,k;
   double arg;
   double cosarg,sinarg;
   double *x2=NULL,*y2=NULL;

   x2 = mnew double[m * 2];
   y2 = x2 + m;
   if (x2 == NULL)
      return false;

   for (i=0;i<m;i++) {
      x2[i] = 0;
      y2[i] = 0;
      arg = - dir * 2.0 * 3.141592654 * (double)i / (double)m;
      for (k=0;k<m;k++) {
         cosarg = cos(k * arg);
         sinarg = sin(k * arg);
         x2[i] += (x1[k] * cosarg - y1[k] * sinarg);
         y2[i] += (x1[k] * sinarg + y1[k] * cosarg);
      }
   }

   /* Copy the data back */
   if (dir == 1) {
      for (i=0;i<m;i++) {
         x1[i] = x2[i] / (double)m;
         y1[i] = y2[i] / (double)m;
      }
   } else {
      for (i=0;i<m;i++) {
         x1[i] = x2[i];
         y1[i] = y2[i];
      }
   }

   mdelete(x2);
   return true;
}

/*-------------------------------------------------------------------------
   This computes an in-place complex-to-complex FFT
   x and y are the real and imaginary arrays of 2^m points.
   dir =  1 gives forward transform
   dir = -1 gives reverse transform

     Formula: forward
                  N-1
                  ---
              1   \          - j k 2 pi n / N
      X(n) = ---   >   x(k) e                    = forward transform
              N   /                                n=0..N-1
                  ---
                  k=0

      Formula: reverse
                  N-1
                  ---
                  \          j k 2 pi n / N
      X(n) =       >   x(k) e                    = forward transform
                  /                                n=0..N-1
                  ---
                  k=0
*/
bool FFT(double *x, double *y, int m, int dir)
{
   long nn,i,i1,j,k,i2,l,l1,l2;
   double c1,c2,tx,ty,t1,t2,u1,u2,z;

   /* Calculate the number of points */
   nn = 1;
   for (i=0;i<m;i++)
      nn *= 2;

   /* Do the bit reversal */
   i2 = nn >> 1;
   j = 0;
   for (i=0;i<nn-1;i++) {
      if (i < j) {
         tx = x[i];
         ty = y[i];
         x[i] = x[j];
         y[i] = y[j];
         x[j] = tx;
         y[j] = ty;
      }
      k = i2;
      while (k <= j) {
         j -= k;
         k >>= 1;
      }
      j += k;
   }

   /* Compute the FFT */
   c1 = -1.0;
   c2 = 0.0;
   l2 = 1;
   for (l=0;l<m;l++) {
      l1 = l2;
      l2 <<= 1;
      u1 = 1.0;
      u2 = 0.0;
      for (j=0;j<l1;j++) {
         for (i=j;i<nn;i+=l2) {
            i1 = i + l1;
            t1 = u1 * x[i1] - u2 * y[i1];
            t2 = u1 * y[i1] + u2 * x[i1];
            x[i1] = x[i] - t1;
            y[i1] = y[i] - t2;
            x[i] += t1;
            y[i] += t2;
         }
         z =  u1 * c1 - u2 * c2;
         u2 = u1 * c2 + u2 * c1;
         u1 = z;
      }
      c2 = sqrt((1.0 - c1) / 2.0);
      if (dir == 1)
         c2 = -c2;
      c1 = sqrt((1.0 + c1) / 2.0);
   }

   // Modified by Morgan Lei, 2013-06-18
   // no scaling
#if 1
   /* Scaling for forward transform */
   if (dir == -1) {
      for (i=0;i<nn;i++) {
         x[i] /= (double)nn;
         y[i] /= (double)nn;
      }
   }
#endif

   return true;
}

/*-------------------------------------------------------------------------
    Calculate the closest but lower power of two of a number
    twopm = 2**m <= n
    Return TRUE if 2**m == n
*/
bool Powerof2(int n, int *m, int *twopm)
{
    if (n <= 1) {
        *m = 0;
        *twopm = 1;
        return false;
    }

   *m = 1;
   *twopm = 2;
   do {
      (*m)++;
      (*twopm) *= 2;
   } while (2*(*twopm) <= n);

   if (*twopm != n)
        return false;
    else
        return true;
}

/*-------------------------------------------------------------------------
   Perform a 2D FFT inplace given a complex 2D array
   The direction dir, 1 for forward, -1 for reverse
   The size of the array (nx,ny)
   Return false if there are memory problems or
      the dimensions are not powers of 2
*/
bool FFT2D(COMPLEX_S *c, int nx, int ny, int dir)
{
   int i,j;
   int m,twopm;
   double *real,*imag;

   /* Transform the rows */
   real = mnew double[nx * 2];
   imag = real + nx;
   if (real == NULL)
      return false;
   if (!Powerof2(nx,&m,&twopm) || twopm != nx)
   {
       mdelete(real);
       return false;
   }
   for (j=0;j<ny;j++)
   {
       const int nIndex = j * nx;
      for (i=0;i<nx;i++) {
         real[i] = c[nIndex + i].real;
         imag[i] = c[nIndex + i].imag;
      }
      FFT(real,imag,m,dir);
      for (i=0;i<nx;i++) {
         c[nIndex + i].real = real[i];
         c[nIndex + i].imag = imag[i];
      }
   }
   mdelete(real);

   /* Transform the columns */
   real = mnew double[ny * 2];
   imag = real + ny;
   if (real == NULL)
      return false;
   if (!Powerof2(ny,&m,&twopm) || twopm != ny)
   {
       mdelete(real);
       return false;
   }
   for (i=0;i<nx;i++)
   {
      for (j=0;j<ny;j++) {
         real[j] = c[j * nx + i].real;
         imag[j] = c[j * nx + i].imag;
      }
      FFT(real,imag,m,dir);
      for (j=0;j<ny;j++) {
         c[j * nx + i].real = real[j];
         c[j * nx + i].imag = imag[j];
      }
   }
   mdelete(real);

   return true;
}

bool FFTSize(const int nWidth, const int nHeight, int &nFFTWidth, int &nFFTHeight)
{
    if (0 >= nWidth || 0 >= nHeight || g_bExpired)
    {
        return false;
    }

    // get minimal size is power of 2 and no small than input image's size
    nFFTWidth = 1;
    while (nFFTWidth < nWidth)
    {
        nFFTWidth <<= 1;
    }
    nFFTHeight = 1;
    while (nFFTHeight < nHeight)
    {
        nFFTHeight <<= 1;
    }

    return true;
}

bool FFT2D(const uchar *pucImg, const int nWidth, const int nHeight,
           COMPLEX_S *pstFFT, const int nFFTWidth, const int nFFTHeight)
{
    if (NULL == pucImg || NULL == pstFFT || 0 >= nWidth || 0 >= nHeight
        || 0 >= nFFTWidth || 0 >= nFFTHeight
        || nWidth > nFFTWidth || nHeight > nFFTHeight || g_bExpired)
    {
        return false;
    }

    int nW = 0, nH = 0;
    FFTSize(nWidth, nHeight, nW, nH);
    if (nW != nFFTWidth || nH != nFFTHeight)
    {
        return false;
    }

    // size must be power of 2
    if ((nFFTWidth & (nFFTWidth - 1)) || (nFFTHeight & (nFFTHeight - 1)))
    {
        return false;
    }

    memset(pstFFT, 0, sizeof(pstFFT[0]) * nFFTWidth * nFFTHeight);

#ifdef FFT_NO_OFFSET
    const int nXOffset = 0;
    const int nYOffset = 0;
#else
    const int nXOffset = (nFFTWidth - nWidth) / 2;
    const int nYOffset = (nFFTHeight - nHeight) / 2;
#endif
    // copy image data to real part of complex
    for (int i = 0; i < nHeight; ++i)
    {
        const int nY = i + nYOffset;
        const int nFFTIndex = nY * nFFTWidth;
        const int nIndex = i * nWidth;
        for (int j = 0; j < nWidth; ++j)
        {
            const int nX = j + nXOffset;
            pstFFT[nFFTIndex + nX].real = pucImg[nIndex + j];
        }
    }

    return FFT2D(pstFFT, nFFTWidth, nFFTHeight, 1);
}

bool FFT2D(const double *pdImg, const int nWidth, const int nHeight,
           COMPLEX_S *pstFFT, const int nFFTWidth, const int nFFTHeight)
{
    if (NULL == pdImg || NULL == pstFFT || 0 >= nWidth || 0 >= nHeight
        || 0 >= nFFTWidth || 0 >= nFFTHeight
        || nWidth > nFFTWidth || nHeight > nFFTHeight || g_bExpired)
    {
        return false;
    }

    int nW = 0, nH = 0;
    FFTSize(nWidth, nHeight, nW, nH);
    if (nW != nFFTWidth || nH != nFFTHeight)
    {
        return false;
    }

    // size must be power of 2
    if ((nFFTWidth & (nFFTWidth - 1)) || (nFFTHeight & (nFFTHeight - 1)))
    {
        return false;
    }

    memset(pstFFT, 0, sizeof(pstFFT[0]) * nFFTWidth * nFFTHeight);

#ifdef FFT_NO_OFFSET
    const int nXOffset = 0;
    const int nYOffset = 0;
#else
    const int nXOffset = (nFFTWidth - nWidth) / 2;
    const int nYOffset = (nFFTHeight - nHeight) / 2;
#endif
    // copy image data to real part of complex
    for (int i = 0; i < nHeight; ++i)
    {
        const int nY = i + nYOffset;
        const int nFFTIndex = nY * nFFTWidth;
        const int nIndex = i * nWidth;
        for (int j = 0; j < nWidth; ++j)
        {
            const int nX = j + nXOffset;
            pstFFT[nFFTIndex + nX].real = pdImg[nIndex + j];
        }
    }

    return FFT2D(pstFFT, nFFTWidth, nFFTHeight, 1);
}

// if fft convolve with another, image will have offset(-1, -1) when transformed back
bool FFT2D(COMPLEX_S *pstFFT, const int nFFTWidth, const int nFFTHeight,
           uchar *pucImg, const int nWidth, const int nHeight, const bool bNormailze/* = false*/)
{
    if (NULL == pucImg || NULL == pstFFT || 0 >= nWidth || 0 >= nHeight
        || 0 >= nFFTWidth || 0 >= nFFTHeight
        || nWidth > nFFTWidth || nHeight > nFFTHeight || g_bExpired)
    {
        return false;
    }

    int nW = 0, nH = 0;
    FFTSize(nWidth, nHeight, nW, nH);
    if (nW != nFFTWidth || nH != nFFTHeight)
    {
        return false;
    }

    // size must be power of 2
    if ((nFFTWidth & (nFFTWidth - 1)) || (nFFTHeight & (nFFTHeight - 1)))
    {
        return false;
    }

    // inverse fft
    FFT2D(pstFFT, nFFTWidth, nFFTHeight, -1);

#ifdef FFT_NO_OFFSET
    const int nXOffset = 0;
    const int nYOffset = 0;
#else
    const int nXOffset = (nFFTWidth - nWidth) / 2;
    const int nYOffset = (nFFTHeight - nHeight) / 2;
#endif
    if (bNormailze)
    {
        double dMax = pstFFT[0].real, dMin = pstFFT[0].real;
        for (int i = 0; i < nHeight * nWidth; ++i)
        {
            dMax = max(dMax, pstFFT[i].real);
            dMin = min(dMin, pstFFT[i].real);
        }
        dMax = max(M_EPS, dMax - dMin);

        for (int i = 0; i < nHeight; ++i)
        {
            const int nY = i + nYOffset;
            const int nFFTIndex = nY * nFFTWidth;
            const int nIndex = i * nWidth;
            for (int j = 0; j < nWidth; ++j)
            {
                const int nX = j + nXOffset;
                double dValue = (pstFFT[nFFTIndex + nX].real - dMin) / dMax;
                pucImg[nIndex + j] = (uchar) (255 * dValue);
            }
        }
    }
    else
    {
        for (int i = 0; i < nHeight; ++i)
        {
            const int nY = i + nYOffset;
            const int nFFTIndex = nY * nFFTWidth;
            const int nIndex = i * nWidth;
            for (int j = 0; j < nWidth; ++j)
            {
                const int nX = j + nXOffset;
                pucImg[nIndex + j] = (uchar) pstFFT[nFFTIndex + nX].real;
            }
        }
    }

    return true;
}

bool FFT2Display(const COMPLEX_S *pstFFT, const int nFFTWidth, const int nFFTHeight,
                 uchar *pucTmp, const int nFactor/* = 16*/)
{
    if (NULL == pstFFT || NULL == pucTmp || 0 >= nFFTWidth || 0 >= nFFTHeight || g_bExpired)
    {
        return false;
    }

    // using the data far away from the real maximum to enhance the displaying image
    int nIndex = nFFTWidth / max(1, nFactor);
    if (nFFTWidth > nFFTHeight)
    {
        nIndex = (nFFTHeight / max(1, nFactor)) * nFFTWidth;
    }
    if (0 == nFactor)
    {
        nIndex = 0;
    }

    // get magnitude
    double dMag = sqrt(pstFFT[nIndex].real * pstFFT[nIndex].real
        + pstFFT[nIndex].imag * pstFFT[nIndex].imag);
    const double dMax = log(dMag + 1);
    for (int i = 0; i < nFFTWidth * nFFTHeight; ++i)
    {
        dMag = sqrt(pstFFT[i].real * pstFFT[i].real
            + pstFFT[i].imag * pstFFT[i].imag);
        pucTmp[i] = (uchar) min(255.0, log(dMag + 1) * 255 / dMax);
    }

    // shift FFT
#if 0
    for (int i = 0; i < nFFTHeight / 2; ++i)
    {
        for (int j = 0; j < nFFTWidth / 2; ++j)
        {
            Swap(pucTmp[i * nFFTWidth + j],
                pucTmp[(i + nFFTHeight / 2) * nFFTWidth + nFFTWidth / 2 + j]);
            Swap(pucTmp[i * nFFTWidth + nFFTWidth / 2 + j],
                pucTmp[(i + nFFTHeight / 2) * nFFTWidth + j]);
        }
    }
#else
    FFTShift(pucTmp, nFFTWidth, nFFTHeight);
#endif

    return true;
}

COMPLEX_S Mult(const COMPLEX_S &stC1, const COMPLEX_S stC2)
{
    COMPLEX_S stCom;
    stCom.real = stC1.real * stC2.real - stC1.imag * stC2.imag;
    stCom.imag = stC1.real * stC2.imag + stC1.imag * stC2.real;
    return stCom;
}

COMPLEX_S Mult(const COMPLEX_S &stC1, const double dScale)
{
    COMPLEX_S stCom;
    stCom.real = stC1.real * dScale;
    stCom.imag = stC1.imag * dScale;
    return stCom;
}

COMPLEX_S MultConjugate(const COMPLEX_S &stC1, const COMPLEX_S stC2)
{
    COMPLEX_S stCom;
    stCom.real = stC1.real * stC2.real + stC1.imag * stC2.imag;
    stCom.imag = stC1.imag * stC2.real - stC1.real * stC2.imag;
    return stCom;
}

bool Conv(const COMPLEX_S *pstFFT1, const COMPLEX_S *pstFFT2,
          const int nWidth, const int nHeight, COMPLEX_S *pstResult)
{
    if (NULL == pstFFT1 || NULL == pstFFT2 || NULL == pstResult
        || 0 >= nWidth || 0 >= nHeight || g_bExpired)
    {
        return false;
    }

    for (int i = 0; i < nHeight; ++i)
    {
        const int nIndex = i * nWidth;
        for (int j = 0; j < nWidth; ++j)
        {
            pstResult[nIndex + j] = Mult(pstFFT1[nIndex + j], pstFFT2[nIndex + j]);
        }
    }

    return true;
}

bool Conv(const COMPLEX_S *pstFFT1, const double *pdFilter,
          const int nWidth, const int nHeight, COMPLEX_S *pstResult)
{
    if (NULL == pstFFT1 || NULL == pdFilter || NULL == pstResult
        || 0 >= nWidth || 0 >= nHeight || g_bExpired)
    {
        return false;
    }

    for (int i = 0; i < nHeight; ++i)
    {
        const int nIndex = i * nWidth;
        for (int j = 0; j < nWidth; ++j)
        {
            pstResult[nIndex + j] = Mult(pstFFT1[nIndex + j], pdFilter[nIndex + j]);
        }
    }

    return true;
}

bool Corr(const COMPLEX_S *pstFFT1, const COMPLEX_S *pstFFT2,
          const int nWidth, const int nHeight, COMPLEX_S *pstResult)
{
    if (NULL == pstFFT1 || NULL == pstFFT2 || NULL == pstResult
        || 0 >= nWidth || 0 >= nHeight || g_bExpired)
    {
        return false;
    }

    for (int i = 0; i < nHeight; ++i)
    {
        const int nIndex = i * nWidth;
        for (int j = 0; j < nWidth; ++j)
        {
            pstResult[nIndex + j] = MultConjugate(pstFFT1[nIndex + j], pstFFT2[nIndex + j]);
        }
    }

    return true;
}

bool FFT2DCV(const uchar *pucImg, const int nWidth, const int nHeight, COMPLEX_S *pstFFT)
{
    if (NULL == pucImg || NULL == pstFFT || 0 >= nWidth || 0 >= nHeight || g_bExpired)
    {
        return false;
    }

    // create IplImage and writing input image's data in it
    Mat img;
    img.create(nHeight, nWidth, CV_64FC2);
    for (int i = 0; i < nHeight; ++i)
    {
        const uchar *pucRow = pucImg + i * nWidth;
        double *pdRow = (double *) (img.data + img.step * i);
        for (int j = 0; j < nWidth; ++j)
        {
            *pdRow++ = *pucRow++;
            *pdRow++ = 0.0;
        }
    }

    Mat dst(nHeight, nWidth, CV_64FC2, (void *) pstFFT);
    dft(img, dst, CV_DXT_FORWARD);

    return true;
}

bool FFT2DCV(const double *pdImg, const int nWidth, const int nHeight,
             COMPLEX_S *pstFFT, const int nFFTWidth, const int nFFTHeight)
{
    if (NULL == pdImg || NULL == pstFFT || 0 >= nWidth || 0 >= nHeight
        || 0 >= nFFTWidth || 0 >= nFFTHeight
        || nWidth > nFFTWidth || nHeight > nFFTHeight || g_bExpired)
    {
        return false;
    }

    memset(pstFFT, 0, sizeof(COMPLEX_S) * nFFTWidth * nFFTHeight);

#ifdef FFT_NO_OFFSET
    const int nXOffset = 0;
    const int nYOffset = 0;
#else
    const int nXOffset = (nFFTWidth - nWidth) / 2;
    const int nYOffset = (nFFTHeight - nHeight) / 2;
#endif
    for (int i = 0; i < nHeight; ++i)
    {
        const int nY = i + nYOffset;
        const int nFFTIndex = nY * nFFTWidth;
        const int nIndex = i * nWidth;
        for (int j = 0; j < nWidth; ++j)
        {
            const int nX = j + nXOffset;
            pstFFT[nFFTIndex + nX].real = pdImg[nIndex + j];
        }
    }

    // using pstFFT memory for creating result image and saving result
    Mat dst(nHeight, nWidth, CV_64FC2, (void *) pstFFT);
    dft(dst, dst, CV_DXT_FORWARD);

    return true;
}

bool FFT2DCV(const COMPLEX_S *pstFFT, const int nWidth, const int nHeight,
             uchar *pucImg, const bool bNormailze)
{
    if (NULL == pucImg || NULL == pstFFT || 0 >= nWidth || 0 >= nHeight || g_bExpired)
    {
        return false;
    }

    // using pstFFT memory for creating result image and saving result
    Mat dst(nHeight, nWidth, CV_64FC2, (void *) pstFFT);
    dft(dst, dst, CV_DXT_INVERSE_SCALE);

    if (bNormailze)
    {
        double dMax = pstFFT[0].real, dMin = pstFFT[0].real;
        for (int i = 0; i < nHeight * nWidth; ++i)
        {
            dMax = max(dMax, pstFFT[i].real);
            dMin = min(dMin, pstFFT[i].real);
        }
        dMax = max(M_EPS, dMax - dMin);

        for (int i = 0; i < nHeight; ++i)
        {
            const int nIndex = i * nWidth;
            for (int j = 0; j < nWidth; ++j)
            {
                double dValue = (pstFFT[nIndex + j].real - dMin) / dMax;
                pucImg[nIndex + j] = (uchar) (255 * dValue);
            }
        }
    }
    else
    {
        for (int i = 0; i < nHeight; ++i)
        {
            const int nIndex = i * nWidth;
            for (int j = 0; j < nWidth; ++j)
            {
                pucImg[nIndex + j] = (uchar) pstFFT[nIndex + j].real;
            }
        }
    }

    return true;
}

bool CreateGaussian(double *pdGauss, const int nWidth, const int nHeight,
                    const double dSigmaX, const double dSigmaY)
{
    if (NULL == pdGauss || 0 >= nWidth || 0 >= nHeight
        || 0 >= dSigmaX || 0 >= dSigmaY || g_bExpired)
    {
        return false;
    }

    const int nHalfH = nHeight / 2, nHalfW = nWidth / 2;
    const int nDeltaH = nHeight % 2, nDeltaW = nWidth % 2;
    // image center: SHOULD MINUS 1
    const double dCenterX = (nWidth - 1) / 2.0, dCenterY = (nHeight - 1) / 2.0;

    // 1 2
    // 4 3
    const int nIndexDalta1 = nHalfH * nWidth + nHalfW;
    const int nIndexDalta2 = nHalfH * nWidth - (nWidth - nHalfW);
    const int nIndexDalta3 = (nHeight - nHalfH) * nWidth + (nWidth - nHalfW);
    const int nIndexDalta4 = (nHeight - nHalfH) * nWidth - nHalfW;

    // center coordinates for calculating the maximal response
    const double dX = (nHalfW - dCenterX) * (nHalfW - dCenterX);
    const double dY = (nHalfH - dCenterY) * (nHalfH - dCenterY);
    const double dSigmaX2 = dSigmaX * dSigmaX * 2;
    const double dSigmaY2 = dSigmaY * dSigmaY * 2;

    // same sigma
    if (M_EPS >= abs(dSigmaX - dSigmaY))
    {
        const double dMax = exp(- (dX + dY) / dSigmaX2);
        for (int i = 0; i < nHalfH + nDeltaH; ++i)
        {
            const int nIndex1 = i * nWidth;
            const int nIndex2 = (nHeight - 1 - i) * nWidth;
            const double dY = (i - dCenterY) * (i - dCenterY);
            for (int j = 0; j < nHalfW + nDeltaW; ++j)
            {
                const double dX = (j - dCenterX) * (j - dCenterX);
                const double dValue = exp(- (dX + dY) / dSigmaX2) / dMax;
                pdGauss[nIndex1 + j + nIndexDalta1] = dValue;

                // central line (both vertical and horizontal)
                if (nHalfH != i && nHalfW != j)
                {
                    pdGauss[nIndex1 + (nWidth - 1 - j) + nIndexDalta2] = dValue;
                    pdGauss[nIndex2 + j - nIndexDalta4] = dValue;
                    pdGauss[nIndex2 + (nWidth - 1 - j) - nIndexDalta3] = dValue;
                }
                else if (nHalfW == j && nHalfH != i)
                {
                    pdGauss[nIndex2 + j - nIndexDalta4] = dValue;
                }
                else if (nHalfH == i && nHalfW != j)
                {
                    pdGauss[nIndex1 + (nWidth - 1 - j) + nIndexDalta2] = dValue;
                }
            }
        }
    }
    else
    {
        const double dMax = exp(- dX / dSigmaX2) * exp(- dY / dSigmaY2);
        for (int i = 0; i < nHalfH + nDeltaH; ++i)
        {
            const int nIndex1 = i * nWidth;
            const int nIndex2 = (nHeight - 1 - i) * nWidth;
            const double dY = (i - dCenterY) * (i - dCenterY);
            for (int j = 0; j < nHalfW + nDeltaW; ++j)
            {
                const double dX = (j - dCenterX) * (j - dCenterX);
                const double dValue = exp(- dX / dSigmaX2) * exp(- dY / dSigmaY2) / dMax;
                pdGauss[nIndex1 + j + nIndexDalta1] = dValue;

                // central line (both vertical and horizontal)
                if (nHalfH != i && nHalfW != j)
                {
                    pdGauss[nIndex1 + (nWidth - 1 - j) + nIndexDalta2] = dValue;
                    pdGauss[nIndex2 + j - nIndexDalta4] = dValue;
                    pdGauss[nIndex2 + (nWidth - 1 - j) - nIndexDalta3] = dValue;
                }
                else if (nHalfW == j && nHalfH != i)
                {
                    pdGauss[nIndex2 + j - nIndexDalta4] = dValue;
                }
                else if (nHalfH == i && nHalfW != j)
                {
                    pdGauss[nIndex1 + (nWidth - 1 - j) + nIndexDalta2] = dValue;
                }
            }
        }
    }

    return true;
}

// create ideal band-pass filter in frequency domain (shifted)
// 0 < dLowFreq < 1.0, 0 < dHighFreq < 1.0, dLowFreq < dHighFreq
bool CreateBandpassIdeal(double *pdBandpass, const int nWidth, const int nHeight,
                         const double dLowFreq, const double dHighFreq)
{
    if (NULL == pdBandpass || 0 >= nWidth || 0 >= nHeight
        || 0.0 > dLowFreq || 1.0 < dHighFreq || dLowFreq > dHighFreq || g_bExpired)
    {
        return false;
    }

    const int nHalfH = nHeight / 2, nHalfW = nWidth / 2;
    const int nDeltaH = nHeight % 2, nDeltaW = nWidth % 2;
    // image center: SHOULD MINUS 1
    const double dCenterX = (nWidth - 1) / 2.0, dCenterY = (nHeight - 1) / 2.0;
    const double dMaxDist = dCenterX * dCenterX + dCenterY * dCenterY;
    // multiple dLowFreq twice because of no sqrt
    const double dMinFreq = dMaxDist * dLowFreq * dLowFreq;
    const double dMaxFreq = dMaxDist * dHighFreq * dHighFreq;

    // 1 2
    // 4 3
    const int nIndexDalta1 = nHalfH * nWidth + nHalfW;
    const int nIndexDalta2 = nHalfH * nWidth - (nWidth - nHalfW);
    const int nIndexDalta3 = (nHeight - nHalfH) * nWidth + (nWidth - nHalfW);
    const int nIndexDalta4 = (nHeight - nHalfH) * nWidth - nHalfW;

    for (int i = 0; i < nHalfH + nDeltaH; ++i)
    {
        const int nIndex1 = i * nWidth;
        const int nIndex2 = (nHeight - 1 - i) * nWidth;
        const double dY = (i - dCenterY) * (i - dCenterY);
        for (int j = 0; j < nHalfW + nDeltaW; ++j)
        {
            const double dX = (j - dCenterX) * (j - dCenterX);
            const double dValue = (dX + dY >= dMinFreq && dX + dY <= dMaxFreq) ? 1.0 : 0.0;
            pdBandpass[nIndex1 + j + nIndexDalta1] = dValue;

            // central line (both vertical and horizontal)
            if (nHalfH != i && nHalfW != j)
            {
                pdBandpass[nIndex1 + (nWidth - 1 - j) + nIndexDalta2] = dValue;
                pdBandpass[nIndex2 + j - nIndexDalta4] = dValue;
                pdBandpass[nIndex2 + (nWidth - 1 - j) - nIndexDalta3] = dValue;
            }
            else if (nHalfW == j && nHalfH != i)
            {
                pdBandpass[nIndex2 + j - nIndexDalta4] = dValue;
            }
            else if (nHalfH == i && nHalfW != j)
            {
                pdBandpass[nIndex1 + (nWidth - 1 - j) + nIndexDalta2] = dValue;
            }
        }
    }

    return true;
}

// create Gaussian band-pass filter in frequency domain (shifted)
// 0 < dLowFreq < 1.0, 0 < dHighFreq < 1.0, dLowFreq < dHighFreq
bool CreateBandPassStd(double *pdBandStd, const int nWidth, const int nHeight,
                       const double dLowFreq, const double dHighFreq)
{
    if (NULL == pdBandStd || 0 >= nWidth || 0 >= nHeight
        || 0.0 > dLowFreq || 1.0 < dHighFreq || dLowFreq > dHighFreq || g_bExpired)
    {
        return false;
    }

    const int nHalfH = nHeight / 2, nHalfW = nWidth / 2;
    const int nDeltaH = nHeight % 2, nDeltaW = nWidth % 2;
    // image center: SHOULD MINUS 1
    const double dCenterX = (nWidth - 1) / 2.0, dCenterY = (nHeight - 1) / 2.0;
    const double dMaxDist = dCenterX * dCenterX + dCenterY * dCenterY;
    // multiple dLowFreq twice because of no sqrt
    const double dMinFreq = dMaxDist * dLowFreq * dLowFreq * 2;
    const double dMaxFreq = dMaxDist * dHighFreq * dHighFreq * 2;

    // 1 2
    // 4 3
    const int nIndexDalta1 = nHalfH * nWidth + nHalfW;
    const int nIndexDalta2 = nHalfH * nWidth - (nWidth - nHalfW);
    const int nIndexDalta3 = (nHeight - nHalfH) * nWidth + (nWidth - nHalfW);
    const int nIndexDalta4 = (nHeight - nHalfH) * nWidth - nHalfW;

    for (int i = 0; i < nHalfH + nDeltaH; ++i)
    {
        const int nIndex1 = i * nWidth;
        const int nIndex2 = (nHeight - 1 - i) * nWidth;
        const double dY = (i - dCenterY) * (i - dCenterY);
        for (int j = 0; j < nHalfW + nDeltaW; ++j)
        {
            const double dX = (j - dCenterX) * (j - dCenterX);
            const double dValue = exp(- (dX + dY) * M_PI / dMaxFreq)
                - exp(- (dX + dY) * M_PI / dMinFreq);
            pdBandStd[nIndex1 + j + nIndexDalta1] = dValue;

            // central line (both vertical and horizontal)
            if (nHalfH != i && nHalfW != j)
            {
                pdBandStd[nIndex1 + (nWidth - 1 - j) + nIndexDalta2] = dValue;
                pdBandStd[nIndex2 + j - nIndexDalta4] = dValue;
                pdBandStd[nIndex2 + (nWidth - 1 - j) - nIndexDalta3] = dValue;
            }
            else if (nHalfW == j && nHalfH != i)
            {
                pdBandStd[nIndex2 + j - nIndexDalta4] = dValue;
            }
            else if (nHalfH == i && nHalfW != j)
            {
                pdBandStd[nIndex1 + (nWidth - 1 - j) + nIndexDalta2] = dValue;
            }
        }
    }

    return true;
}

bool FFTCalcAngle(const COMPLEX_S *pstFFT, const int nWidth, const int nHeight, float &fAngle)
{
    if (NULL == pstFFT || 0 >= nWidth || 0 >= nHeight || g_bExpired)
    {
        return false;
    }

    // take a half horizontally
    double *pdMag = mnew double[nWidth * nHeight];
    if (NULL == pdMag)
    {
        return false;
    }

    // calculate magnitude
    for (int i = 0; i < nHeight; ++i)
    {
        const int nIndex = i * nWidth;
        // using only half of them
        for (int j = nWidth / 2; j < nWidth; ++j)
        {
            pdMag[nIndex + j] = pstFFT[nIndex + j].real * pstFFT[nIndex + j].real
                + pstFFT[nIndex + j].imag * pstFFT[nIndex + j].imag;
        }
    }
    FFTShift(pdMag, nWidth, nHeight);

    const int nPiSize = 240;
    const int nStart = 4, nEnd = min(nWidth, nHeight) * 2 / 5;
    double adAngleHist[nPiSize] = {0.0};
    // center should like this, caused by discrete coordinates
    const int nCenterX = nWidth / 2, nCenterY = nHeight / 2;
    for (int i = 0; i < nPiSize; ++i)
    {
        const double dAngle = i * M_PI / nPiSize;
        const double dSin = sin(dAngle);
        const double dCos = cos(dAngle);

        for (int j = nStart; j < nEnd; ++j)
        {
            const double dCol = nCenterX - j * dSin;
            const double dRow = nCenterY - j * dCos;
            const int nX = (int) dCol;
            const int nY = (int) dRow;
            const double dDistX = dCol - nX;
            const double dDistY = dRow - nY;
            const int nIndex = nY * nWidth + nX;

            adAngleHist[i] += pdMag[nIndex] * (1 - dDistX) * (1 - dDistY);
            adAngleHist[i] += pdMag[nIndex + 1] * dDistX * (1 - dDistY);
            adAngleHist[i] += pdMag[nIndex + nWidth] * (1 - dDistX) * dDistY;
            adAngleHist[i] += pdMag[nIndex + nWidth + 1] * dDistX * dDistY;
        }
    }

    SmoothArray(adAngleHist, nPiSize, true);

    const int nIndex = FindMaxIndex(adAngleHist, nPiSize);
    const int nPre = (nIndex - 1 + nPiSize) % nPiSize;
    const int nNext = (nIndex + 1) % nPiSize;
    fAngle = (float) nIndex + GetSubPosition(adAngleHist[nIndex],
        adAngleHist[nPre], adAngleHist[nNext]);
    fAngle = fAngle * 180.0f / nPiSize;

    mdelete(pdMag);
    return true;
}

bool FFTCalcAngle(const uchar *pucImg, const int nWidth, const int nHeight, float &fAngle)
{
    if (NULL == pucImg || 0 >= nWidth || 0 >= nHeight || g_bExpired)
    {
        return false;
    }

    // only using image data in a circle for accuracy
    uchar *pucModified = mnew uchar[nWidth * nHeight];
    memcpy(pucModified, pucImg, sizeof(uchar) * nWidth * nHeight);
    const int nCenterX = nWidth / 2, nCenterY = nHeight / 2;
    const int nEnd = min(nWidth, nHeight) * 2 / 5;
    const int nMaxDist = nEnd * nEnd;
    for (int i = 0; i < nHeight; ++i)
    {
        const int nY = (i - nCenterY) * (i - nCenterY);
        const int nIndex = i * nWidth;

        // whole line is out of circle
        if (nMaxDist <= nY)
        {
            memset(pucModified + nIndex, 0, sizeof(uchar) * nWidth);
            continue;
        }

        for (int j = 0; j < nWidth; ++j)
        {
            int nX = (j - nCenterX) * (j - nCenterX);
            if (nMaxDist <= nX + nY)
            {
                pucModified[nIndex + j] = 0;
            }
        }
    }

    COMPLEX_S *pstFFT = mnew COMPLEX_S[nWidth * nHeight];
    if (NULL == pstFFT)
    {
        mdelete(pucModified);
        return false;
    }

    // FFT
    FFT2DCV(pucModified, nWidth, nHeight, pstFFT);

    bool bOk = FFTCalcAngle(pstFFT, nWidth, nHeight, fAngle);

    mdelete(pucModified);
    mdelete(pstFFT);

    return bOk;
}
