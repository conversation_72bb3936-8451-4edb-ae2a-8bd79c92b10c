/**
 * Performance monitoring utilities
 */

export class PerformanceMonitor {
  constructor() {
    this.timers = new Map();
    this.metrics = new Map();
  }

  /**
   * Start timing an operation
   * @param {string} name - Operation name
   */
  startTimer(name) {
    this.timers.set(name, {
      start: process.hrtime.bigint(),
      memory: process.memoryUsage()
    });
  }

  /**
   * End timing an operation and record metrics
   * @param {string} name - Operation name
   * @returns {object} Performance metrics
   */
  endTimer(name) {
    const timer = this.timers.get(name);
    if (!timer) {
      throw new Error(`Timer '${name}' not found`);
    }

    const end = process.hrtime.bigint();
    const endMemory = process.memoryUsage();
    
    const duration = Number(end - timer.start) / 1000000; // Convert to milliseconds
    const memoryDelta = {
      rss: endMemory.rss - timer.memory.rss,
      heapUsed: endMemory.heapUsed - timer.memory.heapUsed,
      heapTotal: endMemory.heapTotal - timer.memory.heapTotal,
      external: endMemory.external - timer.memory.external
    };

    const metrics = {
      name,
      duration,
      memoryDelta,
      startMemory: timer.memory,
      endMemory
    };

    this.metrics.set(name, metrics);
    this.timers.delete(name);

    return metrics;
  }

  /**
   * Get metrics for a specific operation
   * @param {string} name - Operation name
   * @returns {object|null} Metrics or null if not found
   */
  getMetrics(name) {
    return this.metrics.get(name) || null;
  }

  /**
   * Get all recorded metrics
   * @returns {Map} All metrics
   */
  getAllMetrics() {
    return new Map(this.metrics);
  }

  /**
   * Clear all metrics and timers
   */
  clear() {
    this.timers.clear();
    this.metrics.clear();
  }

  /**
   * Format metrics for display
   * @param {string} name - Operation name
   * @returns {string} Formatted metrics string
   */
  formatMetrics(name) {
    const metrics = this.getMetrics(name);
    if (!metrics) {
      return `No metrics found for '${name}'`;
    }

    const memoryMB = (bytes) => (bytes / 1024 / 1024).toFixed(2);
    
    return `
Performance Metrics for '${name}':
  Duration: ${metrics.duration.toFixed(2)}ms
  Memory Usage:
    RSS Delta: ${memoryMB(metrics.memoryDelta.rss)}MB
    Heap Used Delta: ${memoryMB(metrics.memoryDelta.heapUsed)}MB
    Heap Total Delta: ${memoryMB(metrics.memoryDelta.heapTotal)}MB
    External Delta: ${memoryMB(metrics.memoryDelta.external)}MB
  Final Memory:
    RSS: ${memoryMB(metrics.endMemory.rss)}MB
    Heap Used: ${memoryMB(metrics.endMemory.heapUsed)}MB
    Heap Total: ${memoryMB(metrics.endMemory.heapTotal)}MB
    External: ${memoryMB(metrics.endMemory.external)}MB`;
  }

  /**
   * Log metrics to console
   * @param {string} name - Operation name
   */
  logMetrics(name) {
    console.log(this.formatMetrics(name));
  }

  /**
   * Get summary of all metrics
   * @returns {object} Summary statistics
   */
  getSummary() {
    const allMetrics = Array.from(this.metrics.values());
    if (allMetrics.length === 0) {
      return { totalOperations: 0 };
    }

    const totalDuration = allMetrics.reduce((sum, m) => sum + m.duration, 0);
    const avgDuration = totalDuration / allMetrics.length;
    const maxDuration = Math.max(...allMetrics.map(m => m.duration));
    const minDuration = Math.min(...allMetrics.map(m => m.duration));

    const totalMemoryUsed = allMetrics.reduce((sum, m) => sum + m.memoryDelta.heapUsed, 0);
    const avgMemoryUsed = totalMemoryUsed / allMetrics.length;

    return {
      totalOperations: allMetrics.length,
      totalDuration: totalDuration.toFixed(2),
      avgDuration: avgDuration.toFixed(2),
      maxDuration: maxDuration.toFixed(2),
      minDuration: minDuration.toFixed(2),
      totalMemoryUsed: (totalMemoryUsed / 1024 / 1024).toFixed(2),
      avgMemoryUsed: (avgMemoryUsed / 1024 / 1024).toFixed(2)
    };
  }
}

// Global performance monitor instance
export const globalPerformanceMonitor = new PerformanceMonitor();

/**
 * Decorator function for timing method execution
 * @param {string} name - Timer name (optional, defaults to method name)
 */
export function timed(name) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value;
    const timerName = name || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = function(...args) {
      globalPerformanceMonitor.startTimer(timerName);
      try {
        const result = originalMethod.apply(this, args);
        
        // Handle async methods
        if (result && typeof result.then === 'function') {
          return result.finally(() => {
            globalPerformanceMonitor.endTimer(timerName);
          });
        }
        
        globalPerformanceMonitor.endTimer(timerName);
        return result;
      } catch (error) {
        globalPerformanceMonitor.endTimer(timerName);
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Simple timing function for standalone operations
 * @param {string} name - Operation name
 * @param {Function} fn - Function to time
 * @returns {Promise|any} Function result
 */
export async function timeOperation(name, fn) {
  globalPerformanceMonitor.startTimer(name);
  try {
    const result = await fn();
    globalPerformanceMonitor.endTimer(name);
    return result;
  } catch (error) {
    globalPerformanceMonitor.endTimer(name);
    throw error;
  }
}
