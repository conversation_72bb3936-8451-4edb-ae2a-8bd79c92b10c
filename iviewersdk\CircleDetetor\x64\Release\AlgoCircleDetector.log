﻿C:\Program Files (x86)\MSBuild\Microsoft.Cpp\v4.0\V140\Microsoft.CppBuild.targets(368,5): warning MSB8004: Output Directory does not end with a trailing slash.  This build instance will add the slash as it is required to allow proper evaluation of the Output Directory.
  AlgoCircleDetector.cpp
d:\code\iviewersdk\circledetetor\AlgInterface.h(200): warning C4267: “return”: 从“size_t”转换到“int”，可能丢失数据
d:\code\iviewersdk\circledetetor\AlgInterface.h(223): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
d:\code\iviewersdk\circledetetor\AlgInterface.h(237): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
d:\code\iviewersdk\circledetetor\AlgInterface.h(370): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “bitsPerPixel”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “bytesPerLine”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “height”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “width”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “image”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(375): warning C4100: “typeOption”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(375): warning C4100: “usage”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(375): warning C4100: “description”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(375): warning C4100: “name”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(375): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(376): warning C4100: “regions”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(380): warning C4100: “param”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(380): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(381): warning C4100: “param”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(381): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(385): warning C4100: “description”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(385): warning C4100: “name”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(385): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(386): warning C4100: “pathName”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(386): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(390): warning C4100: “type”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(390): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(391): warning C4100: “referenceInput”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(391): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(398): warning C4100: “resultItem”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(398): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “scale”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “refY”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “refX”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “angle”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “y”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “x”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “scale”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “refY”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “refX”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “angle”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “y”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “x”: 未引用的形参
C:\Program Files (x86)\Windows Kits\10\Include\10.0.10240.0\ucrt\math.h(1001): warning C4005: “M_PI”: 宏重定义
  d:\code\iviewersdk\circledetetor\./AlgoInterface.h(43): note: 参见“M_PI”的前一个定义
  AlgoInterface.cpp
AlgoInterface.cpp(2226): warning C4267: “初始化”: 从“size_t”转换到“uint”，可能丢失数据
AlgoInterface.cpp(2300): warning C4267: “初始化”: 从“size_t”转换到“uint”，可能丢失数据
  CircleDetector.cpp
d:\code\iviewersdk\circledetetor\AlgInterface.h(200): warning C4267: “return”: 从“size_t”转换到“int”，可能丢失数据
d:\code\iviewersdk\circledetetor\AlgInterface.h(223): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
d:\code\iviewersdk\circledetetor\AlgInterface.h(237): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
d:\code\iviewersdk\circledetetor\AlgInterface.h(370): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “bitsPerPixel”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “bytesPerLine”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “height”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “width”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “image”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(375): warning C4100: “typeOption”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(375): warning C4100: “usage”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(375): warning C4100: “description”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(375): warning C4100: “name”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(375): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(376): warning C4100: “regions”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(380): warning C4100: “param”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(380): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(381): warning C4100: “param”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(381): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(385): warning C4100: “description”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(385): warning C4100: “name”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(385): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(386): warning C4100: “pathName”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(386): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(390): warning C4100: “type”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(390): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(391): warning C4100: “referenceInput”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(391): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(398): warning C4100: “resultItem”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(398): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “scale”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “refY”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “refX”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “angle”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “y”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “x”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “scale”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “refY”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “refX”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “angle”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “y”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “x”: 未引用的形参
C:\Program Files (x86)\Windows Kits\10\Include\10.0.10240.0\ucrt\math.h(1001): warning C4005: “M_PI”: 宏重定义
  d:\code\iviewersdk\circledetetor\./AlgoInterface.h(43): note: 参见“M_PI”的前一个定义
  CircleDetectorCore.cpp
d:\code\iviewersdk\circledetetor\AlgInterface.h(200): warning C4267: “return”: 从“size_t”转换到“int”，可能丢失数据
d:\code\iviewersdk\circledetetor\AlgInterface.h(223): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
d:\code\iviewersdk\circledetetor\AlgInterface.h(237): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
d:\code\iviewersdk\circledetetor\AlgInterface.h(370): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “bitsPerPixel”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “bytesPerLine”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “height”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “width”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “image”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(375): warning C4100: “typeOption”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(375): warning C4100: “usage”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(375): warning C4100: “description”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(375): warning C4100: “name”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(375): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(376): warning C4100: “regions”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(380): warning C4100: “param”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(380): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(381): warning C4100: “param”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(381): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(385): warning C4100: “description”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(385): warning C4100: “name”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(385): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(386): warning C4100: “pathName”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(386): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(390): warning C4100: “type”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(390): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(391): warning C4100: “referenceInput”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(391): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(398): warning C4100: “resultItem”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(398): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “scale”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “refY”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “refX”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “angle”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “y”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “x”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “scale”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “refY”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “refX”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “angle”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “y”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “x”: 未引用的形参
CircleDetectorCore.cpp(266): warning C4100: “description”: 未引用的形参
CircleDetectorCore.cpp(266): warning C4100: “name”: 未引用的形参
CircleDetectorCore.cpp(266): warning C4100: “i”: 未引用的形参
CircleDetectorCore.cpp(271): warning C4100: “pathName”: 未引用的形参
CircleDetectorCore.cpp(271): warning C4100: “i”: 未引用的形参
CircleDetectorCore.cpp(281): warning C4100: “type”: 未引用的形参
CircleDetectorCore.cpp(281): warning C4100: “i”: 未引用的形参
CircleDetectorCore.cpp(286): warning C4100: “referenceInput”: 未引用的形参
CircleDetectorCore.cpp(286): warning C4100: “i”: 未引用的形参
CircleDetectorCore.cpp(362): warning C4100: “scale”: 未引用的形参
CircleDetectorCore.cpp(362): warning C4100: “refY”: 未引用的形参
CircleDetectorCore.cpp(362): warning C4100: “refX”: 未引用的形参
CircleDetectorCore.cpp(362): warning C4100: “angle”: 未引用的形参
CircleDetectorCore.cpp(362): warning C4100: “y”: 未引用的形参
CircleDetectorCore.cpp(362): warning C4100: “x”: 未引用的形参
CircleDetectorCore.cpp(937): warning C4267: “参数”: 从“size_t”转换到“unsigned int”，可能丢失数据
CircleDetectorCore.cpp(990): warning C4457: “circle”的声明隐藏了函数参数
  CircleDetectorCore.cpp(914): note: 参见“circle”的声明
  EdgeDetector.cpp
d:\code\iviewersdk\circledetetor\AlgInterface.h(200): warning C4267: “return”: 从“size_t”转换到“int”，可能丢失数据
d:\code\iviewersdk\circledetetor\AlgInterface.h(223): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
d:\code\iviewersdk\circledetetor\AlgInterface.h(237): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
d:\code\iviewersdk\circledetetor\AlgInterface.h(370): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “bitsPerPixel”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “bytesPerLine”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “height”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “width”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “image”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(371): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(375): warning C4100: “typeOption”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(375): warning C4100: “usage”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(375): warning C4100: “description”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(375): warning C4100: “name”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(375): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(376): warning C4100: “regions”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(380): warning C4100: “param”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(380): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(381): warning C4100: “param”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(381): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(385): warning C4100: “description”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(385): warning C4100: “name”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(385): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(386): warning C4100: “pathName”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(386): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(390): warning C4100: “type”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(390): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(391): warning C4100: “referenceInput”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(391): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(398): warning C4100: “resultItem”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(398): warning C4100: “i”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “scale”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “refY”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “refX”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “angle”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “y”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(402): warning C4100: “x”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “scale”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “refY”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “refX”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “angle”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “y”: 未引用的形参
d:\code\iviewersdk\circledetetor\AlgInterface.h(403): warning C4100: “x”: 未引用的形参
EdgeDetector.cpp(1494): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
    正在创建库 ../../lib/x64/Release/AlgoCircleDetector.lib 和对象 ../../lib/x64/Release/AlgoCircleDetector.exp
  正在生成代码
d:\code\iviewersdk\circledetetor\edgedetector.cpp(1263): warning C4701: 使用了可能未初始化的局部变量“fDx”
d:\code\iviewersdk\circledetetor\edgedetector.cpp(1264): warning C4701: 使用了可能未初始化的局部变量“fDy”
d:\code\iviewersdk\circledetetor\edgedetector.cpp(1169): warning C4701: 使用了可能未初始化的局部变量“fDx”
d:\code\iviewersdk\circledetetor\edgedetector.cpp(1170): warning C4701: 使用了可能未初始化的局部变量“fDy”
  All 1635 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  已完成代码的生成
  AlgoCircleDetector.vcxproj -> D:\code\iviewersdk\CircleDetetor\../../output/x64/Release\AlgoCircleDetector.dll
  AlgoCircleDetector.vcxproj -> AlgoCircleDetector.pdb (Full PDB)
