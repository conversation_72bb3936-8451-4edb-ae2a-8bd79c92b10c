/**
 * Complete replication of iViewerSDK segmentation algorithms
 * This module exactly replicates the ExtractLabel and ExtractMacro functions from iViewerSDK
 */

import { timeOperation } from './performance.js';

/**
 * Smooth array function (replicated from iViewerSDK)
 * @param {number[]} array - Array to smooth
 * @param {number} length - Length to process
 * @param {boolean} circular - Whether to treat as circular
 */
function smoothArray(array, length, circular = false) {
  // Simple smoothing implementation
  const temp = new Array(length);
  for (let i = 0; i < length; i++) {
    temp[i] = array[i];
  }
  
  for (let i = 1; i < length - 1; i++) {
    array[i] = Math.floor((temp[i - 1] + temp[i] + temp[i + 1]) / 3);
  }
}

/**
 * Down sample and convert to grayscale (replicated from iViewerSDK DownSampleGray_)
 * @param {Buffer} imageBuffer - Input image buffer
 * @param {Buffer} outputBuffer - Output grayscale buffer
 * @param {number} width - Image width
 * @param {number} height - Image height
 * @param {number} channels - Number of channels
 * @param {number} factor - Downsampling factor
 */
function downSampleGray(imageBuffer, outputBuffer, width, height, channels, factor) {
  if (factor === 1) {
    // Copy grayscale data directly
    copyGrayData(imageBuffer, outputBuffer, width, height, channels, 0, 0, width, height);
    return;
  }

  const subWidth = Math.floor(width / factor);
  const subHeight = Math.floor(height / factor);
  
  let outIndex = 0;
  
  if (channels === 1) {
    // Already grayscale
    for (let i = 0; i < subHeight; i++) {
      for (let j = 0; j < subWidth; j++) {
        const srcIndex = (i * factor) * width + (j * factor);
        outputBuffer[outIndex++] = imageBuffer[srcIndex];
      }
    }
  } else {
    // Convert RGB to grayscale using iViewerSDK formula
    for (let i = 0; i < subHeight; i++) {
      for (let j = 0; j < subWidth; j++) {
        const srcIndex = ((i * factor) * width + (j * factor)) * channels;
        const r = imageBuffer[srcIndex];
        const g = imageBuffer[srcIndex + 1];
        const b = imageBuffer[srcIndex + 2];
        
        // iViewerSDK grayscale conversion: (306*B + 601*G + 117*R + 512) >> 10
        const gray = Math.floor((306 * b + 601 * g + 117 * r + 512) >> 10);
        outputBuffer[outIndex++] = Math.min(255, Math.max(0, gray));
      }
    }
  }
}

/**
 * Copy grayscale data (replicated from iViewerSDK CopyGrayData_)
 * @param {Buffer} inputBuffer - Input buffer
 * @param {Buffer} outputBuffer - Output buffer
 * @param {number} width - Image width
 * @param {number} height - Image height
 * @param {number} channels - Number of channels
 * @param {number} left - Left boundary
 * @param {number} top - Top boundary
 * @param {number} right - Right boundary
 * @param {number} bottom - Bottom boundary
 */
function copyGrayData(inputBuffer, outputBuffer, width, height, channels, left, top, right, bottom) {
  let outIndex = 0;
  
  if (channels === 1) {
    // Already grayscale
    for (let i = top; i < bottom; i++) {
      for (let j = left; j < right; j++) {
        const srcIndex = i * width + j;
        outputBuffer[outIndex++] = inputBuffer[srcIndex];
      }
    }
  } else {
    // Convert RGB to grayscale
    for (let i = top; i < bottom; i++) {
      for (let j = left; j < right; j++) {
        const srcIndex = (i * width + j) * channels;
        const r = inputBuffer[srcIndex];
        const g = inputBuffer[srcIndex + 1];
        const b = inputBuffer[srcIndex + 2];
        
        // iViewerSDK grayscale conversion
        const gray = Math.floor((306 * b + 601 * g + 117 * r + 512) >> 10);
        outputBuffer[outIndex++] = Math.min(255, Math.max(0, gray));
      }
    }
  }
}

/**
 * Extract macro region (exact replication of iViewerSDK ExtractMacro)
 * @param {Buffer} imageBuffer - Input image buffer
 * @param {number} width - Image width
 * @param {number} height - Image height
 * @param {number} channels - Number of channels (1, 3, or 4)
 * @returns {Promise<object>} Extracted macro data
 */
export async function extractMacro(imageBuffer, width, height, channels) {
  return await timeOperation('iViewerSDK_ExtractMacro', async () => {
    // Validate input parameters (from iViewerSDK)
    if (!imageBuffer || width <= 0 || height <= 0 || 
        (channels !== 1 && channels !== 3 && channels !== 4)) {
      throw new Error('Invalid input parameters for ExtractMacro');
    }

    // Fixed scale factor (from iViewerSDK)
    const nScale = 4;
    const nSubW = Math.floor(width / nScale);
    const nSubH = Math.floor(height / nScale);
    
    // Allocate grayscale buffer
    const grayBuffer = Buffer.alloc(nSubW * nSubH);
    
    // Down sample to grayscale
    downSampleGray(imageBuffer, grayBuffer, width, height, channels, nScale);
    
    // Allocate column projection array
    const colProjections = new Array(nSubW).fill(0);
    
    // Calculate column projections (from iViewerSDK)
    const nStart = Math.max(8, Math.floor(nSubW / 8));
    const nEnd = nSubW - nStart;
    
    for (let i = Math.floor(nSubH / 8); i < nSubH - Math.floor(nSubH / 8); i++) {
      for (let j = nStart; j < nEnd; j++) {
        const currentIndex = i * nSubW + j;
        const nextIndex = i * nSubW + j + 1;
        colProjections[j] += grayBuffer[currentIndex] - grayBuffer[nextIndex];
      }
    }
    
    // Smooth the array (from iViewerSDK)
    smoothArray(colProjections, nEnd, false);
    
    // Find the maximum (from iViewerSDK)
    let nMaxIndex = 0;
    let nMaxValue = 0;
    for (let j = nStart; j < nEnd; j++) {
      colProjections[j] = Math.abs(colProjections[j]);
      if (colProjections[j] > nMaxValue) {
        nMaxValue = colProjections[j];
        nMaxIndex = j;
      }
    }
    
    // Decide range (from iViewerSDK)
    let nLeft, nRight;
    if (nMaxIndex > nSubW / 2) {
      // Label is on right, macro is on left
      nLeft = 0;
      nRight = (nMaxIndex - 1) * nScale;
    } else {
      // Label is on left, macro is on right
      nLeft = (nMaxIndex + 1) * nScale;
      nRight = width;
    }
    
    const macroWidth = nRight - nLeft;
    const macroHeight = height;
    
    // Extract macro region
    const macroBuffer = Buffer.alloc(macroWidth * macroHeight * channels);
    
    for (let i = 0; i < height; i++) {
      const srcOffset = i * width * channels + nLeft * channels;
      const dstOffset = i * macroWidth * channels;
      const copyLength = macroWidth * channels;
      
      imageBuffer.copy(macroBuffer, dstOffset, srcOffset, srcOffset + copyLength);
    }
    
    return {
      buffer: macroBuffer,
      width: macroWidth,
      height: macroHeight,
      boundary: nMaxIndex * nScale,
      algorithm: 'iViewerSDK_ExtractMacro'
    };
  });
}

/**
 * Extract label region (exact replication of iViewerSDK ExtractLabel)
 * @param {Buffer} imageBuffer - Input image buffer
 * @param {number} width - Image width
 * @param {number} height - Image height
 * @param {number} channels - Number of channels (1, 3, or 4)
 * @returns {Promise<object>} Extracted label data
 */
export async function extractLabel(imageBuffer, width, height, channels) {
  return await timeOperation('iViewerSDK_ExtractLabel', async () => {
    // Validate input parameters (from iViewerSDK)
    if (!imageBuffer || width <= 0 || height <= 0 ||
        (channels !== 1 && channels !== 3 && channels !== 4)) {
      throw new Error('Invalid input parameters for ExtractLabel');
    }

    // Calculate scale factor (from iViewerSDK)
    let nScale = 1;
    let nHTmp = height;
    while (nHTmp >= 256) {
      nHTmp = Math.floor(height / nScale);
      nScale++;
    }
    if (nScale > 1) {
      nScale--;
    }

    const nSubW = Math.floor(width / nScale);
    const nSubH = Math.floor(height / nScale);

    // Allocate grayscale buffer (double size for binary buffer)
    const grayBuffer = Buffer.alloc(nSubW * nSubH * 2);
    const binaryBuffer = grayBuffer.subarray(nSubW * nSubH);

    // Down sample to grayscale
    downSampleGray(imageBuffer, grayBuffer, width, height, channels, nScale);

    // Allocate column projection arrays (from iViewerSDK)
    const colProjections = new Array(nSubW * 2).fill(0);
    const breakArray = colProjections.slice(nSubW); // Second half for break values

    // Initialize break array (from iViewerSDK)
    const nLeftStop = Math.floor(nSubW * 45 / 100);
    for (let i = 0; i <= nLeftStop; i++) {
      breakArray[i] = nLeftStop - i + 2;
    }
    for (let i = nSubW - nLeftStop; i < nSubW; i++) {
      breakArray[i] = nLeftStop - nSubW + i + 2;
    }

    // Calculate column projections (from iViewerSDK)
    const nStart = Math.max(8, Math.floor(nSubW / 8));
    const nEnd = nSubW - nStart;

    for (let i = Math.floor(nSubH / 8); i < nSubH - Math.floor(nSubH / 8); i++) {
      for (let j = nStart; j < nEnd; j++) {
        const currentIndex = i * nSubW + j;
        const nextIndex = i * nSubW + j + 1;
        colProjections[j] += grayBuffer[currentIndex] - grayBuffer[nextIndex];
      }
    }

    // Smooth the array (from iViewerSDK)
    smoothArray(colProjections, nEnd, false);

    // Find the maximum (from iViewerSDK)
    let nMaxIndex = 0;
    let nMaxValue = 0;
    for (let j = nStart; j < nEnd; j++) {
      colProjections[j] = Math.abs(colProjections[j]);
      if (colProjections[j] > nMaxValue) {
        nMaxValue = colProjections[j];
        nMaxIndex = j;
      }
    }

    // Decide left or right (from iViewerSDK)
    const nTh = Math.floor(nMaxValue / 4);
    const nThLow = Math.floor(nMaxValue / 10);
    let nLeftRes = 0;
    let nRightRes = 0;

    for (let i = 0; i < Math.floor(nSubW / 3); i++) {
      nLeftRes += colProjections[i];
      nRightRes += colProjections[nSubW - i - 1];
    }

    const bLeft = nLeftRes > nRightRes;
    const nDelta = bLeft ? 1 : -1;
    let nPos = nMaxIndex;
    let nBreak = 0;

    // Refine boundary position (from iViewerSDK)
    while (nBreak < breakArray[nMaxIndex]) {
      if (colProjections[nPos] > nTh) {
        nBreak = 0;
        nMaxIndex = nPos;
      } else {
        nBreak++;
      }
      nPos += nDelta;
    }

    // Decide range (from iViewerSDK)
    let nLeft, nRight;
    if (!bLeft) {
      // Label is on right
      nLeft = (nMaxIndex + 1) * nScale;
      nRight = width;
    } else {
      // Label is on left
      nLeft = 0;
      nRight = (nMaxIndex - 1) * nScale;
    }

    const labelWidth = nRight - nLeft;
    const labelHeight = height;

    // Extract label region
    const labelBuffer = Buffer.alloc(labelWidth * labelHeight * channels);

    for (let i = 0; i < height; i++) {
      const srcOffset = i * width * channels + nLeft * channels;
      const dstOffset = i * labelWidth * channels;
      const copyLength = labelWidth * channels;

      imageBuffer.copy(labelBuffer, dstOffset, srcOffset, srcOffset + copyLength);
    }

    return {
      buffer: labelBuffer,
      width: labelWidth,
      height: labelHeight,
      boundary: nMaxIndex * nScale,
      isLeft: bLeft,
      algorithm: 'iViewerSDK_ExtractLabel'
    };
  });
}
