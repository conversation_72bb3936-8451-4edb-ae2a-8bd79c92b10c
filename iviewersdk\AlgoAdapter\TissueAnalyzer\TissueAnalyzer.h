#ifndef HX_TISSUE_ANALYZER_H__
#define HX_TISSUE_ANALYZER_H__

#ifdef ALGO_ADAPTER
#define TISSUE_DLL_API __declspec(dllexport)
#else
#define TISSUE_DLL_API __declspec(dllimport)
#endif

//#include "interface/IPathologyAnalyzer.h"


#include <vector>
#include <string>
#include <algorithm>
#include <mutex>
#include "opencv2/opencv.hpp"

class TissueReport;

class TISSUE_DLL_API TissueAnalyzer
{
public:
   
    ~TissueAnalyzer();
	static std::shared_ptr<TissueAnalyzer> instance();

	bool run(const cv::Mat &imgOrigin, const float fStepX, const float fStepY, cv::Rect &rect,
		cv::Mat &memMap, cv::Mat &memColor, int &nWidth, int &nHeight, int &nType, std::string fileName="");
private:
	TissueAnalyzer();
	static std::shared_ptr<TissueAnalyzer> m_ins;
	static std::mutex m_mutex;



};

#endif
