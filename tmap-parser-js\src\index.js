/**
 * TMAP Parser JS - Main Entry Point
 * Node.js implementation of TMAP file parser
 */

import { Tmap6Parser } from './parsers/tmap6-parser.js';
import { globalPerformanceMonitor } from './utils/performance.js';

export { Tmap6Parser };
export { PerformanceMonitor, globalPerformanceMonitor } from './utils/performance.js';
export { <PERSON>ufferReader } from './utils/buffer-reader.js';
export * from './structures/tmap6-structures.js';

/**
 * Main TMAP Parser class that auto-detects version and uses appropriate parser
 */
export class TmapParser {
  constructor() {
    this.supportedVersions = ['6.0', '6.1', '6.2', '6.3', '6.4', '6.5', '6.6', '6.7', '6.8', '6.9'];
  }

  /**
   * Parse TMAP file with automatic version detection
   * @param {string} filePath - Path to TMAP file
   * @returns {Promise<object>} Parsed file information
   */
  async parseFile(filePath) {
    globalPerformanceMonitor.startTimer('TmapParser.parseFile');
    
    try {
      // For now, we only support TMAP6, but this can be extended
      const parser = new Tmap6Parser();
      const result = await parser.parseFile(filePath);
      
      globalPerformanceMonitor.endTimer('TmapParser.parseFile');
      return result;
    } catch (error) {
      globalPerformanceMonitor.endTimer('TmapParser.parseFile');
      throw error;
    }
  }

  /**
   * Get supported TMAP versions
   * @returns {string[]} Array of supported version strings
   */
  getSupportedVersions() {
    return [...this.supportedVersions];
  }

  /**
   * Check if a version is supported
   * @param {string} version - Version string (e.g., "6.0")
   * @returns {boolean} True if supported
   */
  isVersionSupported(version) {
    return this.supportedVersions.includes(version);
  }
}

/**
 * Convenience function to parse a TMAP file
 * @param {string} filePath - Path to TMAP file
 * @returns {Promise<object>} Parsed file information
 */
export async function parseTmapFile(filePath) {
  const parser = new TmapParser();
  return await parser.parseFile(filePath);
}

/**
 * Get file information without full parsing (header only)
 * @param {string} filePath - Path to TMAP file
 * @returns {Promise<object>} Basic file information
 */
export async function getTmapFileInfo(filePath) {
  globalPerformanceMonitor.startTimer('getTmapFileInfo');
  
  try {
    const parser = new Tmap6Parser();
    
    // Read only header for quick info
    const fs = await import('fs/promises');
    const headerBuffer = Buffer.alloc(1024);
    const fileHandle = await fs.open(filePath, 'r');
    
    try {
      await fileHandle.read(headerBuffer, 0, 1024, 0);
      
      const { BufferReader } = await import('./utils/buffer-reader.js');
      const reader = new BufferReader(headerBuffer);
      
      await parser.parseHeader(reader);
      
      const basicInfo = {
        filePath,
        version: parser.header.version,
        versionString: `${String.fromCharCode(parser.header.mainVersion[1])}.${String.fromCharCode(parser.header.mainVersion[0])}`,
        totalWidth: parser.header.totalImgWidth,
        totalHeight: parser.header.totalImgHeight,
        scanMagnification: parser.header.maxZoomRate,
        pixelSize: parser.header.pixelSize,
        slideType: parser.getSlideTypeName(parser.header.slideType),
        colorDepth: parser.header.imgColor
      };
      
      globalPerformanceMonitor.endTimer('getTmapFileInfo');
      return basicInfo;
    } finally {
      await fileHandle.close();
    }
  } catch (error) {
    globalPerformanceMonitor.endTimer('getTmapFileInfo');
    throw error;
  }
}

// Default export
export default TmapParser;
