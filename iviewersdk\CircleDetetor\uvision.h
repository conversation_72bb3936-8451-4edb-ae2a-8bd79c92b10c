// Common definitions for vision projects
// Copyright (C) UNIC Technologies. All rights reserved.
// history:
// (1) 20131107: <NAME_EMAIL>
// (2) 20140417: added some functions to ULineF by steve
// (3) 20140419: added boundingRect functions by steve

#ifndef U_VISION_H
#define U_VISION_H

#include <vector>
#define _USE_MATH_DEFINES
#include <math.h>

#pragma warning(disable : 4244 4996)


template <typename T> inline const T &uMin(const T &a, const T &b) { return (a < b) ? a : b; }
template <typename T> inline const T &uMax(const T &a, const T &b) { return (a < b) ? b : a; }
template <typename T> inline const T &uBound(const T &min, const T &val, const T &max) { return uMax(min, uMin(max, val)); }
template <typename T> inline T uAbs(const T &t) { return t >= 0 ? t : -t; }
template <typename T> inline void uSwap(T &value1, T &value2) { const T t = value1; value1 = value2; value2 = t; }

inline int uRound(float d) { return d >= 0 ? int(d + 0.5f) : int(d - int(d-1) + 0.5f) + int(d-1); }
inline int uRound(double d) { return d >= 0 ? int(d + 0.5) : int(d - int(d-1) + 0.5) + int(d-1); }
inline bool uFuzzyCompare(double p1, double p2) { return (uAbs(p1 - p2) * 1000000000000. <= uMin(uAbs(p1), uAbs(p2))); }
inline bool uFuzzyCompare(float p1, float p2) { return (uAbs(p1 - p2) * 100000.f <= uMin(uAbs(p1), uAbs(p2))); }
inline bool uFuzzyIsNull(double d) { return uAbs(d) <= 0.000000000001; }
inline bool uFuzzyIsNull(float f) { return uAbs(f) <= 0.00001f; }

class UPoint {
public:
	UPoint() : m_x(0), m_y(0) {}
	UPoint(int x, int y) : m_x(x), m_y(y) {}
	int &rx() { return m_x; }
	int &ry() { return m_y; }
	void setX(int x) { m_x = x; }
	void setY(int y) { m_y = y; }
	int x() const { return m_x; }
	int y() const { return m_y; }

	UPoint &operator+=(const UPoint &p) { m_x += p.m_x; m_y += p.m_y; return *this; }
	UPoint &operator-=(const UPoint &p) { m_x -= p.m_x; m_y -= p.m_y; return *this; }
	UPoint &operator*=(int c) { m_x = m_x*c; m_y = m_y*c; return *this; }
	UPoint &operator*=(float c) { m_x = uRound(m_x*c); m_y = uRound(m_y*c); return *this; }
	UPoint &operator*=(double c) { m_x = uRound(m_x*c); m_y = uRound(m_y*c); return *this; }
	UPoint &operator/=(int c) { m_x = m_x/c; m_y = m_y/c; return *this; }
	UPoint &operator/=(float c) { m_x = uRound(m_x/c); m_y = uRound(m_y/c); return *this; }
	UPoint &operator/=(double c) { m_x = uRound(m_x/c); m_y = uRound(m_y/c); return *this; }

	friend bool operator==(const UPoint &p1, const UPoint &p2) { return p1.m_x == p2.m_x && p1.m_y == p2.m_y; }
	friend bool operator!=(const UPoint &p1, const UPoint &p2) { return p1.m_x != p2.m_x || p1.m_y != p2.m_y; }
	friend const UPoint operator+(const UPoint &p1, const UPoint &p2) { return UPoint(p1.m_x+p2.m_x, p1.m_y+p2.m_y); }
	friend const UPoint operator-(const UPoint &p1, const UPoint &p2) { return UPoint(p1.m_x-p2.m_x, p1.m_y-p2.m_y); }
	friend const UPoint operator*(const UPoint &p, int c) { return UPoint(p.m_x*c, p.m_y*c); }
	friend const UPoint operator*(int c, const UPoint &p) { return UPoint(p.m_x*c, p.m_y*c); }
	friend const UPoint operator*(const UPoint &p, float c) { return UPoint(uRound(p.m_x*c), uRound(p.m_y*c)); }
	friend const UPoint operator*(float c, const UPoint &p) { return UPoint(uRound(p.m_x*c), uRound(p.m_y*c)); }
	friend const UPoint operator*(const UPoint &p, double c) { return UPoint(uRound(p.m_x*c), uRound(p.m_y*c)); }
	friend const UPoint operator*(double c, const UPoint &p) { return UPoint(uRound(p.m_x*c), uRound(p.m_y*c)); }
	friend const UPoint operator-(const UPoint &p){ return UPoint(-p.m_x, -p.m_y); }
	friend const UPoint operator/(const UPoint &p, int c) { return UPoint(p.m_x/c, p.m_y/c); }
	friend const UPoint operator/(const UPoint &p, float c) { return UPoint(uRound(p.m_x/c), uRound(p.m_y/c)); }
	friend const UPoint operator/(const UPoint &p, double c) { return UPoint(uRound(p.m_x/c), uRound(p.m_y/c)); }
private:
	int m_x, m_y;
};

class UPointF {
public:
	UPointF() : m_x(0), m_y(0) {}
	UPointF(const UPoint &p) : m_x(p.x()), m_y(p.y()) {}
	UPointF(float x, float y) : m_x(x), m_y(y) {}
	float &rx() { return m_x; }
	float &ry() { return m_y; }
	void setX(float x) { m_x = x; }
	void setY(float y) { m_y = y; }
	UPoint toPoint() const { return UPoint(uRound(m_x), uRound(m_y)); }
	float x() const { return m_x; }
	float y() const { return m_y; }

	UPointF &operator+=(const UPointF &p) { m_x += p.m_x; m_y += p.m_y; return *this; }
	UPointF &operator-=(const UPointF &p) { m_x -= p.m_x; m_y -= p.m_y; return *this; }
	UPointF &operator*=(int c) { m_x = m_x*c; m_y = m_y*c; return *this; }
	UPointF &operator*=(float c) { m_x = m_x*c; m_y = m_y*c; return *this; }
	UPointF &operator*=(double c) { m_x = m_x*c; m_y = m_y*c; return *this; }
	UPointF &operator/=(int c) { m_x = m_x/c; m_y = m_y/c; return *this; }
	UPointF &operator/=(float c) { m_x = m_x/c; m_y = m_y/c; return *this; }
	UPointF &operator/=(double c) { m_x = m_x/c; m_y = m_y/c; return *this; }

	friend bool operator==(const UPointF &p1, const UPointF &p2) { return uFuzzyIsNull(p1.m_x - p2.m_x) && uFuzzyIsNull(p1.m_y - p2.m_y); }
	friend bool operator!=(const UPointF &p1, const UPointF &p2) { return !uFuzzyIsNull(p1.m_x - p2.m_x) || !uFuzzyIsNull(p1.m_y - p2.m_y); }
	friend const UPointF operator+(const UPointF &p1, const UPointF &p2) { return UPointF(p1.m_x+p2.m_x, p1.m_y+p2.m_y); }
	friend const UPointF operator-(const UPointF &p1, const UPointF &p2) { return UPointF(p1.m_x-p2.m_x, p1.m_y-p2.m_y); }
	friend const UPointF operator*(const UPointF &p, int c) { return UPointF(p.m_x*c, p.m_y*c); }
	friend const UPointF operator*(int c, const UPointF &p) { return UPointF(p.m_x*c, p.m_y*c); }
	friend const UPointF operator*(const UPointF &p, float c) { return UPointF(p.m_x*c, p.m_y*c); }
	friend const UPointF operator*(float c, const UPointF &p) { return UPointF(p.m_x*c, p.m_y*c); }
	friend const UPointF operator*(const UPointF &p, double c) { return UPointF(p.m_x*c, p.m_y*c); }
	friend const UPointF operator*(double c, const UPointF &p) { return UPointF(p.m_x*c, p.m_y*c); }
	friend const UPointF operator-(const UPointF &p){ return UPointF(-p.m_x, -p.m_y); }
	friend const UPointF operator/(const UPointF &p, int c) { return UPointF(p.m_x/c, p.m_y/c); }
	friend const UPointF operator/(const UPointF &p, float c) { return UPointF(p.m_x/c, p.m_y/c); }
	friend const UPointF operator/(const UPointF &p, double c) { return UPointF(p.m_x/c, p.m_y/c); }
private:
	float m_x, m_y;
};

class USize {
public:
	USize() : m_width(-1), m_height(-1) {}
	USize(int width, int height) : m_width(width), m_height(height) {}
	int width() const { return m_width; }
	int height() const { return m_height; }
	void setWidth(int width) { m_width = width; }
	void setHeight(int height) { m_height = height; }
	int &rwidth() { return m_width; }
	int &rheight() { return m_height; }
	void transpose() { int tmp = m_width; m_width = m_height; m_height = tmp; }
	USize transposed() const { return USize(m_height, m_width); }

	USize &operator+=(const USize &s) { m_width += s.m_width; m_height += s.m_height; return *this; }
	USize &operator-=(const USize &s) { m_width -= s.m_width; m_height -= s.m_height; return *this; }
	USize &operator*=(int c) { m_width = m_width*c; m_height = m_height*c; return *this; }
	USize &operator*=(float c) { m_width = uRound(m_width*c); m_height = uRound(m_height*c); return *this; }
	USize &operator*=(double c) { m_width = uRound(m_width*c); m_height = uRound(m_height*c); return *this; }
	USize &operator/=(int c) { m_width = m_width/c; m_height = m_height/c; return *this; }
	USize &operator/=(float c) { m_width = uRound(m_width/c); m_height = uRound(m_height/c); return *this; }
	USize &operator/=(double c) { m_width = uRound(m_width/c); m_height = uRound(m_height/c); return *this; }

	friend bool operator==(const USize &s1, const USize &s2) { return s1.m_width == s2.m_width && s1.m_height == s2.m_height; }
	friend bool operator!=(const USize &s1, const USize &s2) { return s1.m_width != s2.m_width || s1.m_height != s2.m_height; }
	friend const USize operator+(const USize &s1, const USize &s2) { return USize(s1.m_width+s2.m_width, s1.m_height+s2.m_height); }
	friend const USize operator-(const USize &s1, const USize &s2) { return USize(s1.m_width-s2.m_width, s1.m_height-s2.m_height); }
	friend const USize operator*(const USize &s, int c) { return USize(s.m_width*c, s.m_height*c); }
	friend const USize operator*(int c, const USize &s) { return USize(s.m_width*c, s.m_height*c); }
	friend const USize operator*(const USize &s, float c) { return USize(uRound(s.m_width*c), uRound(s.m_height*c)); }
	friend const USize operator*(float c, const USize &s) { return USize(uRound(s.m_width*c), uRound(s.m_height*c)); }
	friend const USize operator*(const USize &s, double c) { return USize(uRound(s.m_width*c), uRound(s.m_height*c)); }
	friend const USize operator*(double c, const USize &s) { return USize(uRound(s.m_width*c), uRound(s.m_height*c)); }
	friend const USize operator/(const USize &s, int c) { return USize(s.m_width/c, s.m_height/c); }
	friend const USize operator/(const USize &s, float c) { return USize(uRound(s.m_width/c), uRound(s.m_height/c)); }
	friend const USize operator/(const USize &s, double c) { return USize(uRound(s.m_width/c), uRound(s.m_height/c)); }
private:
	int m_width, m_height;
};

class USizeF {
public:
	USizeF() : m_width(-1.), m_height(-1.) {}
	USizeF(float width, float height) : m_width(width), m_height(height) {}
	float width() const { return m_width; }
	float height() const { return m_height; }
	void setWidth(float width) { m_width = width; }
	void setHeight(float height) { m_height = height; }
	float &rwidth() { return m_width; }
	float &rheight() { return m_height; }
	void transpose() { float tmp = m_width; m_width = m_height; m_height = tmp; }
	USizeF transposed() const { return USizeF(m_height, m_width); }
	USize toSize() const { return USize(uRound(m_width), uRound(m_height)); }

	USizeF &operator+=(const USizeF &s) { m_width += s.m_width; m_height += s.m_height; return *this; }
	USizeF &operator-=(const USizeF &s) { m_width -= s.m_width; m_height -= s.m_height; return *this; }
	USizeF &operator*=(int c) { m_width = m_width*c; m_height = m_height*c; return *this; }
	USizeF &operator*=(float c) { m_width = m_width*c; m_height = m_height*c; return *this; }
	USizeF &operator*=(double c) { m_width = m_width*c; m_height = m_height*c; return *this; }
	USizeF &operator/=(int c) { m_width = m_width/c; m_height = m_height/c; return *this; }
	USizeF &operator/=(float c) { m_width = m_width/c; m_height = m_height/c; return *this; }
	USizeF &operator/=(double c) { m_width = m_width/c; m_height = m_height/c; return *this; }

	friend bool operator==(const USizeF &s1, const USizeF &s2) { return s1.m_width == s2.m_width && s1.m_height == s2.m_height; }
	friend bool operator!=(const USizeF &s1, const USizeF &s2) { return s1.m_width != s2.m_width || s1.m_height != s2.m_height; }
	friend const USizeF operator+(const USizeF &s1, const USizeF &s2) { return USizeF(s1.m_width+s2.m_width, s1.m_height+s2.m_height); }
	friend const USizeF operator-(const USizeF &s1, const USizeF &s2) { return USizeF(s1.m_width-s2.m_width, s1.m_height-s2.m_height); }
	friend const USizeF operator*(const USizeF &s, int c) { return USizeF(s.m_width*c, s.m_height*c); }
	friend const USizeF operator*(int c, const USizeF &s) { return USizeF(s.m_width*c, s.m_height*c); }
	friend const USizeF operator*(const USizeF &s, float c) { return USizeF(s.m_width*c, s.m_height*c); }
	friend const USizeF operator*(float c, const USizeF &s) { return USizeF(s.m_width*c, s.m_height*c); }
	friend const USizeF operator*(const USizeF &s, double c) { return USizeF(s.m_width*c, s.m_height*c); }
	friend const USizeF operator*(double c, const USizeF &s) { return USizeF(s.m_width*c, s.m_height*c); }
	friend const USizeF operator/(const USizeF &s, int c) { return USizeF(s.m_width/c, s.m_height/c); }
	friend const USizeF operator/(const USizeF &s, float c) { return USizeF(s.m_width/c, s.m_height/c); }
	friend const USizeF operator/(const USizeF &s, double c) { return USizeF(s.m_width/c, s.m_height/c); }
private:
	float m_width, m_height;
};

class ULine {
public:
	ULine() {}
	ULine(const UPoint &p1, const UPoint &p2) : m_p1(p1), m_p2(p2) {}
	ULine(int x1, int y1, int x2, int y2) : m_p1(UPoint(x1, y1)), m_p2(UPoint(x2, y2)) {}
	bool isNull() const { return m_p1 == m_p2; }
	UPoint p1() const { return m_p1; }
	UPoint p2() const { return m_p2; }
	void setP1(const UPoint &p) { m_p1 = p; }
	void setP2(const UPoint &p) { m_p2 = p; }
	void setPoints(const UPoint &p1, const UPoint &p2) { m_p1 = p1; m_p2 = p2; }
	void setLine(int x1, int y1, int x2, int y2) { m_p1 = UPoint(x1, y1); m_p2 = UPoint(x2, y2); }
	int x1() const { return m_p1.x(); }
	int y1() const { return m_p1.y(); }
	int x2() const { return m_p2.x(); }
	int y2() const { return m_p2.y(); }
	int dx() const { return m_p2.x() - m_p1.x(); }
	int dy() const { return m_p2.y() - m_p1.y(); }
	void translate(const UPoint &p) { m_p1 += p; m_p2 += p; }
	void translate(int x, int y) { translate(UPoint(x, y)); }
	ULine translated(const UPoint &p) const { return ULine(m_p1+p, m_p2+p); }
	ULine translated(int x, int y) const { return translated(UPoint(x, y)); }

	bool operator==(const ULine &d) const { return m_p1 == d.m_p1 && m_p2 == d.m_p2; }
	bool operator!=(const ULine &d) const { return !(*this == d); }
private:
	UPoint m_p1, m_p2;
};

class ULineF {
public:
	ULineF() {}
	ULineF(const UPointF &p1, const UPointF &p2) : m_p1(p1), m_p2(p2) {}
	ULineF(float x1, float y1, float x2, float y2) : m_p1(UPointF(x1, y1)), m_p2(UPointF(x2, y2)) {}
	bool isNull() const { return uFuzzyCompare(m_p1.x(), m_p2.x()) && uFuzzyCompare(m_p1.y(), m_p2.y()); }
	UPointF p1() const { return m_p1; }
	UPointF p2() const { return m_p2; }
	void setP1(const UPointF &p) { m_p1 = p; }
	void setP2(const UPointF &p) { m_p2 = p; }
	void setPoints(const UPointF &p1, const UPointF &p2) { m_p1 = p1; m_p2 = p2; }
	void setLine(float x1, float y1, float x2, float y2) { m_p1 = UPointF(x1, y1); m_p2 = UPointF(x2, y2); }
	float x1() const { return m_p1.x(); }
	float y1() const { return m_p1.y(); }
	float x2() const { return m_p2.x(); }
	float y2() const { return m_p2.y(); }
	float dx() const { return m_p2.x()-m_p1.x(); }
	float dy() const { return m_p2.y()-m_p1.y(); }
	float angle() const { return atan2(-dy(), dx()); }
	void setAngle(float angle) { float l = length(), dx = cos(angle)*l, dy = -sin(angle)*l; m_p2.rx() = m_p1.x()+dx; m_p2.ry() = m_p1.y()+dy; }
	float length() const { return sqrt(dx()*dx()+dy()*dy()); }
	void setLength(float length) { if (isNull()) return; ULineF v = unitVector(); m_p2 = UPointF(m_p1.x() + v.dx() * length, m_p1.y() + v.dy() * length); }
	ULineF unitVector() const { float x = m_p2.x()-m_p1.x(), y = m_p2.y()-m_p1.y(), len = sqrt(x*x + y*y); return ULineF(p1(), UPointF(m_p1.x() + x/len, m_p1.y() + y/len)); }
	void translate(const UPointF &p) { m_p1 += p; m_p2 += p; }
	void translate(float x, float y) { translate(UPointF(x, y)); }
	ULineF translated(const UPointF &p) const { return ULineF(m_p1+p, m_p2+p); }
	ULineF translated(float x, float y) const { return translated(UPointF(x, y)); }
	ULine toLine() const { return ULine(m_p1.toPoint(), m_p2.toPoint()); }

	bool operator==(const ULineF &d) const { return m_p1 == d.m_p1 && m_p2 == d.m_p2; }
	bool operator!=(const ULineF &d) const { return !(*this == d); }
private:
	UPointF m_p1, m_p2;
};

class URect {
public:
	URect() : m_x1(0), m_y1(0), m_x2(0), m_y2(0) {}
	URect(const UPoint &topLeft, const UPoint &bottomRight) : m_x1(topLeft.x()), m_y1(topLeft.y()), m_x2(bottomRight.x()), m_y2(bottomRight.y()) {}
	URect(const UPoint &topLeft, const USize &size) : m_x1(topLeft.x()), m_y1(topLeft.y()), m_x2(topLeft.x()+size.width()), m_y2(topLeft.y()+size.height()) {}
	URect(int left, int top, int right, int bottom) : m_x1(left), m_y1(top), m_x2(right), m_y2(bottom) {}
	int left() const { return m_x1; }
	int top() const { return m_y1; }
	int right() const { return m_x2; }
	int bottom() const { return m_y2; }
	int &rleft() { return m_x1; }
	int &rtop() { return m_y1; }
	int &rright() { return m_x2; }
	int &rbottom() { return m_y2; }
	void setLeft(int pos) { m_x1 = pos; }
	void setTop(int pos) { m_y1 = pos; }
	void setRight(int pos) { m_x2 = pos; }
	void setBottom(int pos) { m_y2 = pos; }
	UPoint topLeft() const { return UPoint(m_x1, m_y1); }
	UPoint bottomRight() const { return UPoint(m_x2, m_y2); }
	UPoint topRight() const { return UPoint(m_x2, m_y1); }
	UPoint bottomLeft() const { return UPoint(m_x1, m_y2); }
	void setTopLeft(const UPoint &p) { m_x1 = p.x(); m_y1 = p.y(); }
	void setBottomRight(const UPoint &p) { m_x2 = p.x(); m_y2 = p.y(); }
	void setTopRight(const UPoint &p) { m_x2 = p.x(); m_y1 = p.y(); }
	void setBottomLeft(const UPoint &p) { m_x1 = p.x(); m_y2 = p.y(); }
	void setRect(int left, int top, int right, int bottom) { m_x1 = left; m_y1 = top; m_x2 = right; m_y2 = bottom; }
	int width() const { return m_x2 - m_x1; }
	int height() const { return m_y2 - m_y1; }
	USize size() const { return USize(width(), height()); }
	bool isEmpty() const { return m_x1 >= m_x2 || m_y1 >= m_y2; }
	UPoint center() const { return UPoint((m_x1+m_x2)/2, (m_y1+m_y2)/2); }
	void translate(const UPoint &p) { m_x1 += p.x(); m_y1 += p.y(); m_x2 += p.x(); m_y2 += p.y(); }
	void translate(int x, int y) { m_x1 += x; m_y1 += y; m_x2 += x; m_y2 += y; }
	URect translated(const UPoint &p) const { return URect(m_x1+p.x(), m_y1+p.y(), m_x2+p.x(), m_y2+p.y()); }
	URect translated(int x, int y) const { return URect(m_x1+x, m_y1+y, m_x2+x, m_y2+y); }
	void adjust(int dx1, int dy1, int dx2, int dy2) { m_x1 += dx1; m_y1 += dy1; m_x2 += dx2; m_y2 += dy2; }
	URect adjusted(int dx1, int dy1, int dx2, int dy2) const { return URect(m_x1+dx1, m_y1+dy1, m_x2+dx2, m_y2+dy2); }
	void moveLeft(int pos) { m_x2 += pos-m_x1; m_x1 = pos; }
	void moveTop(int pos) { m_y2 += pos-m_y1; m_y1 = pos; }
	void moveRight(int pos) { m_x1 += pos-m_x2; m_x2 = pos; }
	void moveBottom(int pos) { m_y1 += pos-m_y2; m_y2 = pos; }
	void moveTopLeft(const UPoint &p) { moveLeft(p.x()); moveTop(p.y()); }
	void moveBottomRight(const UPoint &p) { moveRight(p.x()); moveBottom(p.y()); }
	void moveTopRight(const UPoint &p) { moveRight(p.x()); moveTop(p.y()); }
	void moveBottomLeft(const UPoint &p) { moveLeft(p.x()); moveBottom(p.y()); }
	bool contains(const UPoint &p) const { if (isEmpty()) return false; return p.x()>=m_x1&&p.x()<m_x2&&p.y()>=m_y1&&p.y()<m_y2; }
	bool contains(int x, int y) const { return contains(UPoint(x, y)); }
	void normalize() { int tmp; if (m_x1 > m_x2) tmp = m_x1; m_x1 = m_x2; m_x2 = tmp; if (m_y1 > m_y2) tmp = m_y1; m_y1 = m_y2; m_y2 = tmp; }
	URect normalized() const { URect tmp = *this; tmp.normalize(); return tmp; }
	bool intersects(const URect &r) const { return !intersected(r).isEmpty(); }
	URect intersected(const URect &r) const { return *this & r; }
	URect united(const URect &r) const { return *this | r; }
	URect operator|(const URect &r) const { if (this->isEmpty()) return r; if (r.isEmpty()) return *this; return URect(uMin(m_x1,r.left()),uMin(m_y1,r.top()),uMax(m_x2,r.right()),uMax(m_y2,r.bottom())); }
	URect operator&(const URect &r) const { URect tmp; if (this->isEmpty() || r.isEmpty()) return tmp; int left,top,right,bottom; left = uMax(m_x1, r.left()); right = uMin(m_x2, r.right()); if (left>=right) return tmp; top = uMax(m_y1, r.top()); bottom = uMin(m_y2, r.bottom()); if (top>=bottom) return tmp; return URect(left,top,right,bottom); }
	URect& operator|=(const URect &r) { *this = *this | r; return *this; }
	URect& operator&=(const URect &r) { *this = *this & r; return *this; }

	friend bool operator==(const URect &r1, const URect &r2) { return r1.m_x1==r2.m_x1 && r1.m_x2==r2.m_x2 && r1.m_y1==r2.m_y1 && r1.m_y2==r2.m_y2; }
	friend bool operator!=(const URect &r1, const URect &r2) { return r1.m_x1!=r2.m_x1 || r1.m_x2!=r2.m_x2 || r1.m_y1!=r2.m_y1 || r1.m_y2!=r2.m_y2; }
private:
	int m_x1, m_y1, m_x2, m_y2;
};


class URectF {
public:
	URectF() : m_x1(0), m_y1(0), m_x2(0), m_y2(0) {}
	URectF(const UPointF &topLeft, const UPointF &bottomRight) : m_x1(topLeft.x()), m_y1(topLeft.y()), m_x2(bottomRight.x()), m_y2(bottomRight.y()) {}
	URectF(const UPointF &topLeft, const USizeF &size) : m_x1(topLeft.x()), m_y1(topLeft.y()), m_x2(topLeft.x()+size.width()), m_y2(topLeft.y()+size.height()) {}
	URectF(float left, float top, float right, float bottom) : m_x1(left), m_y1(top), m_x2(right), m_y2(bottom) {}
	float left() const { return m_x1; }
	float top() const { return m_y1; }
	float right() const { return m_x2; }
	float bottom() const { return m_y2; }
	float &rleft() { return m_x1; }
	float &rtop() { return m_y1; }
	float &rright() { return m_x2; }
	float &rbottom() { return m_y2; }
	void setLeft(float pos) { m_x1 = pos; }
	void setTop(float pos) { m_y1 = pos; }
	void setRight(float pos) { m_x2 = pos; }
	void setBottom(float pos) { m_y2 = pos; }
	UPointF topLeft() const { return UPointF(m_x1, m_y1); }
	UPointF bottomRight() const { return UPointF(m_x2, m_y2); }
	UPointF topRight() const { return UPointF(m_x2, m_y1); }
	UPointF bottomLeft() const { return UPointF(m_x1, m_y2); }
	void setTopLeft(const UPointF &p) { m_x1 = p.x(); m_y1 = p.y(); }
	void setBottomRight(const UPointF &p) { m_x2 = p.x(); m_y2 = p.y(); }
	void setTopRight(const UPointF &p) { m_x2 = p.x(); m_y1 = p.y(); }
	void setBottomLeft(const UPointF &p) { m_x1 = p.x(); m_y2 = p.y(); }
	void setRect(float left, float top, float right, float bottom) { m_x1 = left; m_y1 = top; m_x2 = right; m_y2 = bottom; }
	float width() const { return m_x2 - m_x1; }
	float height() const { return m_y2 - m_y1; }
	USizeF size() const { return USizeF(width(), height()); }
	bool isEmpty() const { return m_x1 >= m_x2 || m_y1 >= m_y2; }
	UPointF center() const { return UPointF((m_x1+m_x2)/2., (m_y1+m_y2)/2.); }
	void translate(const UPointF &p) { m_x1 += p.x(); m_y1 += p.y(); m_x2 += p.x(); m_y2 += p.y(); }
	void translate(float x, float y) { m_x1 += x; m_y1 += y; m_x2 += x; m_y2 += y; }
	URectF translated(const UPointF &p) const { return URectF(m_x1+p.x(), m_y1+p.y(), m_x2+p.x(), m_y2+p.y()); }
	URectF translated(float x, float y) const { return URectF(m_x1+x, m_y1+y, m_x2+x, m_y2+y); }
	void adjust(float dx1, float dy1, float dx2, float dy2) { m_x1 += dx1; m_y1 += dy1; m_x2 += dx2; m_y2 += dy2; }
	URectF adjusted(float dx1, float dy1, float dx2, float dy2) const { return URectF(m_x1+dx1, m_y1+dy1, m_x2+dx2, m_y2+dy2); }
	void moveLeft(float pos) { m_x2 += pos-m_x1; m_x1 = pos; }
	void moveTop(float pos) { m_y2 += pos-m_y1; m_y1 = pos; }
	void moveRight(float pos) { m_x1 += pos-m_x2; m_x2 = pos; }
	void moveBottom(float pos) { m_y1 += pos-m_y2; m_y2 = pos; }
	void moveTopLeft(const UPointF &p) { moveLeft(p.x()); moveTop(p.y()); }
	void moveBottomRight(const UPointF &p) { moveRight(p.x()); moveBottom(p.y()); }
	void moveTopRight(const UPointF &p) { moveRight(p.x()); moveTop(p.y()); }
	void moveBottomLeft(const UPointF &p) { moveLeft(p.x()); moveBottom(p.y()); }
	bool contains(const UPointF &p) const { if (isEmpty()) return false; return p.x()>=m_x1&&p.x()<m_x2&&p.y()>=m_y1&&p.y()<m_y2; }
	bool contains(float x, float y) const { return contains(UPointF(x, y)); }
	void normalize() { float tmp; if (m_x1 > m_x2) tmp = m_x1; m_x1 = m_x2; m_x2 = tmp; if (m_y1 > m_y2) tmp = m_y1; m_y1 = m_y2; m_y2 = tmp; }
	URectF normalized() const { URectF tmp = *this; tmp.normalize(); return tmp; }
	bool intersects(const URectF &r) const { return !intersected(r).isEmpty(); }
	URectF intersected(const URectF &r) const { return *this & r; }
	URectF united(const URectF &r) const { return *this | r; }
	URect toRect() const { return URect(uRound(m_x1),uRound(m_y1),uRound(m_x2),uRound(m_y2)); }
	URectF operator|(const URectF &r) const { if (this->isEmpty()) return r; if (r.isEmpty()) return *this; return URectF(uMin(m_x1,r.left()),uMin(m_y1,r.top()),uMax(m_x2,r.right()),uMax(m_y2,r.bottom())); }
	URectF operator&(const URectF &r) const { URectF tmp; if (this->isEmpty() || r.isEmpty()) return tmp; float left,top,right,bottom; left = uMax(m_x1, r.left()); right = uMin(m_x2, r.right()); if (left>=right) return tmp; top = uMax(m_y1, r.top()); bottom = uMin(m_y2, r.bottom()); if (top>=bottom) return tmp; return URectF(left,top,right,bottom); }
	URectF& operator|=(const URectF &r) { *this = *this | r; return *this; }
	URectF& operator&=(const URectF &r) { *this = *this & r; return *this; }

	friend bool operator==(const URectF &r1, const URectF &r2) { return uFuzzyCompare(r1.m_x1,r2.m_x1) && uFuzzyCompare(r1.m_y1,r2.m_y1) && uFuzzyCompare(r1.m_x2,r2.m_x2) && uFuzzyCompare(r1.m_y2,r2.m_y2); }
	friend bool operator!=(const URectF &r1, const URectF &r2) { return !uFuzzyCompare(r1.m_x1,r2.m_x1) || !uFuzzyCompare(r1.m_y1,r2.m_y1) || !uFuzzyCompare(r1.m_x2,r2.m_x2) || !uFuzzyCompare(r1.m_y2,r2.m_y2); }
private:
	float m_x1, m_y1, m_x2, m_y2;
};

class URotatedRect {
public:
	URotatedRect() : m_rect(URect()), m_angle(0) {}
	URotatedRect(const URect &rect, float angle) : m_rect(rect), m_angle(angle) {}
	URect rect() const { return m_rect; }
	void setRect(const URect &rect) { m_rect = rect; }
	float angle() const { return m_angle; }
	void setAngle(float angle) { m_angle = angle; }
	URect boundingRect() const {
		float cosa = fabs(cos(m_angle)), sina = fabs(sin(m_angle)), w = m_rect.width(), h = m_rect.height();
		int nw = uRound(w*fabs(cosa)+h*fabs(sina)), nh = uRound(h*fabs(cosa)+w*fabs(sina)), dx = (nw-w)/2, dy = (nh-h)/2;
		return m_rect.adjusted(-dx, -dy, dx, dy); 
	}

	friend bool operator==(const URotatedRect &r1, const URotatedRect &r2) { return r1.m_rect == r2.m_rect && uFuzzyCompare(r1.m_angle, r2.m_angle); }
	friend bool operator!=(const URotatedRect &r1, const URotatedRect &r2) { return r1.m_rect != r2.m_rect || !uFuzzyCompare(r1.m_angle, r2.m_angle); }
private:
	URect m_rect;
	float m_angle;
};

class URotatedRectF {
public:
	URotatedRectF() {}
	URotatedRectF(const URectF &rect, float angle) : m_rect(rect), m_angle(angle) {}
	URectF rect() const { return m_rect; }
	void setRect(const URectF &rect) { m_rect = rect; }
	float angle() const { return m_angle; }
	void setAngle(float angle) { m_angle = angle; }
	URectF boundingRect() const { 
		float cosa = fabs(cos(m_angle)), sina = fabs(sin(m_angle)), w = m_rect.width(), h = m_rect.height();
		float nw = w*fabs(cosa)+h*fabs(sina), nh = h*fabs(cosa)+w*fabs(sina), dx = (nw-w)/2, dy = (nh-h)/2;
		return m_rect.adjusted(-dx, -dy, dx, dy); 
	}

	friend bool operator==(const URotatedRectF &r1, const URotatedRectF &r2) { return r1.m_rect == r2.m_rect && uFuzzyCompare(r1.m_angle, r2.m_angle); }
	friend bool operator!=(const URotatedRectF &r1, const URotatedRectF &r2) { return r1.m_rect != r2.m_rect || !uFuzzyCompare(r1.m_angle, r2.m_angle); }
private:
	URectF m_rect;
	float m_angle;
};

class UCircle {
public:
	UCircle() {}
	UCircle(const UPointF &center, float radius) : m_center(center), m_radius(radius) {}
	UCircle(float x, float y, float radius) : m_center(UPointF(x, y)), m_radius(radius) {}

	UPointF center() const { return m_center; }
	void setCenter(const UPointF &center) { m_center = center; }
	float radius() const { return m_radius; }
	void setRadius(float radius) { m_radius = radius; }
	URectF boundingRect() const { return URectF(m_center.x()-m_radius, m_center.y()-m_radius, m_center.x()+m_radius, m_center.y()+m_radius); }

	friend bool operator==(const UCircle &c1, const UCircle &c2) { return c1.m_center == c2.m_center && uFuzzyCompare(c1.m_radius, c2.m_radius); }
	friend bool operator!=(const UCircle &c1, const UCircle &c2) { return c1.m_center != c2.m_center || !uFuzzyCompare(c1.m_radius, c2.m_radius); }
private:
	UPointF m_center;
	float m_radius;
};

class UEllipse {
public:
	UEllipse() {}
	UEllipse(const UPointF &center, float majorRadius, float minorRadius) : m_center(center), m_majorRadius(majorRadius), m_minorRadius(minorRadius) {}
	UEllipse(float x, float y, float majorRadius, float minorRadius) : m_center(UPointF(x, y)), m_majorRadius(majorRadius), m_minorRadius(minorRadius) {}

	UPointF center() const { return m_center; }
	void setCenter(const UPointF &center) { m_center = center; }
	float majorRadius() const { return m_majorRadius; }
	void setMajorRadius(float majorRadius) { m_majorRadius = majorRadius; }
	float minorRadius() const { return m_minorRadius; }
	void setMinorRadius(float minorRadius) { m_majorRadius = minorRadius; }
	URectF boundingRect() const { return URectF(m_center.x()-m_majorRadius, m_center.y()-m_minorRadius, m_center.x()+m_majorRadius, m_center.y()+m_minorRadius); }

	friend bool operator==(const UEllipse &e1, const UEllipse &e2) { return e1.m_center == e2.m_center && uFuzzyCompare(e1.m_majorRadius, e2.m_majorRadius) && uFuzzyCompare(e1.m_minorRadius, e2.m_minorRadius); }
	friend bool operator!=(const UEllipse &e1, const UEllipse &e2) { return e1.m_center != e2.m_center || !uFuzzyCompare(e1.m_majorRadius, e2.m_majorRadius) || !uFuzzyCompare(e1.m_minorRadius, e2.m_minorRadius); }
private:
	UPointF m_center;
	float m_majorRadius;
	float m_minorRadius;
};

class URotatedEllipse {
public:
	URotatedEllipse() {}
	URotatedEllipse(const UPointF &center, float majorRadius, float minorRadius, float angle) : m_center(center), m_majorRadius(majorRadius), m_minorRadius(minorRadius), m_angle(angle) {}
	URotatedEllipse(float x, float y, float majorRadius, float minorRadius, float angle) : m_center(UPointF(x, y)), m_majorRadius(majorRadius), m_minorRadius(minorRadius), m_angle(angle) {}

	UPointF center() const { return m_center; }
	void setCenter(const UPointF &center) { m_center = center; }
	float majorRadius() const { return m_majorRadius; }
	void setMajorRadius(float majorRadius) { m_majorRadius = majorRadius; }
	float minorRadius() const { return m_minorRadius; }
	void setMinorRadius(float minorRadius) { m_majorRadius = minorRadius; }
	float angle() const { return m_angle; }
	void setAngle(float angle) { m_angle = angle; }
	URectF boundingRect() const {
		float dx, dy; 
		if (uFuzzyCompare(m_angle, 0)) { dx = m_majorRadius; dy = m_minorRadius; }
		else if (uFuzzyCompare(m_angle, float(M_PI))) { dx = m_minorRadius; dy = m_majorRadius; }
		else {
			float cosa = cos(m_angle), sina = sin(m_angle), tana = tan(m_angle), t, cost, sint;
			t = atan(-m_minorRadius*tana/m_majorRadius); cost = cos(t); sint = sin(t);
			dx = fabs(m_majorRadius*cost*cosa-m_minorRadius*sint*sina);
			t = atan(m_minorRadius/(m_majorRadius*tana)); cost = cos(t); sint = sin(t);
			dy = fabs(m_minorRadius*sint*cosa+m_majorRadius*cost*sina);
		}
		return URectF(m_center.x()-dx, m_center.y()-dy, m_center.x()+dx, m_center.y()+dy); 
	}

	friend bool operator==(const URotatedEllipse &e1, const URotatedEllipse &e2) { return e1.m_center == e2.m_center && uFuzzyCompare(e1.m_majorRadius, e2.m_majorRadius) && uFuzzyCompare(e1.m_minorRadius, e2.m_minorRadius) && uFuzzyCompare(e1.m_angle, e2.m_angle); }
	friend bool operator!=(const URotatedEllipse &e1, const URotatedEllipse &e2) { return e1.m_center != e2.m_center || !uFuzzyCompare(e1.m_majorRadius, e2.m_majorRadius) || !uFuzzyCompare(e1.m_minorRadius, e2.m_minorRadius) || !uFuzzyCompare(e1.m_angle, e2.m_angle); }
private:
	UPointF m_center;
	float m_majorRadius;
	float m_minorRadius;
	float m_angle;
};

inline void polygon_isect_line(const UPointF &p1, const UPointF &p2, const UPointF &pos, int *winding)
{
	float x1 = p1.x(), y1 = p1.y(), x2 = p2.x(), y2 = p2.y(), y = pos.y();
	int dir = 1;
	if (uFuzzyCompare(y1, y2)) return;
	else if (y2 < y1) {
		float x_tmp = x2; x2 = x1; x1 = x_tmp;
		float y_tmp = y2; y2 = y1; y1 = y_tmp;
		dir = -1;
	}
	if (y >= y1 && y < y2) {
		float x = x1 + ((x2 - x1) / (y2 - y1)) * (y - y1);
		if (x<=pos.x()) (*winding) += dir;
	}
}

class UPolygon : public std::vector<UPoint> {
public:
	UPolygon() {}
	UPolygon(const UPolygon &polygon) : std::vector<UPoint>(polygon) {}
	UPolygon(const std::vector<UPoint> &points) : std::vector<UPoint>(points) {}
	UPoint point(int i) const { return at(i); }
	void setPoint(int i, const UPoint &point) { (*this)[i] = point; }
	void setPoint(int i, int x, int y) { (*this)[i] = UPoint(x, y); }
	bool isEmpty() const { return empty(); }
	URect boundingRect() const {
		if (isEmpty())
			return URect(0, 0, 0, 0);
		int minx, maxx, miny, maxy;
		UPoint p = (*this)[0];
		minx = maxx = p.x();
		miny = maxy = p.y();
		for (unsigned i = 1; i < size(); ++i) {
			p = (*this)[i];
			if (p.x() < minx)
				minx = p.x();
			else if (p.x() > maxx)
				maxx = p.x();
			if (p.y() < miny)
				miny = p.y();
			else if (p.y() > maxy)
				maxy = p.y();
		}
		return URect(minx, miny, maxx+1, maxy+1);
	}
#if 0
	bool contains(const UPoint &point) const { 
		if (isEmpty()) 
			return false;
		int winding_number = 0;
		UPoint last_pt = at(0);
		for (unsigned i = 1; i < size(); i++) {
			const UPoint &e = at(i);
			qt_polygon_isect_line(last_pt, e, point, &winding_number);
			last_pt = e;
		}
		qt_polygon_isect_line(last_pt, at(0), point, &winding_number);
		return (winding_number != 0);
	}
#else
	void windingNumber(const UPoint &p0, const UPoint &p1, const UPoint &p, int *winding) const {
		int cp = ((p1.x() - p0.x())*(p.y() - p0.y()) - (p.x() -  p0.x())*(p1.y() - p0.y()));
		if (cp > 0 && p0.y() <= p.y() && p1.y() > p.y())
			(*winding)++;
		else if (cp < 0 && p0.y() > p.y() && p1.y() <= p.y())
			(*winding)--;
	}
	bool contains(const UPoint &point) const {
		if (isEmpty()) 
			return false;
		int winding_number = 0;
		UPoint last_pt = (*this)[0];
		for (unsigned i = 1; i < size(); i++) {
			const UPoint &e = (*this)[i];
			windingNumber(last_pt, e, point, &winding_number);
			last_pt = e;
		}
		windingNumber(last_pt, (*this)[0], point, &winding_number);
		return (winding_number != 0);
	}
#endif
	void translate(const UPoint &offset) { translate(offset.x(), offset.y()); }
	void translate(int dx, int dy) { if (dx==0 && dy==0) return; UPoint offset(dx, dy); for (unsigned i = 0; i < size(); i++) (*this)[i] += offset; }
	UPolygon translated(const UPoint &offset) const { return translated(offset.x(), offset.y()); }
	UPolygon translated(int dx, int dy) const { UPolygon copy(*this); copy.translate(dx, dy); return copy; }
};

class UPolygonF : public std::vector<UPointF> {
public:
	UPolygonF() {}
	UPolygonF(const UPolygonF &polygon) : std::vector<UPointF>(polygon) {}
	UPolygonF(const std::vector<UPointF> &points) : std::vector<UPointF>(points) {}
	UPointF point(int i) const { return at(i); }
	void setPoint(int i, const UPointF &point) { (*this)[i] = point; }
	void setPoint(int i, float x, float y) { (*this)[i] = UPointF(x, y); }
	bool isEmpty() const { return empty(); }
	URectF boundingRect() const {
		if (isEmpty())
			return URectF(0, 0, 0, 0);
		float minx, maxx, miny, maxy;
		UPointF p = (*this)[0];
		minx = maxx = p.x();
		miny = maxy = p.y();
		for (unsigned i = 1; i < size(); ++i) {
			p = (*this)[i];
			if (p.x() < minx)
				minx = p.x();
			else if (p.x() > maxx)
				maxx = p.x();
			if (p.y() < miny)
				miny = p.y();
			else if (p.y() > maxy)
				maxy = p.y();
		}
		return URectF(minx, miny, maxx, maxy);
	}
	bool contains(const UPointF &point) const { 
		if (isEmpty()) 
			return false;
		int winding_number = 0;
		UPointF last_pt = (*this)[0];
		for (unsigned i = 1; i < size(); i++) {
			const UPointF &e = (*this)[i];
			polygon_isect_line(last_pt, e, point, &winding_number);
			last_pt = e;
		}
		polygon_isect_line(last_pt, (*this)[0], point, &winding_number);
		return (winding_number != 0);
	}
	void translate(const UPointF &offset) { translate(offset.x(), offset.y()); }
	void translate(float dx, float dy) { if (dx==0 && dy==0) return; UPointF offset(dx, dy); for (unsigned i = 0; i < size(); i++) (*this)[i] += offset; }
	UPolygonF translated(const UPointF &offset) const { return translated(offset.x(), offset.y()); }
	UPolygonF translated(float dx, float dy) const { UPolygonF copy(*this); copy.translate(dx, dy); return copy; }
};


template<class T> class UImgT {
public:
	UImgT() {empty();} 
	UImgT(int width, int height, int depth = 1) {empty(); resize(width, height, depth);} 
	UImgT(const UImgT<T> &other) {empty(); copy(other);}
	UImgT<T> &operator=(const UImgT<T> &other) {if (this != &other) copy(other); return *this;} 
	~UImgT() {resize(0, 0, 0);}
	void resize(int width, int height, int depth = 1) {if(width<=0||height<=0||depth<=0){free();empty();}else{if(width*height*depth!=m_width*m_height*m_depth){free();m_data=new T[width*height*depth];}m_width=width;m_height=height;m_depth=depth;}}
	void copy(const UImgT<T> &other) {if (&other != this) {resize(other.width(), other.height(), other.depth()); if (m_data != 0) memcpy(m_data, other.data(), sizeof(T) * m_width * m_height * m_depth);}}
	void assign(T *data, int width, int height, int depth = 1) {resize(width, height, depth); if (data != 0 && width > 0 && height > 0 && depth > 0) memcpy(m_data, data, sizeof(T)*width*height*depth);}
	int width() const {return m_width;} 
	int height() const {return m_height;}
	int depth() const { return m_depth;}
	int size() const { return m_width * m_height * m_depth; }
	T *data() {return m_data;} 
	const T *data() const {return m_data;}
	T &operator()(int x, int y) {return m_data[m_width * y + x];}
	T &operator()(int x, int y, int d) { return m_data[m_width * (m_height * d + y) + x];}
	const T &operator()(int x, int y) const {return m_data[y * m_width + x];}
	const T &operator()(int x, int y, int d) const { return m_data[m_width * (m_height * d + y) + x];}
	T &operator()(int i) {return m_data[i];} 
	const T &operator()(int i) const {return m_data[i];}
private: 
	T *m_data; 
	int m_width, m_height, m_depth;
	void empty() {m_data = 0; m_width = m_height = m_depth = 0;} 
	void free() {if (m_data != 0) {delete [] m_data; empty();}}
};

typedef UImgT<unsigned char> Img;
typedef UImgT<short> ImgS;
typedef UImgT<int> ImgI;
typedef UImgT<float> ImgF;
typedef UImgT<double> ImgD;

#ifndef DUMP_IMAGE
#define DUMP_IMAGE
#endif

template <class T>
void SaveImg(T *img, int width, int height, char *filename)
{
#ifdef DUMP_IMAGE
	FILE *file = fopen(filename,"wb");
	unsigned char header[54] = { 0 }, align_buf[4] = { 0 };
	const unsigned int align = (4 - (3*width)%4)%4;
	const unsigned int buf_size = (3*width + align)*height;
	const unsigned int file_size = 54 + buf_size;
	header[0] = 'B'; header[1] = 'M';
	header[0x02] = file_size&0xFF;
	header[0x03] = (file_size>>8)&0xFF;
	header[0x04] = (file_size>>16)&0xFF;
	header[0x05] = (file_size>>24)&0xFF;
	header[0x0A] = 0x36;
	header[0x0E] = 0x28;
	header[0x12] = width&0xFF;
	header[0x13] = (width>>8)&0xFF;
	header[0x14] = (width>>16)&0xFF;
	header[0x15] = (width>>24)&0xFF;
	header[0x16] = height&0xFF;
	header[0x17] = (height>>8)&0xFF;
	header[0x18] = (height>>16)&0xFF;
	header[0x19] = (height>>24)&0xFF;
	header[0x1A] = 1;
	header[0x1B] = 0;
	header[0x1C] = 24;
	header[0x1D] = 0;
	header[0x22] = buf_size&0xFF;
	header[0x23] = (buf_size>>8)&0xFF;
	header[0x24] = (buf_size>>16)&0xFF;
	header[0x25] = (buf_size>>24)&0xFF;
	header[0x27] = 0x1;
	header[0x2B] = 0x1;
	fwrite(header,1,54,file);

	const T *ptr = img + width * height - width;

	for (int y = 0; y < height; y++) {
		for (int x = 0; x < width; x++) {
			int val = (int)*(ptr++);
			if (val < 0)
				val = 0;
			else if (val > 255)
				val = 255;
			fputc(val,file); std::fputc(val,file); std::fputc(val,file);
		}
		fwrite(align_buf,1,align,file);
		ptr-=2*width;
	}

	fclose(file);
#endif
}


#endif // U_VISION_H
