#pragma once
#include <string>
#include "./CTmap7Interface.h"

using namespace std;

class CTmap7Reader : public CTmap7Interface
{
public:
    CTmap7Reader(void);
    ~CTmap7Reader(void);
    virtual bool Open(const char *pcFile);
    virtual int64 GetFileSize() const;
    virtual bool Download(int64 startPoint, int64 endPoint, void *pBuffer);
    virtual int Download(const char* pcFileName, const int nLayer,
        const int nPosX, const int nPosY, void* pucData);
    virtual int Download(const char* pcFileName, const int nImage, void* pucData);

private:
    string m_strFile;
};
