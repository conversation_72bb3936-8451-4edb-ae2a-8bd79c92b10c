/**
* @date         2014-02-17
* @filename     CommonEx.cpp
* @purpose      commonly used functions based on opencv
* @version      1.0
* @history      initial draft
* <AUTHOR> Beijing, China
* @copyright    <EMAIL>, 2009-2014. All rights reserved.
*/

#include <cmath>
#include <algorithm>
#include <opencv2/opencv.hpp>
#include "./CommonEx.h"
#include "./Templates.h"
// 
// #ifndef COMMONEX_SPECIAL
// #include "./LicenseSDK/License.h"
// #endif

using namespace cv;

//#define FOR_TRAINING
// #ifdef FOR_TRAINING
// extern bool g_bExpired = IsExpired(2016, 12, 30);
// #elif defined COMMONEX_SPECIAL
// 
// #else
// extern bool g_bExpired = IsExpired(2020, 1, 1);
// #endif

extern bool g_bExpired = false;

// round integer fast
int RoundIntFast(const float f)
{
//    int r;
//     __asm fld f;
//     __asm fistp r;
    return (int)f;
}

// quick sqrt algorithm
float SqrtFloatFast(const float x)
{
    union Float_Int
    {
        int intPart;
        float floatPart;
    };

    Float_Int convertor, convertor2;

    convertor.floatPart = x;
    convertor2.floatPart = x;
    convertor.intPart = 0x1FBCF800 + (convertor.intPart >> 1);
    convertor2.intPart = 0x5f3759df - (convertor2.intPart >> 1);
    return 0.5f * (convertor.floatPart + (x * convertor2.floatPart));
}

// Quick Sqrt Int
uint SqrtIntFast(uint n)
{
    uint a;
    for (a = 0; n >= (2 * a) + 1; n -= (2 * a++) + 1);
    return a;
}

// get gray image data of source image
bool CopyGrayData(const uchar *pucIn, uchar *pucOut,
                  const int nWidth, const int nHeight, const int nPitch,
                  const int nLeft, const int nTop, const int nRight, const int nBottom)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (g_bExpired || NULL == pucIn || NULL == pucOut
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || !IsRoiOk(nWidth, nHeight, nLeft, nTop, nRight, nBottom)
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    pucIn += nTop * nPitch + nLeft * nChannels;
    if (1 == nChannels)
    {
        for (int i = nTop; i < nBottom; i++)
        {
            memcpy(pucOut, pucIn, sizeof(uchar) * ((uint) (nRight - nLeft)));
            pucOut += nRight - nLeft;
            pucIn += nPitch;
        }
    }
    else if (3 == nChannels || 4 == nChannels)
    {
        for (int i = nTop; i < nBottom; i++)
        {
            for (int j = 0; j < (nRight - nLeft); j++, pucIn += nChannels, pucOut++)
            {
                *pucOut = (uchar) ((306 * (*(pucIn + 2))
                    + 601 * (*(pucIn + 1))
                    + 117 * (*pucIn) + 512) >> 10); //lint !e702
            }

            pucIn += nPitch - (nRight - nLeft) * nChannels;
        }
    }

    return true;
}

bool CopyGrayData(const uchar *pucIn, uchar *pucOut,
                  const int nWidth, const int nHeight, const int nPitch)
{
    return CopyGrayData(pucIn, pucOut, nWidth, nHeight, nPitch, 0, 0, nWidth, nHeight);
}

bool CopyGrayData(const uchar *pucIn, uchar *pucOut,
                  const int nWidth, const int nHeight, const int nPitch, const UN_RECT_S &stRect)
{
    return CopyGrayData(pucIn, pucOut, nWidth, nHeight, nPitch,
        stRect.nLeft, stRect.nTop, stRect.nRight, stRect.nBottom);
}

bool CopyGrayData(const uchar *pucIn, uchar *pucOut,
                  const int nWidth, const int nHeight, const int nPitch, const Rect &stRect)
{
    return CopyGrayData(pucIn, pucOut, nWidth, nHeight, nPitch,
        stRect.x, stRect.y, stRect.x + stRect.width, stRect.y + stRect.height);
}

// get color image data of source image
bool CopyColorData(const uchar *pucIn, uchar *pucOut,
                   const int nWidth, const int nHeight, const int nPitchIn, const int nPitchOut,
                   const int nLeft, const int nTop, const int nRight, const int nBottom)
{
    const int nChannelsIn = nPitchIn / max(1, nWidth);
    const int nChannelsOut = nPitchOut / max(1, nRight - nLeft);
    if (g_bExpired || NULL == pucIn || NULL == pucOut
        || (3 != nChannelsIn && 4 != nChannelsIn)
        || (3 != nChannelsOut && 4 != nChannelsOut)
        || !IsRoiOk(nWidth, nHeight, nLeft, nTop, nRight, nBottom)
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    pucIn += nTop * nPitchIn + nLeft * nChannelsIn;
    if (nChannelsIn == nChannelsOut)
    {
        for (int i = nTop; i < nBottom; ++i)
        {
            memcpy(pucOut, pucIn, nPitchOut);
            pucIn += nPitchIn;
            pucOut += nPitchOut;
        }
    }
    else
    {
        memset(pucOut, 255, sizeof(uchar) * (nBottom - nTop) * nPitchOut);

        for (int i = nTop; i < nBottom; i++)
        {
            for (int j = 0; j < (nRight - nLeft);
                j++, pucIn += nChannelsIn, pucOut += nChannelsOut)
            {
                *pucOut = *pucIn;
                *(pucOut + 1) = *(pucIn + 1);
                *(pucOut + 2) = *(pucIn + 2);
            }

            pucIn += nPitchIn - (nRight - nLeft) * nChannelsIn;
            pucOut += nPitchOut - (nRight - nLeft) * nChannelsOut;
        }
    }

    return true;
}

bool CopyColorData(const uchar *pucIn, uchar *pucOut,
                   const int nWidth, const int nHeight, const int nPitchIn, const int nPitchOut)
{
    return CopyColorData(pucIn, pucOut, nWidth, nHeight, nPitchIn, nPitchOut,
        0, 0, nWidth, nHeight);
}

bool CopyColorData(const uchar *pucIn, uchar *pucOut,
                   const int nWidth, const int nHeight, const int nPitchIn, const int nPitchOut,
                   const UN_RECT_S &stRect)
{
    return CopyColorData(pucIn, pucOut, nWidth, nHeight, nPitchIn, nPitchOut,
        stRect.nLeft, stRect.nTop, stRect.nRight, stRect.nBottom);
}

bool CopyColorData(const uchar *pucIn, uchar *pucOut,
                   const int nWidth, const int nHeight, const int nPitchIn, const int nPitchOut,
                   const Rect &stRect)
{
    return CopyColorData(pucIn, pucOut, nWidth, nHeight, nPitchIn, nPitchOut,
        stRect.x, stRect.y, stRect.x + stRect.width, stRect.y + stRect.height);
}

// get average value by statistics
float GetMeanByStat(const float *pfData, const int nNum,
                    const float fMinScale, const float fMaxScale)
{
    float fAvg = 0.0f, fTmp = 0.0f, fMin = (float) (1e6), fMax = 0.0f;
    for (int i = 0; i < nNum; i++)
    {
        fTmp += pfData[i];
        fMin = min(fMin, pfData[i]);
        fMax = max(fMax, pfData[i]);
    }

    int nMinIndex = -1, nMaxIndex = -1, nActualNum = nNum;
    if (nNum > 2)
    {
        // remove maximum and minimum to reduce the influence caused by noise
        for (int i = 0; i < nNum; i++)
        {
            if (M_EPS >= fabs(pfData[i] - fMin))
            {
                nMinIndex = i;
                nActualNum--;
                break;
            }
        }
        for (int i = 0; i < nNum; i++)
        {
            if (M_EPS >= fabs(pfData[i] - fMax))
            {
                nMaxIndex = i;
                nActualNum--;
                break;
            }
        }
        fTmp -= fMin;
        fTmp -= fMax;
        fTmp /= nActualNum;
    }
    else if (nNum >= 1)
    {
        fTmp /= nNum;
    }

    // remove abnormal ones
    int nGoodNum = 0;
    for (int i = 0; i < nNum; i++)
    {
        if (i == nMaxIndex || i == nMinIndex)
        {
            continue;
        }
        if (pfData[i] < fTmp * fMaxScale && pfData[i] > fTmp * fMinScale)
        {
            fAvg += pfData[i];
            nGoodNum++;
        }
    }

    if (nGoodNum > 0)
    {
        fAvg /= nGoodNum;
    }
    else
    {
        fAvg = fTmp;
    }

    return fAvg;
}

// detect edge in a roi
bool DetectEdge(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                const int nLeft, const int nTop, const int nRight, const int nBottom,
                const int nTh)
{
    if (g_bExpired || NULL == pucImg || NULL == pucEdge || pucImg == pucEdge
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    if (!IsRoiOk(nWidth, nHeight, nLeft, nTop, nRight, nBottom))
    {
        return false;
    }

    memset(pucEdge, 0, sizeof(uchar) * (uint) (nWidth * nHeight));
    int nIndex = 0, nDx = 0, nDy = 0;
    for (int i = max(1, nTop); i < min(nHeight - 1, nBottom); i++)
    {
        nIndex = i * nWidth;
        for (int j = max(1, nLeft); j < min(nWidth - 1, nRight); j++)
        {
            nDx = abs(pucImg[nIndex + j + 1] - pucImg[nIndex + j - 1]);
            nDy = abs(pucImg[nIndex + j + nWidth] - pucImg[nIndex + j - nWidth]);

            nDx = max(nDx, nDy);
            if (nDx > nTh)
            {
                pucEdge[nIndex + j] = (uchar) nDx;
            }
        }
    }

    return true;
}

bool DetectEdge(const uchar *pucImg, uchar *pucEdge, const int nWidth,
                const int nHeight, const int nTh)
{
    return DetectEdge(pucImg, pucEdge, nWidth, nHeight, 0, 0, nWidth, nHeight, nTh);
}

bool DetectEdge(const uchar *pucImg, uchar *pucEdge, const int nWidth,
                const int nHeight, const UN_RECT_S &stRect, const int nTh)
{
    return DetectEdge(pucImg, pucEdge, nWidth, nHeight,
        stRect.nLeft, stRect.nTop, stRect.nRight, stRect.nBottom, nTh);
}

bool DetectEdge(const uchar *pucImg, uchar *pucEdge, const int nWidth,
                const int nHeight, const Rect &stRect, const int nTh)
{
    return DetectEdge(pucImg, pucEdge, nWidth, nHeight,
        stRect.x, stRect.y, stRect.x + stRect.width, stRect.y + stRect.height, nTh);
}

// detect horizontal edge in a roi +128
bool DetectEdgeH(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const int nLeft, const int nTop, const int nRight, const int nBottom)
{
    if (g_bExpired || NULL == pucImg || NULL == pucEdge || pucImg == pucEdge
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    if (!IsRoiOk(nWidth, nHeight, nLeft, nTop, nRight, nBottom))
    {
        return false;
    }

    LabelBorder(pucEdge, nWidth, nHeight, RectMake(nLeft, nTop, nRight, nBottom), 128);

    int nIndex = 0, nDy = 0;
    for (int i = max(1, nTop); i < min(nHeight, nBottom); i++)
    {
        nIndex = i * nWidth;
        for (int j = nLeft; j < nRight; j++)
        {
            nDy = 128 + (pucImg[nIndex + j - nWidth] - pucImg[nIndex + j]) / 2;
            pucEdge[nIndex + j] = (uchar) nDy;
        }
    }

    return true;
}

bool DetectEdgeH(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight)
{
    return DetectEdgeH(pucImg, pucEdge, nWidth, nHeight, 0, 0, nWidth, nHeight);
}

bool DetectEdgeH(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const UN_RECT_S &stRect)
{
    return DetectEdgeH(pucImg, pucEdge, nWidth, nHeight,
        stRect.nLeft, stRect.nTop, stRect.nRight, stRect.nBottom);
}

bool DetectEdgeH(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const Rect &stRect)
{
    return DetectEdgeH(pucImg, pucEdge, nWidth, nHeight,
        stRect.x, stRect.y, stRect.x + stRect.width, stRect.y + stRect.height);
}

// detect vertical edge in a roi +128
bool DetectEdgeV(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const int nLeft, const int nTop, const int nRight, const int nBottom)
{
    if (g_bExpired || NULL == pucImg || NULL == pucEdge || pucImg == pucEdge
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    if (!IsRoiOk(nWidth, nHeight, nLeft, nTop, nRight, nBottom))
    {
        return false;
    }

    LabelBorder(pucEdge, nWidth, nHeight, RectMake(nLeft, nTop, nRight, nBottom), 128);

    int nIndex = 0, nDx = 0;
    for (int i = nTop; i < nBottom; i++)
    {
        nIndex = i * nWidth;
        for (int j = max(1, nLeft); j < min(nWidth, nRight); j++)
        {
            nDx = 128 + (pucImg[nIndex + j - 1] - pucImg[nIndex + j]) / 2;
            pucEdge[nIndex + j] = (uchar) nDx;
        }
    }

    return true;
}

bool DetectEdgeV(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight)
{
    return DetectEdgeV(pucImg, pucEdge, nWidth, nHeight, 0, 0, nWidth, nHeight);
}

bool DetectEdgeV(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const UN_RECT_S &stRect)
{
    return DetectEdgeV(pucImg, pucEdge, nWidth, nHeight,
        stRect.nLeft, stRect.nTop, stRect.nRight, stRect.nBottom);
}

bool DetectEdgeV(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const Rect &stRect)
{
    return DetectEdgeV(pucImg, pucEdge, nWidth, nHeight,
        stRect.x, stRect.y, stRect.x + stRect.width, stRect.y + stRect.height);
}

bool DetectEdgeD(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const int nLeft, const int nTop, const int nRight, const int nBottom)
{
    if (g_bExpired || NULL == pucImg || NULL == pucEdge || pucImg == pucEdge
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    if (!IsRoiOk(nWidth, nHeight, nLeft, nTop, nRight, nBottom))
    {
        return false;
    }

    LabelBorder(pucEdge, nWidth, nHeight, RectMake(nLeft, nTop, nRight, nBottom), 128);

    int nIndex = 0, nDy = 0;
    for (int i = max(1, nTop); i < nBottom; i++)
    {
        nIndex = i * nWidth;
        for (int j = max(1, nLeft); j < nRight; j++)
        {
            nDy = 128 + (pucImg[nIndex + j - nWidth - 1] - pucImg[nIndex + j]) / 2;
            pucEdge[nIndex + j] = (uchar) nDy;
        }
    }

    return true;
}

bool DetectEdgeD(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight)
{
    return DetectEdgeD(pucImg, pucEdge, nWidth, nHeight, 0, 0, nWidth, nHeight);
}

bool DetectEdgeD(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const UN_RECT_S &stRect)
{
    return DetectEdgeD(pucImg, pucEdge, nWidth, nHeight,
        stRect.nLeft, stRect.nTop, stRect.nRight, stRect.nBottom);
}

bool DetectEdgeD(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const Rect &stRect)
{
    return DetectEdgeD(pucImg, pucEdge, nWidth, nHeight,
        stRect.x, stRect.y, stRect.x + stRect.width, stRect.y + stRect.height);
}

bool DetectEdgeA(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const int nLeft, const int nTop, const int nRight, const int nBottom)
{
    if (g_bExpired || NULL == pucImg || NULL == pucEdge || pucImg == pucEdge
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    if (!IsRoiOk(nWidth, nHeight, nLeft, nTop, nRight, nBottom))
    {
        return false;
    }

    LabelBorder(pucEdge, nWidth, nHeight, RectMake(nLeft, nTop, nRight, nBottom), 128);

    int nIndex = 0, nDy = 0;
    for (int i = max(1, nTop); i < nBottom; i++)
    {
        nIndex = i * nWidth;
        for (int j = nLeft; j < nRight - 1; j++)
        {
            nDy = 128 + (pucImg[nIndex + j - nWidth + 1] - pucImg[nIndex + j]) / 2;
            pucEdge[nIndex + j] = (uchar) nDy;
        }
    }

    return true;
}

bool DetectEdgeA(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight)
{
    return DetectEdgeA(pucImg, pucEdge, nWidth, nHeight, 0, 0, nWidth, nHeight);
}

bool DetectEdgeA(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const UN_RECT_S &stRect)
{
    return DetectEdgeA(pucImg, pucEdge, nWidth, nHeight,
        stRect.nLeft, stRect.nTop, stRect.nRight, stRect.nBottom);
}

bool DetectEdgeA(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const Rect &stRect)
{
    return DetectEdgeA(pucImg, pucEdge, nWidth, nHeight,
        stRect.x, stRect.y, stRect.x + stRect.width, stRect.y + stRect.height);
}

// detect horizontal edge in a roi +128
bool DetectEdgeHC(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                  const int nLeft, const int nTop, const int nRight, const int nBottom)
{
    if (g_bExpired || NULL == pucImg || NULL == pucEdge || pucImg == pucEdge
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    if (!IsRoiOk(nWidth, nHeight, nLeft, nTop, nRight, nBottom))
    {
        return false;
    }

    LabelBorder(pucEdge, nWidth, nHeight, RectMake(nLeft, nTop, nRight, nBottom), 128);

    int nIndex = 0, nDy = 0;
    for (int i = max(1, nTop); i < min(nHeight - 1, nBottom); i++)
    {
        nIndex = i * nWidth;
        for (int j = nLeft; j < nRight; j++)
        {
            nDy = 128 + (pucImg[nIndex + j - nWidth] - pucImg[nIndex + j + nWidth]) / 2;
            pucEdge[nIndex + j] = (uchar) nDy;
        }
    }

    return true;
}

bool DetectEdgeHC(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight)
{
    return DetectEdgeHC(pucImg, pucEdge, nWidth, nHeight, 0, 0, nWidth, nHeight);
}

bool DetectEdgeHC(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                  const UN_RECT_S &stRect)
{
    return DetectEdgeHC(pucImg, pucEdge, nWidth, nHeight,
        stRect.nLeft, stRect.nTop, stRect.nRight, stRect.nBottom);
}

bool DetectEdgeHC(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                  const Rect &stRect)
{
    return DetectEdgeHC(pucImg, pucEdge, nWidth, nHeight,
        stRect.x, stRect.y, stRect.x + stRect.width, stRect.y + stRect.height);
}

// detect vertical edge in a roi +128
bool DetectEdgeVC(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                  const int nLeft, const int nTop, const int nRight, const int nBottom)
{
    if (g_bExpired || NULL == pucImg || NULL == pucEdge || pucImg == pucEdge
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    if (!IsRoiOk(nWidth, nHeight, nLeft, nTop, nRight, nBottom))
    {
        return false;
    }

    LabelBorder(pucEdge, nWidth, nHeight, RectMake(nLeft, nTop, nRight, nBottom), 128);

    int nIndex = 0, nDx = 0;
    for (int i = nTop; i < nBottom; i++)
    {
        nIndex = i * nWidth;
        for (int j = max(1, nLeft); j < min(nWidth - 1, nRight); j++)
        {
            nDx = 128 + (pucImg[nIndex + j - 1] - pucImg[nIndex + j + 1]) / 2;
            pucEdge[nIndex + j] = (uchar) nDx;
        }
    }

    return true;
}

bool DetectEdgeVC(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight)
{
    return DetectEdgeVC(pucImg, pucEdge, nWidth, nHeight, 0, 0, nWidth, nHeight);
}

bool DetectEdgeVC(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                  const UN_RECT_S &stRect)
{
    return DetectEdgeVC(pucImg, pucEdge, nWidth, nHeight,
        stRect.nLeft, stRect.nTop, stRect.nRight, stRect.nBottom);
}

bool DetectEdgeVC(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                  const Rect &stRect)
{
    return DetectEdgeVC(pucImg, pucEdge, nWidth, nHeight,
        stRect.x, stRect.y, stRect.x + stRect.width, stRect.y + stRect.height);
}

bool DetectEdge(const uchar *pucImg, uchar *pucMag, uchar *pucDir,
                int nWidth, int nHeight, int nScale, int nMagTh)
{
    if (g_bExpired || NULL == pucImg || NULL == pucMag || NULL == pucDir
        || 0 >= nWidth || 0 >= nHeight || 0 >= nScale)
    {
        return false;
    }

    memset(pucMag, 0, sizeof(uchar) * nWidth * nHeight);
    memset(pucDir, 0, sizeof(uchar) * nWidth * nHeight);

    int i, j, nIndex;
    int n0, n1, n2, n3, n4, n5, n6, n7;
    int nTmp1, nTmp2, nGx, nGy, nG;
    const uchar *pTmp = NULL;
    for (i = nScale; i < nHeight - nScale; i++)
    {
        nIndex = i * nWidth;
        for (j = nScale; j < nWidth - nScale; j++)
        {
            pTmp = pucImg + (i - nScale) * nWidth + j;
            n0 = *(pTmp - nScale);
            n1 = *(pTmp);
            n2 = *(pTmp + nScale);
            pTmp += nScale * nWidth;
            n3 = *(pTmp - nScale);
            n4 = *(pTmp + nScale);
            pTmp += nScale * nWidth;
            n5 = *(pTmp - nScale);
            n6 = *(pTmp);
            n7 = *(pTmp + nScale);
            nTmp1 = n7 - n0;
            nTmp2 = n2 - n5;
            nGx = (nTmp1 + nTmp2 + (n4 - n3) * 2) / 4;
            nGy = (nTmp1 - nTmp2 + (n6 - n1) * 2) / 4;
            nG = cvRound(sqrt((float) (nGx * nGx + nGy * nGy)));
            if (nG >= nMagTh)
            {
                pucMag[nIndex + j] = (uchar) nG;
                ComputeAngle(nGx, nGy, pucDir[nIndex + j]);
            }
        }
    }

    return true;
}

bool NonMaximumSuppress(const uchar *pucMag, const uchar *pucDir, uchar *pucOut,
                        int nWidth, int nHeight, const int nTh)
{
    if (g_bExpired || NULL == pucMag || NULL == pucDir || NULL == pucOut
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    const int nOffset[9][2] = {{1, 0}, {1, 1}, {0, 1}, {-1, 1},
    {-1, 0}, {-1, -1}, {0, -1}, {1, -1}, {1, 0}};

    memset(pucOut, 0, sizeof(uchar) * nWidth * nHeight);

    int i, j, nIndex;
    int nMag, nDir;
    int x, y, nBin;
    for (i = 1; i < nHeight - 1; i++)
    {
        nIndex = i * nWidth;
        for (j = 1; j < nWidth - 1; j++)
        {
            nMag = pucMag[nIndex + j];
            if (0 == nMag)
            {
                continue;
            }

            nDir = pucDir[nIndex + j];
            nBin = (nDir + 16) / 32;

            x = j + nOffset[nBin][0];
            y = i + nOffset[nBin][1];
            if (nMag < pucMag[y * nWidth + x] - nTh)
            {
                continue;
            }

            x = j - nOffset[nBin][0];
            y = i - nOffset[nBin][1];
            if (nMag < pucMag[y * nWidth + x] - nTh)
            {
                continue;
            }

            pucOut[nIndex + j] = (uchar) nMag;
        }
    }

    return true;
}

// down sample an image to speed up, destination image should have the same color bits as source image
bool DownSample(const uchar *pucImg, uchar *pucOut, const int nWidth, const int nHeight,
                const int nPitch, const int nFactor/* = 4*/)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (g_bExpired || NULL == pucImg || NULL == pucOut || 1 > nFactor
        || 0 >= nWidth || 0 >= nHeight
        || (1 != nChannels && 3 != nChannels && 4 != nChannels))
    {
        return false;
    }

    const int nW = nWidth / nFactor, nH = nHeight / nFactor;
    const int nDelta = nPitch - nFactor * nW * nChannels;
    int i = 0, j = 0;
    switch (nChannels)
    {
    case 1:
        if (1 == nFactor)
        {
            CopyGrayData(pucImg, pucOut, nWidth, nHeight, nPitch);
        }
        else
        {
            for (i = 0; i < nH; i++)
            {
                for (j = 0; j < nW; j++)
                {
                    *pucOut++ = *pucImg;
                    pucImg += nFactor;
                }

                pucImg += nDelta;
                pucImg += (nFactor - 1) * nPitch;
            }
        }
        break;
    case 3:
        if (1 == nFactor)
        {
            CopyColorData(pucImg, pucOut, nWidth, nHeight, nPitch, nWidth * nChannels);
        }
        else
        {
            for (i = 0; i < nH; i++)
            {
                for (j = 0; j < nW; j++)
                {
                    *pucOut++ = pucImg[0];
                    *pucOut++ = pucImg[1];
                    *pucOut++ = pucImg[2];
                    pucImg += nFactor * nChannels;
                }

                pucImg += nDelta;
                pucImg += (nFactor - 1) * nPitch;
            }
        }
        break;
    case 4:
        if (1 == nFactor)
        {
            CopyColorData(pucImg, pucOut, nWidth, nHeight, nPitch, nWidth * nChannels);
        }
        else
        {
            for (i = 0; i < nH; i++)
            {
                for (j = 0; j < nW; j++)
                {
                    *pucOut++ = pucImg[0];
                    *pucOut++ = pucImg[1];
                    *pucOut++ = pucImg[2];
                    *pucOut++ = pucImg[3];
                    pucImg += nFactor * nChannels;
                }

                pucImg += nDelta;
                pucImg += (nFactor - 1) * nPitch;
            }
        }
        break;
    default:
        break;
    }

    return true;
}

// down sample an image to speed up and make it gray
bool DownSampleGray(const uchar *pucImg, uchar *pucOut, const int nWidth, const int nHeight,
                    const int nPitch, const int nFactor/* = 4*/)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (g_bExpired || NULL == pucImg || NULL == pucOut || 1 > nFactor
        || 0 >= nWidth || 0 >= nHeight
        || (1 != nChannels && 3 != nChannels && 4 != nChannels))
    {
        return false;
    }

    if (1 == nFactor)
    {
        return CopyGrayData(pucImg, pucOut, nWidth, nHeight, nPitch);
    }

    const int nW = nWidth / nFactor, nH = nHeight / nFactor;
    const int nDelta = nPitch - nFactor * nW * nChannels;
    int i = 0, j = 0;
    if (1 == nChannels)
    {
        for (i = 0; i < nH; i++)
        {
            for (j = 0; j < nW; j++)
            {
                *pucOut++ = *pucImg;
                pucImg += nFactor;
            }

            pucImg += nDelta;
            pucImg += (nFactor - 1) * nPitch;
        }
    }
    else
    {
        for (i = 0; i < nH; i++)
        {
            for (j = 0; j < nW; j++)
            {
                *pucOut++ = (uchar) ((306 * (*(pucImg + 2))
                    + 601 * (*(pucImg + 1))
                    + 117 * (*pucImg) + 512) >> 10); //lint !e702

                pucImg += nFactor * nChannels;
            }

            pucImg += nDelta;
            pucImg += (nFactor - 1) * nPitch;
        }
    }

    return true;
}

// down sample an image to speed up
bool DownSampleSplit(const uchar *pucImg, uchar *pucB, uchar *pucG, uchar *pucR,
                     const int nWidth, const int nHeight,
                     const int nPitch, const int nFactor/* = 4*/)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (g_bExpired || NULL == pucImg || NULL == pucB || NULL == pucG || NULL == pucR
        || 1 > nFactor || 0 >= nWidth || 0 >= nHeight
        || (3 != nChannels && 4 != nChannels))
    {
        return false;
    }

    const int nW = nWidth / nFactor, nH = nHeight / nFactor;
    const int nDelta = nPitch - nFactor * nW * nChannels;
    int i = 0, j = 0;
    for (i = 0; i < nH; i++)
    {
        for (j = 0; j < nW; j++)
        {
            *pucB++ = *pucImg;
            *pucG++ = *(pucImg + 1);
            *pucR++ = *(pucImg + 2);

            pucImg += nFactor * nChannels;
        }

        pucImg += nDelta;
        pucImg += (nFactor - 1) * nPitch;
    }

    return true;
}

// project horizontal
bool ProjectH(const uchar *pucImg, const int nWidth, const int nHeight,
              const int nLeft, const int nTop, const int nRight, const int nBottom,
              int *pnRow, const int nAdjust, const bool bInverted /*= false*/)
{
    if (g_bExpired || NULL == pucImg || NULL == pnRow
        || !IsRoiOk(nWidth, nHeight, nLeft, nTop, nRight, nBottom)
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    memset(pnRow + nTop, 0, sizeof(pnRow[0]) * (nBottom - nTop));

    const uchar *pucRow = pucImg + nTop * nWidth + nLeft;
    for (int i = nTop; i < nBottom; i++)
    {
        for (int j = nLeft; j < nRight; j++)
        {
            pnRow[i] += pucRow[j - nLeft];
        }

        pucRow += nWidth;
    }

    const int nAdjustSum = nAdjust * (nRight - nLeft);
    if (bInverted)
    {
        const int nAdjustFinal = 255 * (nRight - nLeft) - nAdjustSum;
        for (int i = nTop; i < nBottom; ++i)
        {
            pnRow[i] = nAdjustFinal - pnRow[i];
        }
    }
    else
    {
        for (int i = nTop; i < nBottom && 0 != nAdjustSum; ++i)
        {
            pnRow[i] += nAdjustSum;
        }
    }

    return true;
}

bool ProjectH(const uchar *pucImg, const int nWidth, const int nHeight,
              int *pnRow, const int nAdjust, const bool bInverted /*= false*/)
{
    return ProjectH(pucImg, nWidth, nHeight, 0, 0, nWidth, nHeight, pnRow, nAdjust, bInverted);
}

bool ProjectH(const uchar *pucImg, const int nWidth, const int nHeight,
              const UN_RECT_S &stRect,
              int *pnRow, const int nAdjust, const bool bInverted /*= false*/)
{
    return ProjectH(pucImg, nWidth, nHeight, stRect.nLeft, stRect.nTop,
        stRect.nRight, stRect.nBottom, pnRow, nAdjust, bInverted);
}

bool ProjectH(const uchar *pucImg, const int nWidth, const int nHeight,
              const Rect &stRect,
              int *pnRow, const int nAdjust, const bool bInverted /*= false*/)
{
    return ProjectH(pucImg, nWidth, nHeight, stRect.x, stRect.x + stRect.width,
        stRect.y, stRect.y + stRect.height, pnRow, nAdjust, bInverted);
}

// project vertical
bool ProjectV(const uchar *pucImg, const int nWidth, const int nHeight,
              const int nLeft, const int nTop, const int nRight, const int nBottom,
              int *pnCol, const int nAdjust, const bool bInverted /*= false*/)
{
    if (g_bExpired || NULL == pucImg || NULL == pnCol
        || !IsRoiOk(nWidth, nHeight, nLeft, nTop, nRight, nBottom)
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    memset(pnCol + nLeft, 0, sizeof(pnCol[0]) * (nRight - nLeft));

    const uchar *pucRow = pucImg + nTop * nWidth + nLeft;
    for (int i = nTop; i < nBottom; i++)
    {
        for (int j = nLeft; j < nRight; j++)
        {
            pnCol[j] += pucRow[j - nLeft];
        }

        pucRow += nWidth;
    }

    const int nAdjustSum = nAdjust * (nBottom - nTop);
    if (bInverted)
    {
        const int nAdjustFinal = 255 * (nBottom - nTop) - nAdjustSum;
        for (int j = nLeft; j < nRight; ++j)
        {
            pnCol[j] = nAdjustFinal - pnCol[j];
        }
    }
    else
    {
        for (int j = nLeft; j < nRight && 0 != nAdjustSum; ++j)
        {
            pnCol[j] += nAdjustSum;
        }
    }

    return true;
}

bool ProjectV(const uchar *pucImg, const int nWidth, const int nHeight,
              int *pnCol, const int nAdjust, const bool bInverted /*= false*/)
{
    return ProjectV(pucImg, nWidth, nHeight, 0, 0, nWidth, nHeight, pnCol, nAdjust, bInverted);
}

bool ProjectV(const uchar *pucImg, const int nWidth, const int nHeight,
              const UN_RECT_S &stRect,
              int *pnCol, const int nAdjust, const bool bInverted /*= false*/)
{
    return ProjectV(pucImg, nWidth, nHeight, stRect.nLeft, stRect.nTop,
        stRect.nRight, stRect.nBottom, pnCol, nAdjust, bInverted);
}

bool ProjectV(const uchar *pucImg, const int nWidth, const int nHeight,
              const Rect &stRect,
              int *pnCol, const int nAdjust, const bool bInverted /*= false*/)
{
    return ProjectV(pucImg, nWidth, nHeight, stRect.x, stRect.x + stRect.width,
        stRect.y, stRect.y + stRect.height, pnCol, nAdjust, bInverted);
}

// Label image border
void LabelBorder(uchar *pucImg, const int nWidth, const int nHeight,
                 const UN_RECT_S &stRect, const uchar nLabel)
{
    if (g_bExpired || NULL == pucImg || stRect.nRight <= stRect.nLeft
        || stRect.nBottom <= stRect.nTop || 0 >= nWidth || 0 >= nHeight)
    {
        return;
    }

    UN_RECT_S stTmp = RectMake(0, 0, nWidth, nHeight);
    stTmp.nLeft = max(stRect.nLeft, stTmp.nLeft);
    stTmp.nTop = max(stRect.nTop, stTmp.nTop);
    stTmp.nRight = min(stRect.nRight, stTmp.nRight);
    stTmp.nBottom = min(stRect.nBottom, stTmp.nBottom);

    const int nCol = stTmp.nRight - stTmp.nLeft;
    memset(pucImg + stTmp.nTop * nWidth + stTmp.nLeft, nLabel, sizeof(uchar) * nCol);
    memset(pucImg + (stTmp.nBottom - 1) * nWidth + stTmp.nLeft, nLabel, sizeof(uchar) * nCol);

    uchar *pucTmp = pucImg + stTmp.nTop * nWidth;
    for (int i = 0; i < stTmp.nBottom - stTmp.nTop; i++)
    {
        *(pucTmp + stTmp.nLeft) = nLabel;
        *(pucTmp + stTmp.nLeft + nCol - 1) = nLabel;
        pucTmp += nWidth;
    }
}

// Label image border
void LabelBorder(uchar *pucImg, const int nWidth, const int nHeight, const uchar nLabel)
{
    LabelBorder(pucImg, nWidth, nHeight, RectMake(0, 0, nWidth, nHeight), nLabel);
}

// Label image border
void LabelBorder(uchar *pucImg, const int nWidth, const int nHeight,
                 const Rect &stRect, const uchar nLabel)
{
    LabelBorder(pucImg, nWidth, nHeight,
        RectMake(stRect.x, stRect.y, stRect.width, stRect.height), nLabel);
}

// fill the gaps between to points
const int ConnectTwoPoints(const UN_POINT_S &stPoint1, const UN_POINT_S &stPoint2,
                           UN_POINT_S* pstPoints, const int nLength)
{
    // too much points need to fill between these two points
    if (abs(stPoint1.nX - stPoint2.nX) + abs(stPoint1.nY - stPoint2.nY) + 1 >= nLength
        || NULL == pstPoints || stPoint1.nX < 0 || stPoint1.nY < 0
        || g_bExpired || stPoint2.nX < 0 || stPoint2.nY < 0)
    {
        return -1; // error occurred
    }
    int nAdd = 0;
    int x1 = stPoint1.nX, y1 = stPoint1.nY;
    int x2 = stPoint2.nX, y2 = stPoint2.nY;
    int xDiff = x2 - x1, yDiff = y2 - y1;
    int xDelta = (xDiff == 0 ) ? 0 : ((xDiff > 0) ? 1 : -1);
    int yDelta = (yDiff == 0 ) ? 0 : ((yDiff > 0) ? 1 : -1);

    // add the first point
    pstPoints[nAdd] = stPoint1;
    nAdd++;
    // four situations
    // 1:    |
    //       |
    //       |
    if (xDiff == 0 && yDiff != 0)
    {
        for (int j = 1; j < abs(yDiff); j++)
        {
            pstPoints[nAdd].nX = x1;
            pstPoints[nAdd].nY = y1 + yDelta * j;
            nAdd++;
        }
    }
    // 2:    ---
    else if (xDiff != 0 && yDiff == 0)
    {
        for (int j = 1; j < abs(xDiff); j++)
        {
            pstPoints[nAdd].nX = x1 + xDelta * j;
            pstPoints[nAdd].nY = y1;
            nAdd++;
        }
    }
    // 3:     /    \
    //       /  or  \
    //      /        \

    else if (xDiff != 0 && yDiff != 0)
    {
        float xk = 0.0f, yk = 0.0f;
        if (abs(xDiff) > abs(yDiff))
        {
            xk =1.0f; yk = (float) fabs((yDiff + 0.0f) / xDiff);
        }
        else
        {
            yk =1.0f; xk = (float) fabs((xDiff + 0.0f) / yDiff);
        }
        for (int j = 1; j < max(abs(xDiff), abs(yDiff)); j++)
        {
            pstPoints[nAdd].nX = (int) (x1 + xDelta * xk * j);
            pstPoints[nAdd].nY = (int) (y1 + yDelta * yk * j);
            nAdd++;
        }
    }
    else if (xDiff == 0 && yDiff == 0)
    {
        // no points need to be filled
    }
    // add the last point
    pstPoints[nAdd] = stPoint2;
    nAdd++;

    return nAdd;
}

// otsu threshold, consider pixels only with contrast
uchar otsu_contrast(const uchar *pucImg, const int nWidth,
                    const int nHeight, const UN_RECT_S &stRect)
{
    const uchar *np;    // pointer to position in the image we are working with
    uchar op1, op2;   // predecessor of pixel *np (start value)
    int maxc = 0;           // maximum contrast (start value)
    int thresholdValue = 1; // value we will threshold at
    int ihist[256];       // image histogram
    int chist[256];       // contrast histogram

    int i, j, k;          // various counters
    int is, i1, i2, ns, n1, n2, gmin, gmax;
    double m1, m2, sum, csum, fmax, sb;

    // zero out histogram ...
    memset(ihist, 0, sizeof(ihist));
    memset(chist, 0, sizeof(chist));
    op1 = op2 = 0;

    UN_RECT_S rtROI = stRect;
    if (rtROI.nRight <= rtROI.nLeft || rtROI.nBottom <= rtROI.nTop)
    {
        rtROI = RectMake(0, 0, nWidth, nHeight);
    }
    gmin = 255;
    gmax = 0;
    k = (rtROI.nBottom - rtROI.nTop) / 512 + 1;
    // v0.43 first get max contrast, dont do it together with next step
    //  because it fails if we have pattern as background (on top)
    for (i = rtROI.nTop; i < rtROI.nBottom; i += k)
    {
        np = &pucImg[(rtROI.nTop + i) * nWidth + rtROI.nLeft];
        for (j = rtROI.nLeft; j < rtROI.nRight; j++)
        {
            ihist[*np]++;
            if (*np > gmax)
                gmax = *np;
            if (*np < gmin)
                gmin = *np;
            if (abs(*np - op1) > maxc)
                maxc = abs(*np - op1); /* new maximum contrast */
            if (abs(*np - op2) > maxc)
                maxc = abs(*np - op2); /* new maximum contrast */
            /* we hope that maxc will be find its maximum very fast */
            op2 = op1;    /* shift old pixel to next older */
            op1 = *np;     /* store old pixel for contrast check */
            np++;       /* next pixel */
        }
    }

    // generate the histogram
    // Aug06 images with large white or black homogeneous
    //   areas give bad results, so we only add pixels on contrast edges
    for (i = rtROI.nTop; i < rtROI.nBottom; i += k)
    {
        np = &pucImg[(rtROI.nTop + i) * nWidth + rtROI.nLeft];
        for (j = rtROI.nLeft; j < rtROI.nRight; j++)
        {
            if (abs(*np - op1) > maxc / 4 || abs(*np - op2) > maxc / 4)
                chist[*np]++; // count only relevant pixels
            op2 = op1;    /* shift old pixel to next older */
            op1 = *np;     /* store old pixel for contrast check */
            np++;       /* next pixel */
        }
    }

    // set up everything
    sum = csum = 0.0;
    ns = 0;
    is = 0;

    for (k = 0; k <= 255; k++)
    {
        sum += (double) k * (double) chist[k];  /* x*f(x) cmass moment */
        ns  += chist[k];                        /*  f(x)    cmass      */
        is  += ihist[k];                        /*  f(x)    imass      */
    }

    if (ns == 0)
    {
        return (160);
    }

    // do the otsu global thresholding method
    fmax = -1.0;
    n1 = 0;
    for (k = 0; k < 255; k++)
    {
        n1 += chist[k];          // left  mass (integration)
        if (n1 == 0)
            continue;       // we need at least one foreground pixel
        n2 = ns - n1;            // right mass (num pixels - left mass)
        if (n2 == 0)
            break;      // we need at least one background pixel
        csum += (double) k * chist[k];  // left mass moment
        m1 =        csum  / n1;        // left  mass center (black chars)
        m2 = (sum - csum) / n2;        // right mass center (white background)
        // max. dipol moment?
        // orig: sb = (double) n1 *(double) n2 * (m1 - m2) * (m1 - m2);
        sb = (double) n1 *(double) n2 * (m2 - m1); // seems to be better Aug06
        /* bbg: note: can be optimized. */
        if (sb > fmax)
        {
            fmax = sb;
            thresholdValue = k + 1;
        }
    }
    // ToDo: error = left/right point where sb is 90% of maximum?
    // now we count all pixels for background detection
    i1 = 0;
    for (k = 0; k < thresholdValue; k++)
    {
        i1 += ihist[k];          // left  mass (integration)
    }
    i2 = is - i1;            // right mass (num pixels - left mass)

    // at this point we have our thresholding value
    // black_char: value<cs,  white_background: value>=cs

    // can it happen? check for sureness
    if (thresholdValue >  gmax)
    {
        thresholdValue = gmax;
    }
    if (thresholdValue <= gmin)
    {
        thresholdValue = gmin+1;
    }

    return (uchar) thresholdValue;
    /* range: 0 < thresholdValue <= 255, example: 1 on b/w images */
    /* 0..threshold-1 is foreground */
    /* threshold..255 is background */
    /* ToDo:  min=blackmasscenter/2,thresh,max=(whitemasscenter+255)/2 */
}

// otsu threshold
uchar otsu(const uchar *image, const int nWidth, const int nHeight, const UN_RECT_S &stRect)
{
    const int L = 256;
    float hist[L] = {0.0f};

    UN_RECT_S rtROI = stRect;
    if (0 > rtROI.nLeft || 0 > rtROI.nTop
        || nWidth < rtROI.nRight || nHeight < rtROI.nBottom
        || rtROI.nLeft >= rtROI.nRight || rtROI.nTop >= rtROI.nBottom)
    {
        return 0;
    }

    //calculate gray scale histogram
    int nIndex = 0;
    for (int i = rtROI.nTop; i < rtROI.nBottom; i++)
    {
        nIndex = i * nWidth;
        for (int j = rtROI.nLeft; j < rtROI.nRight; j++)
        {
            hist[image[nIndex + j]] += 1;
        }
    }

    const int N = (rtROI.nRight - rtROI.nLeft) * (rtROI.nBottom - rtROI.nTop);
    //normalize histogram
    for (int i = 0; i < L; i++)
        hist[i] /= N;

    float ut = 0;
    for (int i = 0; i < L; i++)
        ut += i * hist[i];

    int max_k = 0;
    int max_sigma_k_ = 0;
    for (int k = 0; k < L; ++k)
    {
        float wk = 0.0f, uk = 0.0f;
        for (int i = 0; i <= k; ++i)
        {
            wk += hist[i];
            uk += i * hist[i];
        }

        float sigma_k = 0.0f;
        if (wk != 0 && wk != 1)
            sigma_k  = ((ut * wk - uk) * (ut * wk - uk)) / (wk * (1 - wk));

        if (sigma_k > max_sigma_k_)
        {
            max_k = k;
            max_sigma_k_ = (int) sigma_k;
        }
    }

    return (uchar) max_k;
}

// otsu threshold using log
uchar otsu_log(const uchar *image, const int nWidth,
               const int nHeight, const UN_RECT_S &stRect)
{
    UN_RECT_S rtROI = stRect;
    if (!IsRoiOk(nWidth, nHeight, rtROI))
    {
        return 0;
    }

    int i, j;
    int pHist[256];
    memset(pHist, 0, sizeof(int) * 256);

    int nSum = 0, nIndex = 0;
    for (i = rtROI.nTop; i < rtROI.nBottom; i++)
    {
        nIndex = i * nWidth;
        for (j = rtROI.nLeft; j < rtROI.nRight; j++)
        {
            pHist[image[nIndex + j]]++;
            nSum++;
        }
    }

    // find minimum and maximum level
    int nMinLevel, nMaxLevel;
    nMinLevel = 0;
    nMaxLevel = 255;
    while (nMinLevel < 255 && pHist[nMinLevel] == 0)
        nMinLevel++;
    while (nMaxLevel > 0 && pHist[nMaxLevel] == 0)
        nMaxLevel--;

    int nSumLow = 0, nSumHigh;
    float pLogHist[256];
    float pLogSumLow[256];
    float pLogSumHigh[256];
    for (i = nMinLevel; i < nMaxLevel; i++)
    {
        nSumLow += pHist[i];
        nSumHigh = nSum - nSumLow;

        if (pHist[i] > 0)
            pLogHist[i] = log((float)pHist[i]);

        pLogSumLow[i] = log((float)(nSumLow));
        pLogSumHigh[i] = log((float)(nSumHigh));
    }
    pLogHist[nMaxLevel] = log((float)pHist[nMaxLevel]);

    float fMaxE = 0.0f;
    uchar nTh = (uchar) -1;
    nSumLow = 0;
    for (i = nMinLevel; i < nMaxLevel; i++)
    {
        nSumLow += pHist[i];
        nSumHigh = nSum - nSumLow;

        float fE, fELow = 0, fEHigh = 0;
        for (j = nMinLevel; j <= i; j++)
        {
            if (pHist[j] > 0)
                fELow -= pHist[j]*(pLogHist[j] - pLogSumLow[i]);
        }

        for (j; j <= nMaxLevel; j++)
        {
            if (pHist[j] > 0)
                fEHigh -= pHist[j]*(pLogHist[j] - pLogSumHigh[i]);
        }
        fE = fELow/nSumLow + fEHigh/nSumHigh;
        if (fMaxE < fE || nTh < 0)
        {
            fMaxE = fE;
            nTh = (uchar) i;
        }
    }

    return nTh;
}

uchar otsu(const uchar *pucImg, const int nWidth, const int nHeight)
{
    return otsu(pucImg, nWidth, nHeight, RectMake(0, 0, nWidth, nHeight));
}

uchar otsu_log(const uchar *pucImg, const int nWidth, const int nHeight)
{
    return otsu_log(pucImg, nWidth, nHeight, RectMake(0, 0, nWidth, nHeight));
}

uchar otsu_contrast(const uchar *pucImg, const int nWidth, const int nHeight)
{
    return otsu_contrast(pucImg, nWidth, nHeight, RectMake(0, 0, nWidth, nHeight));
}

uchar otsu(const uchar *pucImg, const int nWidth, const int nHeight, const Rect &stRect)
{
    return otsu(pucImg, nWidth, nHeight,
        RectMake(stRect.x, stRect.y, stRect.width, stRect.height));
}

uchar otsu_log(const uchar *pucImg, const int nWidth, const int nHeight, const Rect &stRect)
{
    return otsu_log(pucImg, nWidth, nHeight,
        RectMake(stRect.x, stRect.y, stRect.width, stRect.height));
}

uchar otsu_contrast(const uchar *pucImg, const int nWidth, const int nHeight, const Rect &stRect)
{
    return otsu_contrast(pucImg, nWidth, nHeight,
        RectMake(stRect.x, stRect.y, stRect.width, stRect.height));
}

// fit line, fA * x + fB * y + fC = 0
bool FitLine(const UN_POINT_S* pstPoints, const int nNum, float &fA, float &fB, float &fC)
{
    if (g_bExpired)
    {
        return false;
    }
    fA = 0.0f;
    fB = 0.0f;
    fC = 0.0f;
    if (nNum < 2)
    {
        return false;
    }

    float fX = 0.0f, fY = 0.0f, fX2 = 0.0f, fY2 = 0.0f, fXY = 0.0f;
    int i = 0;
    for (i = 0; i < nNum; i++)
    {
        fX += pstPoints[i].nX;
        fY += pstPoints[i].nY;
        fX2 += pstPoints[i].nX * pstPoints[i].nX;
        fY2 += pstPoints[i].nY * pstPoints[i].nY;
        fXY += pstPoints[i].nX * pstPoints[i].nY;
    }

    const float fXSum2 = fX * fX, fXSumYSum = fX * fY, fYSum2 = fY * fY;
    const float fC11 = nNum * fX2 - fXSum2, fC12 = nNum * fXY - fXSumYSum;
    const float fC21 = fC12, fC22 = nNum * fY2 - fYSum2;

    if (fabs(fC11) > fabs(fC22)) // y = kx + fB
    {
        fA = fC12 / fC11;
        fB = -1.0f;
        fC = (fY - fA * fX) / nNum;
    }
    else // x = ky + fB
    {
        fA = -1.0f;
        fB = fC21 / fC22;
        fC = (fX - fB * fY) / nNum;
    }

    return true;
}

float FitErrorLine(const UN_POINT_S *pstPoints, const int nNum,
                   const float fA, const float fB, const float fC)
{
    if (g_bExpired || NULL == pstPoints || 2 >= nNum
        || (M_EPS >= max(fabs(fA), fabs(fB))))
    {
        return 0.0f;
    }

    float fX = 0.0f, fY = 0.0f, fError = 0.0f;
    if (fabs(fA) > fabs(fB))
    {
        for (int i = 0; i < nNum; ++i)
        {
            fX = - (fB * pstPoints[i].nY + fC) / fA - pstPoints[i].nX;
            fError += fX * fX;
        }
    }
    else
    {
        for (int i = 0; i < nNum; ++i)
        {
            fY = - (fA * pstPoints[i].nX + fC) / fB - pstPoints[i].nY;
            fError += fY * fY;
        }
    }

    return sqrtf(fError / nNum);
}

bool FitLineRefit(const UN_POINT_S *pstFit, const int nNum,
                  float &fA, float &fB, float &fC, float &fFitError)
{
    bool bOk = FitLine(pstFit, nNum, fA, fB, fC);
    if (!bOk)
    {
        return false;
    }

    float fError = FitErrorLine(pstFit, nNum, fA, fB, fC);
    if (M_EPS >= fError)
    {
        fFitError = fError;
        return true;
    }

    UN_POINT_S *pstSec = mnew UN_POINT_S[nNum];
    if (NULL == pstSec)
    {
        return false;
    }

    int nSec = 0;
    float fX = 0.0f, fY = 0.0f;
    if (fabs(fA) > fabs(fB))
    {
        for (int i = 0; i < nNum; ++i)
        {
            fX = - (fB * pstFit[i].nY + fC) / fA - pstFit[i].nX;
            if (fX * fX < fError * fError)
            {
                pstSec[nSec++] = pstFit[i];
            }
        }
    }
    else
    {
        for (int i = 0; i < nNum; ++i)
        {
            fY = - (fA * pstFit[i].nX + fC) / fB - pstFit[i].nY;
            if (fY * fY <= fError * fError)
            {
                pstSec[nSec++] = pstFit[i];
            }
        }
    }

    bOk = FitLine(pstSec, nSec, fA, fB, fC);
    if (!bOk)
    {
        mdelete(pstSec);
        return false;
    }

    fFitError = FitErrorLine(pstSec, nSec, fA, fB, fC);
    mdelete(pstSec);

    return fFitError <= fError;
}

// fit line, fA * x + fB * y + fC = 0
bool FitLine(const UN_POINT_F_S* pstPoints, const int nNum, float &fA, float &fB, float &fC)
{
    fA = 0.0f;
    fB = 0.0f;
    fC = 0.0f;
    if (g_bExpired || nNum < 2)
    {
        return false;
    }

    float fX = 0.0f, fY = 0.0f, fX2 = 0.0f, fY2 = 0.0f, fXY = 0.0f;
    int i = 0;
    for (i = 0; i < nNum; i++)
    {
        fX += pstPoints[i].fX;
        fY += pstPoints[i].fY;
        fX2 += pstPoints[i].fX * pstPoints[i].fX;
        fY2 += pstPoints[i].fY * pstPoints[i].fY;
        fXY += pstPoints[i].fX * pstPoints[i].fY;
    }

    const float fXSum2 = fX * fX, fXSumYSum = fX * fY, fYSum2 = fY * fY;
    const float fC11 = nNum * fX2 - fXSum2, fC12 = nNum * fXY - fXSumYSum;
    const float fC21 = fC12, fC22 = nNum * fY2 - fYSum2;

    if (fabs(fC11) > fabs(fC22)) // y = kx + fB
    {
        fA = fC12 / fC11;
        fB = -1.0f;
        fC = (fY - fA * fX) / nNum;
    }
    else // x = ky + fB
    {
        fA = -1.0f;
        fB = fC21 / fC22;
        fC = (fX - fB * fY) / nNum;
    }

    return true;
}

float FitErrorLine(const UN_POINT_F_S *pstPoints, const int nNum,
                   const float fA, const float fB, const float fC)
{
    if (g_bExpired || NULL == pstPoints || 2 >= nNum
        || (M_EPS >= max(fabs(fA), fabs(fB))))
    {
        return 0.0f;
    }

    float fX = 0.0f, fY = 0.0f, fError = 0.0f;
    if (fabs(fA) > fabs(fB))
    {
        for (int i = 0; i < nNum; ++i)
        {
            fX = - (fB * pstPoints[i].fY + fC) / fA - pstPoints[i].fX;
            fError += fX * fX;
        }
    }
    else
    {
        for (int i = 0; i < nNum; ++i)
        {
            fY = - (fA * pstPoints[i].fX + fC) / fB - pstPoints[i].fY;
            fError += fY * fY;
        }
    }

    return sqrtf(fError / nNum);
}

bool FitLineRefit(const UN_POINT_F_S *pstFit, const int nNum,
                  float &fA, float &fB, float &fC, float &fFitError)
{
    bool bOk = FitLine(pstFit, nNum, fA, fB, fC);
    if (!bOk)
    {
        return false;
    }

    float fError = FitErrorLine(pstFit, nNum, fA, fB, fC);
    if (M_EPS >= fError)
    {
        fFitError = fError;
        return true;
    }

    UN_POINT_F_S *pstSec = mnew UN_POINT_F_S[nNum];
    if (NULL == pstSec)
    {
        return false;
    }

    int nSec = 0;
    float fX = 0.0f, fY = 0.0f;
    if (fabs(fA) > fabs(fB))
    {
        for (int i = 0; i < nNum; ++i)
        {
            fX = - (fB * pstFit[i].fY + fC) / fA - pstFit[i].fX;
            if (fX * fX < fError * fError)
            {
                pstSec[nSec++] = pstFit[i];
            }
        }
    }
    else
    {
        for (int i = 0; i < nNum; ++i)
        {
            fY = - (fA * pstFit[i].fX + fC) / fB - pstFit[i].fY;
            if (fY * fY <= fError * fError)
            {
                pstSec[nSec++] = pstFit[i];
            }
        }
    }

    bOk = FitLine(pstSec, nSec, fA, fB, fC);
    if (!bOk)
    {
        mdelete(pstSec);
        return false;
    }

    fFitError = FitErrorLine(pstSec, nSec, fA, fB, fC);
    mdelete(pstSec);

    return fFitError <= fError;
}

// fit circle
bool FitCircle(const UN_POINT_S* pstPoints, const int nLength,
               float &fX, float &fY, float &fR)
{
    fX = 0.0f;
    fY = 0.0f;
    fR = 0.0f;
    if (g_bExpired || nLength < 3)
    {
        return false;
    }

    float X1 = 0.0f, Y1 = 0.0f, X2 = 0.0f, Y2 = 0.0f, X3 = 0.0f, Y3 = 0.0f;
    float X1Y1 = 0.0f, X1Y2 = 0.0f, X2Y1 = 0.0f;
    float fTmp1 = 0.0f, fTmp2 = 0.0f;
    int x = 0, y = 0, i = 0;
    for (i = 0; i < nLength; i++)
    {
        x = pstPoints[i].nX;
        y = pstPoints[i].nY;
        X1 += x;
        Y1 += y;
        fTmp1 = x * x + 0.0f;
        fTmp2 = y * y + 0.0f;
        X2 += fTmp1;
        Y2 += fTmp2;
        X3 += fTmp1 * x;
        Y3 += fTmp2 * y;
        X1Y1 += x * y;
        X1Y2 += x * fTmp2;
        X2Y1 += fTmp1 * y;
    }

    float C, D, E, G, H, N;
    float a, b, c;
    N = nLength + 0.0f;
    C = N * X2 - X1 * X1;
    D = N * X1Y1 - X1 * Y1;
    E = N * X3 + N * X1Y2 - (X2 + Y2) * X1;
    G = N * Y2 - Y1 * Y1;
    H = N * X2Y1 + N * Y3 - (X2 + Y2) * Y1;
    a = (H * D - E * G) / (C * G - D * D);
    b = (H * C - E * D) / (D * D - G * C);
    c = -(a * X1 + b * Y1 + X2 + Y2) / N;

    fX = a / (-2);
    fY = b / (-2);
    fR = sqrt(a * a + b * b - 4 * c) / 2;
    return true;
}

// fit circle
bool FitCircle(const UN_POINT_S* pstPoints, const uchar *pucWeight, const int nNum,
               float &fX, float &fY, float &fR)
{
    fX = 0.0f;
    fY = 0.0f;
    fR = 0.0f;
    if (g_bExpired || nNum < 3)
    {
        return false;
    }

    float X1 = 0.0f, Y1 = 0.0f, X2 = 0.0f, Y2 = 0.0f, X3 = 0.0f, Y3 = 0.0f;
    float X1Y1 = 0.0f, X1Y2 = 0.0f, X2Y1 = 0.0f;
    float fTmp1 = 0.0f, fTmp2 = 0.0f;
    int x = 0, y = 0, i = 0, j = 0, nCount = 0;
    for (i = 0; i < nNum; i++)
    {
        for (j = 0; j < pucWeight[i]; ++j)
        {
            x = pstPoints[i].nX;
            y = pstPoints[i].nY;
            X1 += x;
            Y1 += y;
            fTmp1 = x * x + 0.0f;
            fTmp2 = y * y + 0.0f;
            X2 += fTmp1;
            Y2 += fTmp2;
            X3 += fTmp1 * x;
            Y3 += fTmp2 * y;
            X1Y1 += x * y;
            X1Y2 += x * fTmp2;
            X2Y1 += fTmp1 * y;

            nCount++;
        }
    }

    float C, D, E, G, H, N;
    float a, b, c;
    N = nCount + 0.0f;
    C = N * X2 - X1 * X1;
    D = N * X1Y1 - X1 * Y1;
    E = N * X3 + N * X1Y2 - (X2 + Y2) * X1;
    G = N * Y2 - Y1 * Y1;
    H = N * X2Y1 + N * Y3 - (X2 + Y2) * Y1;
    a = (H * D - E * G) / (C * G - D * D);
    b = (H * C - E * D) / (D * D - G * C);
    c = -(a * X1 + b * Y1 + X2 + Y2) / N;

    fX = a / (-2);
    fY = b / (-2);
    fR = sqrt(a * a + b * b - 4 * c) / 2;
    return true;
}

float FitErrorCircle(const UN_POINT_S *pstPoints, const int nNum,
                     const float fX, const float fY, const float fR)
{
    if (g_bExpired || NULL == pstPoints || 3 >= nNum)
    {
        return 0.0f;
    }

    float fDeltaX = 0.0f, fDeltaY = 0.0f;
    float fError = 0.0f, fTmp = 0.0f;
    for (int i = 0; i < nNum; ++i)
    {
        fDeltaX = pstPoints[i].nX - fX;
        fDeltaY = pstPoints[i].nY - fY;
        fTmp = fR * fR - fDeltaX * fDeltaX - fDeltaY * fDeltaY;
        fError += fabs(fTmp);
    }

    return sqrtf(fError / nNum);
}

bool FitCircleRefit(const UN_POINT_S *pstFit, const int nNum,
                    float &fX, float &fY, float &fR, float &fFitError)
{
    bool bOk = FitCircle(pstFit, nNum, fX, fY, fR);
    if (!bOk)
    {
        return false;
    }

    float fError = FitErrorCircle(pstFit, nNum, fX, fY, fR);
    if (M_EPS >= fError)
    {
        fFitError = fError;
        return true;
    }

    UN_POINT_S *pstSec = mnew UN_POINT_S[nNum];
    if (NULL == pstSec)
    {
        return false;
    }

    int nSec = 0;
    float fDeltaX = 0.0f, fDeltaY = 0.0f, fTmp = 0.0f;
    for (int i = 0; i < nNum; ++i)
    {
        fDeltaX = pstFit[i].nX - fX;
        fDeltaY = pstFit[i].nY - fY;
        fTmp = fR * fR - fDeltaX * fDeltaX - fDeltaY * fDeltaY;
        if (fabs(fTmp) <= fError * fError / 2)
        {
            pstSec[nSec++] = pstFit[i];
        }
    }

    bOk = FitCircle(pstSec, nSec, fX, fY, fR);
    if (!bOk)
    {
        mdelete(pstSec);
        return false;
    }

    fFitError = FitErrorCircle(pstSec, nSec, fX, fY, fR);
    mdelete(pstSec);

    return fFitError <= fError;
}

// sub function for fitting rectangle, calculate response and find rectangle
void FitRectangleSub(const UN_POINT_S *pstPoints, const int nLen, const UN_POINT_F_S &stCenter,
                     float *pfCol, float *pfRow, const int nSize,
                     const int fOffsetX, const int fOffsetY, const float fAngle,
                     float &fMaxRes, float &fLeft, float &fRight, float &fTop, float &fBottom)
{
    memset(pfCol, 0, sizeof(float) * nSize * 2);

    UN_POINT_F_S stPoint;
    const float fSin = (float) sin(fAngle * M_PI / 180);
    const float fCos = (float) cos(fAngle * M_PI / 180);
    for (int j = 0; j < nLen; ++j)
    {
        stPoint = PointMake(pstPoints[j].nX - fOffsetX+0.0f,
            pstPoints[j].nY - fOffsetY+0.0f);
        stPoint = RotatePoint(stPoint, stCenter, fSin, fCos);

        // interpreted projection
        const int nX = (int) stPoint.fX, nY = (int) stPoint.fY;
        float fDistX = stPoint.fX - nX, fDistY = stPoint.fY - nY;
        if (0 <= nX && nSize > nX)
        {
            pfCol[nX] += 1 - fDistX;
        }
        if (0 <= nX + 1 && nSize > nX + 1)
        {
            pfCol[nX + 1] += fDistX;
        }
        if (0 <= nY && nSize > nY)
        {
            pfRow[nY] += 1 - fDistY;
        }
        if (0 <= nY + 1 && nSize > nY + 1)
        {
            pfRow[nY + 1] += fDistY;
        }
    }

    // *****
    // array can NOT be smoothed, maybe it's because projection already interpreted
    // or maybe it's because of floating data
#if 0
    SmoothArray(pfCol, nSize, false);
    SmoothArray(pfRow, nSize, false);
#endif

    // find two maxima
    const int nHalfSize = nSize / 2;
    int nIndex = FindMaxIndex(pfCol, nHalfSize);
    fLeft = (float) nIndex;
    float fXRes = pfCol[nIndex];
    if (0 < nIndex && nSize - 1 > nIndex)
    {
        fLeft += GetSubPosition(pfCol[nIndex], pfCol[nIndex - 1], pfCol[nIndex + 1]);
    }

    nIndex = FindMaxIndex(pfCol + nHalfSize, nSize - nHalfSize) + nHalfSize;
    fRight = (float) nIndex;
    fXRes += pfCol[nIndex];
    if (0 < nIndex && nSize - 1 > nIndex)
    {
        fRight += GetSubPosition(pfCol[nIndex], pfCol[nIndex - 1], pfCol[nIndex + 1]);
    }

    nIndex = FindMaxIndex(pfRow, nHalfSize);
    fTop = (float) nIndex;
    float fYRes = pfRow[nIndex];
    if (0 < nIndex && nSize - 1 > nIndex)
    {
        fTop += GetSubPosition(pfRow[nIndex], pfRow[nIndex - 1], pfRow[nIndex + 1]);
    }

    nIndex = FindMaxIndex(pfRow + nHalfSize, nSize - nHalfSize) + nHalfSize;
    fBottom = (float) nIndex;
    fYRes += pfRow[nIndex];
    if (0 < nIndex && nSize - 1 > nIndex)
    {
        fBottom += GetSubPosition(pfRow[nIndex], pfRow[nIndex - 1], pfRow[nIndex + 1]);
    }

    // calculate the whole response
    fMaxRes = fXRes * fYRes / (fXRes + fYRes);
}

// fitting a rectangle using contour points
bool FitRectangle(const UN_POINT_S *pstPoints, const int nLen,
                  float &fAngle, float &fA, float &fB, UN_POINT_F_S *pstRect)
{
    if (g_bExpired || NULL == pstPoints || 4 > nLen)
    {
        return false;
    }

    // find outer rectangle of points
    int nLeft = pstPoints[0].nX, nRight = pstPoints[0].nX;
    int nTop = pstPoints[0].nY, nBottom = pstPoints[0].nY;
    for (int i = 1; i < nLen; ++i)
    {
        nLeft = min(nLeft, pstPoints[i].nX);
        nRight = max(nRight, pstPoints[i].nX);
        nTop = min(nTop, pstPoints[i].nY);
        nBottom = max(nBottom, pstPoints[i].nY);
    }

    // calculate maximal size of rotated rectangle
    int nWidth = nRight - nLeft + 1, nHeight = nBottom - nTop + 1;
    const int nSize = SqrtIntFast(nWidth * nWidth + nHeight * nHeight) + 1;
    float *pfCol = mnew float[nSize * 2];
    if (g_bExpired || NULL == pfCol)
    {
        return false;
    }
    float *pfRow = pfCol + nSize;
    memset(pfCol, 0, sizeof(float) * nSize * 2);

    // NOTE: contour points have to be shifted to the center of projection dimensions
    // so that all points will be effective after rotation
    // center of contour
    const UN_POINT_F_S stCenterCon = PointMake((nLeft + nRight) / 2.0f - nLeft,
        (nTop + nBottom) / 2.0f - nTop);
    // center of projection dimensions
    const UN_POINT_F_S stCenter = PointMake(nSize / 2.0f, nSize / 2.0f);
    float fMaxRes = 0.0f, fBestLeft = 0.0f, fBestRight = 0.0f;
    float fBestTop = 0.0f, fBestBottom = 0.0f;
    float fRes = 0.0f, fLeft, fRight, fTop, fBottom;
    int nBestAngle = 0;

    // should use integer!!! (accumulate error for float data???)
    const int nOffsetX = (int) (stCenterCon.fX - stCenter.fX);
    const int nOffsetY = (int) (stCenterCon.fY - stCenter.fY);

    // rotate 90 degrees to find the maximal response
    for (int i = 0; i < 90; ++i)
    {
        FitRectangleSub(pstPoints, nLen, stCenter, pfCol, pfRow, nSize,
            nLeft + nOffsetX, nTop + nOffsetY,
            i + 0.0f, fRes, fLeft, fRight, fTop, fBottom);

        if (fMaxRes < fRes)
        {
            fMaxRes = fRes;
            fBestLeft = fLeft;
            fBestRight = fRight;
            fBestTop = fTop;
            fBestBottom = fBottom;
            nBestAngle = i;
        }
    }

    // sub-angle
    const int nResolution = 10; // 1.0/10 degree
    float fBestAngle = (float) nBestAngle;
    for (int i = 1 - nResolution; i < nResolution; ++i)
    {
        FitRectangleSub(pstPoints, nLen, stCenter, pfCol, pfRow, nSize,
            nLeft + nOffsetX, nTop + nOffsetY,
            nBestAngle + (i + 0.0f) / nResolution,
            fRes, fLeft, fRight, fTop, fBottom);

        if (fMaxRes < fRes)
        {
            fMaxRes = fRes;
            fBestLeft = fLeft;
            fBestRight = fRight;
            fBestTop = fTop;
            fBestBottom = fBottom;
            fBestAngle = nBestAngle + (i + 0.0f) / nResolution;
        }
    }

    fAngle = fBestAngle;
    fA = fBestRight - fBestLeft + 1;
    fB = fBestBottom - fBestTop + 1;

    // position of rectangle is not accurate enough
    if (g_bExpired || NULL != pstRect)
    {
        pstRect[0] = PointMake(fBestLeft, fBestTop);
        pstRect[1] = PointMake(fBestRight, fBestTop);
        pstRect[2] = PointMake(fBestRight, fBestBottom);
        pstRect[3] = PointMake(fBestLeft, fBestBottom);

        const float fSin = (float) sin(-fBestAngle * M_PI / 180);
        const float fCos = (float) cos(-fBestAngle * M_PI / 180);

        for (int j = 0; j < 4; ++j)
        {
            pstRect[j] = RotatePoint(pstRect[j], stCenter, fSin, fCos);
            // set offset back after rotation
            pstRect[j].fX += nLeft + nOffsetX;
            pstRect[j].fY += nTop + nOffsetY;
        }
    }

    mdelete(pfCol);
    return true;
}

// fitting a rectangle using contour points, fAngle is initialized
bool FitRectangle(const UN_POINT_S *pstPoints, const int nLen, const int nResolution,
                  float &fAngle, float &fA, float &fB, UN_POINT_F_S *pstRect)
{
    if (g_bExpired || NULL == pstPoints || 4 > nLen)
    {
        return false;
    }

    // find outer rectangle of points
    int nLeft = pstPoints[0].nX, nRight = pstPoints[0].nX;
    int nTop = pstPoints[0].nY, nBottom = pstPoints[0].nY;
    for (int i = 1; i < nLen; ++i)
    {
        nLeft = min(nLeft, pstPoints[i].nX);
        nRight = max(nRight, pstPoints[i].nX);
        nTop = min(nTop, pstPoints[i].nY);
        nBottom = max(nBottom, pstPoints[i].nY);
    }

    // calculate maximal size of rotated rectangle
    int nWidth = nRight - nLeft + 1, nHeight = nBottom - nTop + 1;
    const int nSize = SqrtIntFast(nWidth * nWidth + nHeight * nHeight) + 1;
    float *pfCol = mnew float[nSize * 2];
    if (g_bExpired || NULL == pfCol)
    {
        return false;
    }
    float *pfRow = pfCol + nSize;
    memset(pfCol, 0, sizeof(float) * nSize * 2);

    // NOTE: contour points have to be shifted to the center of projection dimensions
    // so that all points will be effective after rotation
    // center of contour
    const UN_POINT_F_S stCenterCon = PointMake((nLeft + nRight) / 2.0f - nLeft,
        (nTop + nBottom) / 2.0f - nTop);
    // center of projection dimensions
    const UN_POINT_F_S stCenter = PointMake(nSize / 2.0f, nSize / 2.0f);
    float fMaxRes = 0.0f, fBestLeft = 0.0f, fBestRight = 0.0f;
    float fBestTop = 0.0f, fBestBottom = 0.0f;
    float fRes = 0.0f, fLeft, fRight, fTop, fBottom;

    // should use integer!!! (accumulate error for float data???)
    const int nOffsetX = (int) (stCenterCon.fX - stCenter.fX);
    const int nOffsetY = (int) (stCenterCon.fY - stCenter.fY);

    // sub-angle
    float fBestAngle = fAngle;
    const float fDeltaAngle = 2.0f; // check +- 2 degrees
    for (int i = 1 - nResolution; i < nResolution; ++i)
    {
        FitRectangleSub(pstPoints, nLen, stCenter, pfCol, pfRow, nSize,
            nLeft + nOffsetX, nTop + nOffsetY,
            fAngle + fDeltaAngle * i / nResolution,
            fRes, fLeft, fRight, fTop, fBottom);

        if (fMaxRes < fRes)
        {
            fMaxRes = fRes;
            fBestLeft = fLeft;
            fBestRight = fRight;
            fBestTop = fTop;
            fBestBottom = fBottom;
            fBestAngle = fAngle + fDeltaAngle * i / nResolution;
        }
    }

    fAngle = fBestAngle;
    fA = fBestRight - fBestLeft + 1;
    fB = fBestBottom - fBestTop + 1;

    // position of rectangle is not accurate enough
    if (g_bExpired || NULL != pstRect)
    {
        pstRect[0] = PointMake(fBestLeft, fBestTop);
        pstRect[1] = PointMake(fBestRight, fBestTop);
        pstRect[2] = PointMake(fBestRight, fBestBottom);
        pstRect[3] = PointMake(fBestLeft, fBestBottom);

        const float fSin = (float) sin(-fBestAngle * M_PI / 180);
        const float fCos = (float) cos(-fBestAngle * M_PI / 180);

        for (int j = 0; j < 4; ++j)
        {
            pstRect[j] = RotatePoint(pstRect[j], stCenter, fSin, fCos);
            // set offset back after rotation
            pstRect[j].fX += nLeft + nOffsetX;
            pstRect[j].fY += nTop + nOffsetY;
        }
    }

    mdelete(pfCol);
    return true;
}

// estimate angle of rectangle by fitting lines, not so accurate
bool EstimateRectAngle(const UN_POINT_S *pstPoints, const int nLen, float &fAngle)
{
    if (g_bExpired || NULL == pstPoints || 20 > nLen)
    {
        return false;
    }

    // center of contour
    int nX = 0, nY = 0;
    for (int i = 0; i < nLen; ++i)
    {
        nX += pstPoints[i].nX;
        nY += pstPoints[i].nY;
    }
    nX = (nX + nLen / 2) / nLen;
    nY = (nY + nLen / 2) / nLen;

    int *pnDists = mnew int[nLen];
    if (g_bExpired || NULL == pnDists)
    {
        return false;
    }

    for (int i = 0; i < nLen; ++i)
    {
        pnDists[i] = (nX - pstPoints[i].nX) * (nX - pstPoints[i].nX);
        pnDists[i] += (nY - pstPoints[i].nY) * (nY - pstPoints[i].nY);
    }

    bool *pbFlags = mnew bool[nLen * 2];
    if (g_bExpired || NULL == pbFlags)
    {
        mdelete(pnDists);
        return false;
    }
    memset(pbFlags, false, sizeof(bool) * nLen * 2);
    bool *pbUsed = pbFlags + nLen;

    FindLocalMax(pnDists, nLen, pbFlags, 0, nLen, 2, max(4, nLen / 64), true);

    // find four corners of maximal distance
    const int nCorners = 4;
    UN_POINT_S astCorners[nCorners];
    int anCornerDist[nCorners] = {0};
    FindMaxIndex(pnDists, nLen, anCornerDist, nCorners, pbFlags);
    for (int i = 0; i < nCorners; ++i)
    {
        astCorners[i] = pstPoints[anCornerDist[i]];
    }

    mdelete(pnDists);

    const int nLines = 4;
    UN_POINT_S *pstLines = mnew UN_POINT_S[nLen * nLines];
    int anLines[nLines] = {0};
    if (g_bExpired || NULL == pstLines)
    {
        mdelete(pbFlags);
        return false;
    }
    UN_POINT_S *pastLines[nLines];
    pastLines[0] = pstLines;
    for (int i = 1; i < nLines; ++i)
    {
        pastLines[i] = pastLines[i - 1] + nLen;
    }

    // try to find four lines
    for (int i = 0; i < nLines; ++i)
    {
        bool bAdded = true;
        while (bAdded)
        {
            bAdded = false;

            for (int j = 0; j < nLen; ++j)
            {
                if (pbUsed[j])
                {
                    continue;
                }

                if (IsEightNeighbors(astCorners, nCorners, pstPoints[j]))
                {
                    pbUsed[j] = true;
                    continue;
                }

                if (IsInLine(pastLines[i], anLines[i], pstPoints[j]))
                {
                    pastLines[i][anLines[i]++] = pstPoints[j];
                    pbUsed[j] = true;
                    bAdded = true;
                }
            }
        }
    }

    mdelete(pbFlags);

    float afErrors[nLines] = {1e6};
    float afAngles[nLines] = {0.0f};
    float fBestScore = 0.0f;
    for (int i = 0; i < nLines; ++i)
    {
        float fA = 0.0f, fB = 0.0f, fC = 0.0f;
        // fit line
        bool bFitted = FitLine(pastLines[i], anLines[i], fA, fB, fC);
        if (!bFitted)
        {
            continue;
        }

        // calculate fitting error
        afErrors[i] = 0.0f;
        for (int j = 0; j < anLines[i]; ++j)
        {
            afErrors[i] += fabs(fA * pastLines[i][j].nX + fB * pastLines[i][j].nY + fC);
        }
        afErrors[i] /= max(1, anLines[i]);

        afAngles[i] = atan2(fA, fB);
        // normalize angle to 0 ~ PI
        while (afAngles[i] < 0)
        {
            afAngles[i] = float(afAngles[i] + M_PI);
        }
        while (afAngles[i] >= M_PI)
        {
            afAngles[i] = float(afAngles[i] - M_PI);
        }

        afAngles[i] *= (float) (180.0f / M_PI);

#if 0
        float fScore = anLines[i] / max((float) M_EPS, afErrors[i]);
#else
        float fScore = 1 / max((float) M_EPS, afErrors[i]);
#endif
        if (fBestScore < fScore)
        {
            fBestScore = fScore;
            fAngle = afAngles[i];
        }
    }

    mdelete(pstLines);

    return true;
}

// check if a point is an 8-neighbor of any points
bool IsEightNeighbors(const UN_POINT_S *pstPoints, const int nLen, const UN_POINT_S &stPoint)
{
    if (g_bExpired || NULL == pstPoints || 0 >= nLen)
    {
        return false;
    }

    for (int i = 0; i < nLen; ++i)
    {
        if (1 >= abs(pstPoints[i].nX - stPoint.nX)
            && 1 >= abs(pstPoints[i].nY - stPoint.nY))
        {
            return true;
        }
    }

    return false;
}

// check if a point is an 8-neighbor of any points in current line
bool IsInLine(const UN_POINT_S *pstLine, const int nLen, const UN_POINT_S &stPoint)
{
    if (g_bExpired || NULL == pstLine || 0 > nLen)
    {
        return false;
    }

    if (0 == nLen)
    {
        return true;
    }

    return IsEightNeighbors(pstLine, nLen, stPoint);
}

// detect local minima
bool CalcLocalMinimum(const uchar *pucImg, uchar *pucSmooth,
                      const int nWidth, const int nHeight, const int nStep,
                      const uchar ucTh, LOCAL_MINIMUM_S *pstLocalMin, int &nNumber)
{
    if (g_bExpired || NULL == pucImg || NULL == pucSmooth || NULL == pstLocalMin
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    nNumber = 0;

    // get averaged image
    Mat src(nHeight, nWidth, CV_8UC1, (void *) pucImg, nWidth);
    Mat dst(nHeight, nWidth, CV_8UC1, pucSmooth, nWidth);
    blur(src, dst, Size(3, 3));

    // find candidate points
    int i = 0, j = 0;
    UN_RECT_S rtROI, rtTmp;
    rtROI = RectMake(IMAGE_BORDER, IMAGE_BORDER,
        nWidth - IMAGE_BORDER, nHeight - IMAGE_BORDER);

    // find candidate points
    int nBlockW = (rtROI.nRight - rtROI.nLeft) / nStep + 1;
    int nBlockH = (rtROI.nBottom - rtROI.nTop) / nStep + 1;
    int top = 0, btm = 0, lft = 0, rgt = 0, nIndex = 0;
    int nMinV = 255, nMinI = 0, nMinJ = 0;

    for (i = 0; i < nBlockH; i++)
    {
        top = rtROI.nTop + i * nStep;
        btm = top + nStep;
        if (btm > rtROI.nBottom)
        {
            btm = rtROI.nBottom;
        }

        for (j = 0; j < nBlockW; j++)
        {
            lft = rtROI.nLeft + j * nStep;
            rgt = lft + nStep;
            if (rgt > rtROI.nRight)
            {
                rgt = rtROI.nRight;
            }

            nMinV = 255, nMinI = 0, nMinJ = 0;
            for (int m = top; m < btm; m++)
            {
                nIndex = m * nWidth;
                for (int n = lft; n < rgt; n++)
                {
                    if (pucSmooth[nIndex + n] < nMinV)
                    {
                        nMinI = m;
                        nMinJ = n;
                        nMinV = pucSmooth[nIndex + n];
                    }
                }
            }
            if (nMinV > ucTh)
            {
                continue;
            }

            // record candidate
            pstLocalMin[nNumber].nX = nMinJ;
            pstLocalMin[nNumber].nY = nMinI;
            pstLocalMin[nNumber].ucGray = (uchar) nMinV;
            nNumber++;
        }
    }

    return true;
}

// detect local minima
bool CalcLocalMinimum(const uchar *pucImg, const int nWidth, const int nHeight,
                      const int nLeft, const int nTop, const int nRight, const int nBottom,
                      const int nStep, const uchar ucTh,
                      LOCAL_MINIMUM_S *pstLocalMin, const int nMax, int &nNumber)
{
    if (g_bExpired || NULL == pucImg || NULL == pstLocalMin
        || !IsRoiOk(nWidth, nHeight, nLeft, nTop, nRight, nBottom)
        || 0 >= nWidth || 0 >= nHeight || 0 >= nMax)
    {
        return false;
    }

    nNumber = 0;

    // find candidate points
    int i = 0, j = 0;
    UN_RECT_S rtROI, rtTmp;
    rtROI = RectMake(nLeft, nTop, nRight, nBottom);

    // find candidate points
    int nBlockW = (rtROI.nRight - rtROI.nLeft) / nStep + 1;
    int nBlockH = (rtROI.nBottom - rtROI.nTop) / nStep + 1;
    int top = 0, btm = 0, lft = 0, rgt = 0, nIndex = 0;
    int nMinV = 255, nMinI = 0, nMinJ = 0;

    for (i = 0; i < nBlockH; i++)
    {
        top = rtROI.nTop + i * nStep;
        btm = top + nStep;
        if (btm > rtROI.nBottom)
        {
            btm = rtROI.nBottom;
        }

        for (j = 0; j < nBlockW; j++)
        {
            lft = rtROI.nLeft + j * nStep;
            rgt = lft + nStep;
            if (rgt > rtROI.nRight)
            {
                rgt = rtROI.nRight;
            }

            nMinV = 255, nMinI = 0, nMinJ = 0;
            for (int m = top; m < btm; m++)
            {
                nIndex = m * nWidth;
                for (int n = lft; n < rgt; n++)
                {
                    if (pucImg[nIndex + n] < nMinV)
                    {
                        nMinI = m;
                        nMinJ = n;
                        nMinV = pucImg[nIndex + n];
                    }
                }
            }
            if (nMinV > ucTh)
            {
                continue;
            }

            // record candidate
            pstLocalMin[nNumber].nX = nMinJ;
            pstLocalMin[nNumber].nY = nMinI;
            pstLocalMin[nNumber].ucGray = (uchar) nMinV;
            nNumber++;

            if (nNumber >= nMax)
            {
                return true;
            }
        }
    }

    return true;
}

bool CalcOffset(const uchar *pucImgRef, const int nWidth, const int nHeight,
                const uchar *pucImgSap, const int nWidthIn, const int nHeightIn,
                int &nXOffset, int &nYOffset, int &nScore,
                const int nStepX/* = 4*/, const int nStepY/* = 4*/)
{
    nXOffset = 0;
    nYOffset = 0;
    if (g_bExpired || NULL == pucImgRef || NULL == pucImgSap
        || 1 > nStepX || 1 > nStepY
        || nWidthIn > nWidth || nHeightIn > nHeight)
    {
        return false;
    }
    int i = 0, j = 0, m = 0, n = 0, x = 0, y = 0;
    int nStep = 0, nValue = 0;
    double dSapWeight = 0, dRefWeight = 0, dCorrlation = 0;
    double dScore = 0.0, dMaxScore = 0.0;

    // initialize
    int nH = nHeightIn / nStepY;
    int nW = nWidthIn / nStepX;
    uchar *pucSample = mnew uchar[nW * nH];
    if (NULL == pucSample)
    {
        return false;
    }
    // compute sample image value
    int nIndex1 = 0, nIndex2 = 0;
    for (i = 0, m = 0; i < nH; i ++, m += nStepY)
    {
        nIndex1 = i * nW;
        nIndex2 = m * nWidthIn;
        for (j = 0, n = 0; j < nW; j ++, n += nStepX)
        {
            nValue = pucImgSap[nIndex2 + n];
            pucSample[nIndex1 + j] = (uchar)nValue;
            dSapWeight += nValue * nValue;
        }
    }

    // get the best offset
    nStep = 4;
    int nStartX = 0, nEndX = nWidth - nWidthIn;
    int nStartY = 0, nEndY = nHeight - nHeightIn;

    while (nStep > 0)
    {
        dScore = 0.0;
        for (y = nStartY; y <= nEndY; y += nStep)
        {
            for (x = nStartX; x <= nEndX; x += nStep)
            {
                dCorrlation = dRefWeight = 0;

                // overlay area
                m = y;
                for (i = 0; i < nH; i++, m += nStepY)
                {
                    n = x;
                    nIndex1 = m * nWidth;
                    nIndex2 = i * nW;
                    for (j = 0; j < nW; j++, n += nStepX)
                    {
                        nValue = pucImgRef[nIndex1 + n];
                        dRefWeight += nValue * nValue;
                        dCorrlation += pucSample[nIndex2 + j] * nValue;
                    }
                }

                // dCorrlation should be double, or it's easy to overflow
                dScore = dCorrlation / (sqrt(dRefWeight)*sqrt(dSapWeight)+0.1);
                if (dScore > dMaxScore)
                {
                    dMaxScore = dScore;
                    nXOffset = x;
                    nYOffset = y;
                    nScore = int(dMaxScore * 100);
                }
            }
        }

        nStartX = max(nStartX, nXOffset - nStep + 1);
        nEndX = min(nEndX, nXOffset + nStep - 1);
        nStartY = max(nStartY, nYOffset - nStep + 1);
        nEndY = min(nEndY, nYOffset + nStep - 1);

        nStep >>= 1;
    }

    mdelete(pucSample);
    return true;
}

bool FindWeakStrong(const int *pnData, const int nLength, const int nCenter,
                    const int nMin, const int nMax, int &nStart, int &nEnd)
{
    if (g_bExpired || NULL == pnData || nLength < nMin)
    {
        return false;
    }
    const int nMaxCandi = 32;
    int anCandStart[nMaxCandi] = {0}, anCandEnd[nMaxCandi] = {0};
    int nCandStart = 0, nCandEnd = 0;
    const int nMinValue = min(-32, pnData[FindMinIndex(pnData, nCenter)] / 8);
    const int nMaxValue = max(32, pnData[FindMaxIndex(pnData + nCenter, nLength - nCenter)
        + nCenter] / 8);

    for (int i = 0; i < nCenter; ++i)
    {
        if (nMinValue >= pnData[i])
        {
            anCandStart[nCandStart++] = i;
            if (nMaxCandi <= nCandStart)
            {
                break;
            }
        }
    }

    for (int i = nLength - 1; i > nCenter; --i)
    {
        if (nMaxValue <= pnData[i])
        {
            anCandEnd[nCandEnd++] = i;
            if (nMaxCandi <= nCandEnd)
            {
                break;
            }
        }
    }

    if (0 == nCandStart * nCandEnd)
    {
        return false;
    }

    int nBestEdge = 0, nBestStart = 0, nBestEnd = 0;
    int nGap = 0, nTmp = 0;
    for (int i = 0; i < nCandStart; ++i)
    {
        for (int j = 0; j < nCandEnd; ++j)
        {
            nGap = anCandEnd[j] - anCandStart[i] + 1;
            if (nMin <= nGap && nMax >= nGap)
            {
                nTmp = - pnData[anCandEnd[j]] * pnData[anCandStart[i]];
                if (nTmp > nBestEdge)
                {
                    nBestEdge = nTmp;
                    nBestStart = i;
                    nBestEnd = j;
                }
            }
        }
    }
    if (0 == nBestEdge)
    {
        return false;
    }

    nStart = anCandStart[nBestStart];
    nEnd = anCandEnd[nBestEnd];

    return true;
}

bool FindWeakStrong(const uchar *pucH, const uchar *pucV, const int nWidth, const int nHeight,
                    const int nLeft, const int nTop, const int nRight, const int nBottom,
                    int *pnCol, int *pnRow, float &fLeft, float &fTop,
                    float &fRight, float &fBottom)
{
    if (g_bExpired || NULL == pucH || NULL == pucV || NULL == pnCol || NULL == pnRow
        || !IsRoiOk(nWidth, nHeight, nLeft, nTop, nRight, nBottom))
    {
        return false;
    }

    memset(pnCol, 0, sizeof(int) * nWidth);
    memset(pnRow, 0, sizeof(int) * nHeight);

    ProjectH(pucH, nWidth, nHeight, nLeft, nRight, nTop, nBottom, pnRow, -128, false);
    ProjectV(pucV, nWidth, nHeight, nLeft, nRight, nTop, nBottom, pnCol, -128, false);

    SmoothArray(pnCol, nWidth, false);
    SmoothArray(pnRow, nHeight, false);

    const int nHalfWidth = (nRight - nLeft) / 2;
    int nIdx = FindMinIndex(pnCol + nLeft, nHalfWidth) + nLeft;
    fLeft = (float) nIdx;
    if (1 <= nIdx && nWidth - 2 >= nIdx)
    {
        fLeft += GetSubPosition(pnCol[nIdx], pnCol[nIdx - 1], pnCol[nIdx + 1]);
    }
    nIdx = FindMaxIndex(pnCol + nLeft + nHalfWidth, nRight - nHalfWidth - nLeft)
        + nLeft + nHalfWidth;
    fRight = (float) nIdx;
    if (1 <= nIdx && nWidth - 2 >= nIdx)
    {
        fRight += GetSubPosition(pnCol[nIdx], pnCol[nIdx - 1], pnCol[nIdx + 1]);
    }

    const int nHalfHeight = (nBottom - nTop) / 2;
    nIdx = FindMinIndex(pnRow + nTop, nHalfHeight) + nTop;
    fTop = (float) nIdx;
    if (1 <= nIdx && nHeight - 2 >= nIdx)
    {
        fTop += GetSubPosition(pnRow[nIdx], pnRow[nIdx - 1], pnRow[nIdx + 1]);
    }
    nIdx = FindMaxIndex(pnRow + nTop + nHalfHeight, nBottom - nHalfHeight - nTop)
        + nTop + nHalfHeight;
    fBottom = (float) nIdx;
    if (1 <= nIdx && nHeight - 2 >= nIdx)
    {
        fBottom += GetSubPosition(pnRow[nIdx], pnRow[nIdx - 1], pnRow[nIdx + 1]);
    }

    return true;
}

bool FindTwoStrong(const int *pnData, const int nLength, const int nCenter,
                   const int nMin, const int nMax, int &nStart, int &nEnd)
{
    if (g_bExpired || NULL == pnData || nLength < nMin)
    {
        return false;
    }
    const int nMaxCandi = 32;
    int anCandStart[nMaxCandi] = {0}, anCandEnd[nMaxCandi] = {0};
    int nCandStart = 0, nCandEnd = 0;
    const int nMaxValueLeft = max(32, pnData[FindMaxIndex(pnData, nCenter)] / 8);
    const int nMaxValueRight = max(32, pnData[FindMaxIndex(pnData + nCenter, nLength - nCenter)
        + nCenter] / 8);

    for (int i = 0; i < nCenter; ++i)
    {
        if (nMaxValueLeft <= pnData[i])
        {
            anCandStart[nCandStart++] = i;
            if (nMaxCandi <= nCandStart)
            {
                break;
            }
        }
    }

    for (int i = nLength - 1; i > nCenter; --i)
    {
        if (nMaxValueRight <= pnData[i])
        {
            anCandEnd[nCandEnd++] = i;
            if (nMaxCandi <= nCandEnd)
            {
                break;
            }
        }
    }

    if (0 == nCandStart * nCandEnd)
    {
        return false;
    }

    int nBestEdge = 0, nBestStart = 0, nBestEnd = 0;
    int nGap = 0, nTmp = 0;
    for (int i = 0; i < nCandStart; ++i)
    {
        for (int j = 0; j < nCandEnd; ++j)
        {
            nGap = anCandEnd[j] - anCandStart[i] + 1;
            if (nMin <= nGap && nMax >= nGap)
            {
                nTmp = pnData[anCandEnd[j]] * pnData[anCandStart[i]];
                if (nTmp > nBestEdge)
                {
                    nBestEdge = nTmp;
                    nBestStart = i;
                    nBestEnd = j;
                }
            }
        }
    }
    if (0 == nBestEdge)
    {
        return false;
    }

    nStart = anCandStart[nBestStart];
    nEnd = anCandEnd[nBestEnd];

    return true;
}

bool FindStrongWeak(const int *pnData, const int nLength, const int nCenter,
                    const int nMin, const int nMax, int &nStart, int &nEnd)
{
    if (g_bExpired || NULL == pnData || nLength < nMin)
    {
        return false;
    }
    const int nMaxCandi = 8;
    const int nTh = 32;
    int anCandStart[nMaxCandi] = {0}, anCandEnd[nMaxCandi] = {0};
    int nCandStart = 0, nCandEnd = 0;
    const int nMaxValueLeft = max(nTh, pnData[FindMaxIndex(pnData, nCenter)] / 8);
    const int nMinValueRight = min(-nTh, pnData[FindMinIndex(pnData + nCenter, nLength - nCenter)
        + nCenter] / 8);

    for (int i = 0; i < nCenter; ++i)
    {
        if (nMaxValueLeft <= pnData[i])
        {
            anCandStart[nCandStart++] = i;
            if (nMaxCandi <= nCandStart)
            {
                break;
            }
        }
    }

    for (int i = nLength - 1; i > nCenter; --i)
    {
        if (nMinValueRight >= pnData[i])
        {
            anCandEnd[nCandEnd++] = i;
            if (nMaxCandi <= nCandEnd)
            {
                break;
            }
        }
    }

    if (0 == nCandStart * nCandEnd)
    {
        return false;
    }

    int nBestEdge = 0, nBestStart = 0, nBestEnd = 0;
    int nGap = 0, nTmp = 0;
    for (int i = 0; i < nCandStart; ++i)
    {
        for (int j = 0; j < nCandEnd; ++j)
        {
            nGap = anCandEnd[j] - anCandStart[i] + 1;
            if (nMin <= nGap && nMax >= nGap)
            {
                nTmp = - pnData[anCandEnd[j]] * pnData[anCandStart[i]];
                if (nTmp > nBestEdge)
                {
                    nBestEdge = nTmp;
                    nBestStart = i;
                    nBestEnd = j;
                }
            }
        }
    }
    if (0 == nBestEdge)
    {
        return false;
    }

    nStart = anCandStart[nBestStart];
    nEnd = anCandEnd[nBestEnd];

    return true;
}

////////////////////////////////////////////////////////////////////////////////
#define  HSLMAX   255    /* ucH,ucL, and ucS vary over 0-HSLMAX */
#define  RGBMAX   255   /* ucR,ucG, and ucB vary over 0-RGBMAX */
/* HSLMAX BEST IF DIVISIBLE BY 6 */
/* RGBMAX, HSLMAX must each fit in a BYTE. */
/* Hue is undefined if Saturation is 0 (gray-scale) */
/* This value determines where the Hue scrollbar is */
/* initially set for achromatic colors */
#define HSLUNDEFINED (HSLMAX*2/3)
////////////////////////////////////////////////////////////////////////////////
void RGB2HSL(uchar &ucR, uchar &ucG, uchar &ucB)
{
    uchar ucMax, ucMin;                /* max and min RGB values */
    uchar ucH, ucS, ucL;
    int nDeltaR, nDeltaG, nDeltaB;    /* intermediate value: % of spread from max*/

    ucMax = max(max(ucR,ucG), ucB);    /* calculate lightness */
    ucMin = min(min(ucR,ucG), ucB);
    ucL = (uchar) ((((ucMax+ucMin)*HSLMAX)+RGBMAX)/(2*RGBMAX));

    if (ucMax==ucMin)
    {
        // r=g=b --> achromatic case
        ucS = 0;                // saturation
        ucH = HSLUNDEFINED;        // hue
    }
    else
    {
        // chromatic case
        if (ucL <= (HSLMAX/2))    // saturation
            ucS = (uchar)((((ucMax-ucMin)*HSLMAX)+((ucMax+ucMin)/2))/(ucMax+ucMin));
        else
            ucS = (uchar)((((ucMax-ucMin)*HSLMAX)+((2*RGBMAX-ucMax-ucMin)/2))/(2*RGBMAX-ucMax-ucMin));
        // hue
        nDeltaR = (int)((((ucMax-ucR)*(HSLMAX/6)) + ((ucMax-ucMin)/2) ) / (ucMax-ucMin));
        nDeltaG = (int)((((ucMax-ucG)*(HSLMAX/6)) + ((ucMax-ucMin)/2) ) / (ucMax-ucMin));
        nDeltaB = (int)((((ucMax-ucB)*(HSLMAX/6)) + ((ucMax-ucMin)/2) ) / (ucMax-ucMin));

        int nH = 0;
        if (ucR == ucMax)
            nH = (nDeltaB - nDeltaG);
        else if (ucG == ucMax)
            nH = ((HSLMAX/3) + nDeltaR - nDeltaB);
        else // ucB == ucMax
            nH = (((2*HSLMAX)/3) + nDeltaG - nDeltaR);

        //        if (nH < 0) nH += HSLMAX;     //always false
        if (nH > HSLMAX)
            nH -= HSLMAX;

        ucH = (uchar) nH;
    }

    ucR = ucH;
    ucG = ucS;
    ucB = ucL;
}

bool RGB2HSL(const uchar *pucImg, const int nWidth, const int nHeight,
             const int nPitch, uchar *pucH, uchar *pucS, uchar *pucL)
{
    if (g_bExpired || NULL == pucImg || NULL == pucH || NULL == pucS || NULL == pucL
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    const int nChannel = nPitch / nWidth;
    const int nDelta = nPitch - nWidth * nChannel;
    if (3 != nChannel && 4 != nChannel)
    {
        return false;
    }

    uchar ucB, ucG, ucR;
    for (int i = 0; i < nHeight; ++i)
    {
        for (int j = 0; j < nWidth; ++j)
        {
            ucB = pucImg[0];
            ucG = pucImg[1];
            ucR = pucImg[2];

            pucImg += nChannel;

            RGB2HSL(ucR, ucG, ucB);

            *pucH++ = ucR;
            *pucS++ = ucG;
            *pucL++ = ucB;
        }

        pucImg += nDelta;
    }

    return true;
}

void RGB2Yuv(uchar &ucR, uchar &ucG, uchar &ucB)
{
    const int SHIFT = 10;
    const int HALF = 1 << (SHIFT - 1);
    uchar ucY = (306 * ucR + 601 * ucG + 117 * ucB + HALF) >> SHIFT;
    uchar ucU = ((512 * ucR - 429 * ucG - 83 * ucB + HALF) >> SHIFT) + 128; //lint !e834
    uchar ucV = ((-173 * ucR - 339 * ucG + 512 * ucB + HALF) >> SHIFT) + 128; //lint !e834
    ucR = ucY;
    ucG = ucU;
    ucB = ucV;
}

bool RGB2Yuv(const uchar *pucImg, const int nWidth, const int nHeight,
             const int nPitch, uchar *pucY, uchar *pucU, uchar *pucV)
{
    if (g_bExpired || NULL == pucImg || NULL == pucY || NULL == pucU || NULL == pucV
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    const int nChannel = nPitch / nWidth;
    const int nDelta = nPitch - nWidth * nChannel;
    if (3 != nChannel && 4 != nChannel)
    {
        return false;
    }

    uchar ucB, ucG, ucR;
    for (int i = 0; i < nHeight; ++i)
    {
        for (int j = 0; j < nWidth; ++j)
        {
            ucB = pucImg[0];
            ucG = pucImg[1];
            ucR = pucImg[2];

            pucImg += nChannel;

            RGB2Yuv(ucR, ucG, ucB);

            *pucY++ = ucR;
            *pucU++ = ucG;
            *pucV++ = ucB;
        }

        pucImg += nDelta;
    }

    return true;
}

void HSL2RGB(uchar &ucH, uchar &ucS, uchar &ucL)
{
    float h, s, l;
    float m1, m2;
    uchar r, g, b;

    h = (float) ucH * 360.0f / 255.0f;
    s = (float) ucS / 255.0f;
    l = (float) ucL / 255.0f;

    if (l <= 0.5f)
    {
        m2 = l * (1 + s);
    }
    else
    {
        m2 = l + s - l * s;
    }

    m1 = 2 * l - m2;

    if (s == 0)
    {
        r = g = b = (uchar) (l * 255.0f);
    }
    else
    {
        r = (uchar) (Hue2RGB(m1, m2, h + 120) * 255.0f);
        g = (uchar) (Hue2RGB(m1, m2, h) * 255.0f);
        b = (uchar) (Hue2RGB(m1, m2, h - 120) * 255.0f);
    }

    ucH = r;
    ucS = g;
    ucL = b;
}

float Hue2RGB(float n1, float n2, float hue)
{
    float rValue;

    if (hue > 360)
    {
        hue = hue - 360;
    }
    else if (hue < 0)
    {
        hue = hue + 360;
    }

    if (hue < 60)
    {
        rValue = n1 + (n2 - n1) * hue / 60.0f;
    }
    else if (hue < 180)
    {
        rValue = n2;
    }
    else if (hue < 240)
    {
        rValue = n1 + (n2 - n1) * (240 - hue) / 60.0f;
    }
    else
    {
        rValue = n1;
    }

    return rValue;
}

bool HSL2RGB(const uchar *pucHue, const uchar *pucSat, const uchar *pucLam,
             uchar *pucImg, const int nWidth, const int nHeight, const int nPitch)
{
    if (g_bExpired || NULL == pucImg || NULL == pucHue || NULL == pucSat || NULL == pucLam
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    const int nChannel = nPitch / nWidth;
    const int nDelta = nPitch - nWidth * nChannel;
    if (3 != nChannel && 4 != nChannel)
    {
        return false;
    }

    uchar ucB, ucG, ucR;
    for (int i = 0; i < nHeight; ++i)
    {
        for (int j = 0; j < nWidth; ++j)
        {
            ucR = *pucHue++;
            ucG = *pucSat++;
            ucB = *pucLam++;

            pucImg += nChannel;

            HSL2RGB(ucR, ucG, ucB);

            pucImg[0] = ucB;
            pucImg[1] = ucG;
            pucImg[2] = ucR;
        }

        pucImg += nDelta;
    }

    return true;
}

void RGB2Lab(uchar &ucR, uchar &ucG, uchar &ucB)
{
    uchar ucL = (uchar) ((13933 * ucR + 46871 * ucG + 4732 * ucB) >> 16);
    uchar uca = (uchar) ((377 * (14503 * ucR - 22218 * ucG + 7714 * ucB) >> 24) + 128);
    uchar ucb = (uchar) ((160 * (12773 * ucR + 39695 * ucG - 52468 * ucB) >> 24) + 128);

    ucR = ucL;
    ucG = uca;
    ucB = ucb;
}

bool RGB2Lab(const uchar *pucImg, const int nWidth, const int nHeight,
             const int nPitch, uchar *pucL, uchar *puca, uchar *pucb)
{
    if (g_bExpired || NULL == pucImg || NULL == pucL || NULL == puca || NULL == pucb
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    const int nChannel = nPitch / nWidth;
    const int nDelta = nPitch - nWidth * nChannel;
    if (3 != nChannel && 4 != nChannel)
    {
        return false;
    }

    uchar ucB, ucG, ucR;
    for (int i = 0; i < nHeight; ++i)
    {
        for (int j = 0; j < nWidth; ++j)
        {
            ucB = pucImg[0];
            ucG = pucImg[1];
            ucR = pucImg[2];

            pucImg += nChannel;

            RGB2Lab(ucR, ucG, ucB);

            *pucL++ = ucR;
            *puca++ = ucG;
            *pucb++ = ucB;
        }

        pucImg += nDelta;
    }

    return true;
}

bool SplitRGB(const uchar *pucImg, const int nWidth, const int nHeight,
              const int nPitch, uchar *pucB, uchar *pucG, uchar *pucR)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (g_bExpired || NULL == pucImg || 0 >= nWidth || 0 >= nHeight
        || (3 != nChannels && 4 != nChannels)
        || NULL == pucB || NULL == pucG || NULL == pucR)
    {
        return false;
    }

    const int nDelta = nPitch - nWidth * nChannels;
    for (int i = 0; i < nHeight; ++i)
    {
        for (int j = 0; j < nWidth; ++j)
        {
            *pucB++ = pucImg[0];
            *pucG++ = pucImg[1];
            *pucR++ = pucImg[2];

            pucImg += nChannels;
        }

        pucImg += nDelta;
    }

    return true;
}

bool MergeRGB(uchar *pucImg, const int nWidth, const int nHeight, const int nPitch,
              const uchar *pucB, const uchar *pucG, const uchar *pucR)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (g_bExpired || NULL == pucImg || 0 >= nWidth || 0 >= nHeight
        || (3 != nChannels && 4 != nChannels)
        || NULL == pucB || NULL == pucG || NULL == pucR)
    {
        return false;
    }

    const int nDelta = nPitch - nWidth * nChannels;
    for (int i = 0; i < nHeight; ++i)
    {
        for (int j = 0; j < nWidth; ++j)
        {
            pucImg[0] = *pucB++;
            pucImg[1] = *pucG++;
            pucImg[2] = *pucR++;

            pucImg += nChannels;
        }

        pucImg += nDelta;
    }

    return true;
}

bool ExtractChannel(const uchar *pucImg, uchar *pucChannel, const int nWidth,
                    const int nHeight, const int nPitch, const int nChannel,
                    const int nLeft, const int nTop, const int nRight, const int nBottom)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (g_bExpired || NULL == pucImg || NULL == pucChannel
        || !IsRoiOk(nWidth, nHeight, nLeft, nTop, nRight, nBottom)
        || 0 > nChannel || nChannels <= nChannel)
    {
        return false;
    }

    for (int i = nTop; i < nBottom; ++i)
    {
        const uchar *pucTmp = pucImg + i * nPitch + nLeft * nChannels + nChannel;
        for (int j = nLeft; j < nRight; ++j)
        {
            *pucChannel++ = *pucTmp;
            pucTmp += nChannels;
        }
    }

    return true;
}

bool ExtractChannel(const uchar *pucImg, uchar *pucChannel, const int nWidth,
                    const int nHeight, const int nPitch, const int nChannel)
{
    return ExtractChannel(pucImg, pucChannel, nWidth, nHeight,
        nPitch, nChannel, 0, 0, nWidth, nHeight);
}

bool ExtractChannel(const uchar *pucImg, uchar *pucChannel, const int nWidth,
                    const int nHeight, const int nPitch, const int nChannel,
                    const UN_RECT_S &stRect)
{
    return ExtractChannel(pucImg, pucChannel, nWidth, nHeight,
        nPitch, nChannel, stRect.nLeft, stRect.nTop, stRect.nRight, stRect.nBottom);
}

bool ExtractChannel(const uchar *pucImg, uchar *pucChannel,
                    const int nWidth, const int nHeight,
                    const int nPitch, const int nChannel, const int nScale)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (g_bExpired || NULL == pucImg || NULL == pucChannel
        || !IsRoiOk(nWidth, nHeight, 0, 0, nWidth, nHeight)
        || 0 > nChannel || nChannels <= nChannel
        || 1 > nScale)
    {
        return false;
    }

    const int nW = nWidth / nScale;
    const int nH = nHeight / nScale;
    for (int i = 0; i < nH; ++i)
    {
        const uchar *pucTmp = pucImg + (i * nScale) * nPitch + nChannel;
        for (int j = 0; j < nW; ++j)
        {
            *pucChannel++ = *pucTmp;
            pucTmp += nChannels * nScale;
        }
    }

    return true;
}

bool IsRoiOk(const int nWidth, const int nHeight, const int nLeft,
             const int nTop, const int nRight, const int nBottom)
{
    if (0 > nLeft || 0 > nTop || nWidth < nRight
        || nHeight < nBottom || nLeft >= nRight || nTop >= nBottom)
    {
        return false;
    }

    return true;
}

bool IsRoiOk(const int nWidth, const int nHeight, const UN_RECT_S &stRect)
{
    return IsRoiOk(nWidth, nHeight, stRect.nLeft, stRect.nTop, stRect.nRight, stRect.nBottom);
}

bool IsRoiOk(const int nWidth, const int nHeight, const Rect &stRect)
{
    return IsRoiOk(nWidth, nHeight, stRect.x, stRect.y,
        stRect.x + stRect.width, stRect.y + stRect.height);
}

// trace contour, Moore-Neighbor Tracing
bool TraceContour(const uchar *pucImg, const int nWidth, const int nHeight,
                  const int nX, const int nY, const int nLeft, const int nTop,
                  const int nRight, const int nBottom, const int nMinTh, const int nMaxTh,
                  UN_POINT_S *pstContour, const int nMaxContour, int &nCount)
{
    if (g_bExpired || NULL == pucImg || NULL == pstContour
        || 0 >= nMaxContour || 0 >= nWidth || 0 >= nHeight
        || 0 > nX || 0 > nY || nWidth - 1 < nX || nHeight - 1 < nY
        || 0 > nMinTh || 255 < nMaxTh || nMinTh > nMaxTh
        || 255 <= nMaxTh - nMinTh
        || !IsRoiOk(nWidth, nHeight, nLeft, nTop, nRight, nBottom))
    {
        return false;
    }

    nCount = 0;
    const int nDelta = 2;
    const int nW = nRight - nLeft + nDelta * 2;
    const int nH = nBottom - nTop + nDelta * 2;
    uchar *pucSample = mnew uchar[nW * nH];
    if (NULL == pucSample)
    {
        return false;
    }

    // fill image with background gray level
    const uchar ucFill = (uchar) (0 < nMinTh ? 0 : nMaxTh + 1);
    memset(pucSample, ucFill, sizeof(pucSample[0]) * nW * nH);

    int i = 0, j = 0;
    for (i = nTop; i < nBottom; ++i)
    {
        memcpy(pucSample + (i + nDelta - nTop) * nW + nDelta,
            pucImg + i * nWidth + nLeft, nW - nDelta * 2);
    }

    // try to find the first point
    int nOriX = nX - nLeft + nDelta, nOriY = nY - nTop + nDelta;
    for (i = nDelta * nW + nDelta; i < nW * nH; ++i)
    {
        if (nMinTh <= pucSample[i] && nMaxTh >= pucSample[i])
        {
            nOriX = i % nW;
            nOriY = i / nW;
            break;
        }
    }
    int nCurX = nOriX, nCurY = nOriY;

    // add the first point
    pstContour[nCount++] = PointMake(nCurX, nCurY);

    int nNeiX = 0, nNeiY = 0;
    int nGray = 0, nCurDir = 0;
    bool bFinished = false, bFound = false;
    const DIR_E nDir = DIR_EIGHT;
    do
    {
        bFound = false;

        // visit all neighbors
        for (i = 0; i < nDir; ++i)
        {
            nNeiX = nCurX + g_anDirClockwise[(nCurDir + i) % nDir][0];
            nNeiY = nCurY + g_anDirClockwise[(nCurDir + i) % nDir][1];

            // finished tracing
            if (nNeiX == nOriX && nNeiY == nOriY)
            {
                bFinished = true;
                break;
            }

            nGray = pucSample[nNeiY * nW + nNeiX];
            if (nGray >= nMinTh && nGray <= nMaxTh)
            {
                // update direction index
                int nX = nCurX + g_anDirClockwise[(nCurDir + i - 1 + nDir) % nDir][0];
                int nY = nCurY + g_anDirClockwise[(nCurDir + i - 1 + nDir) % nDir][1];
                for (j = 0; j < nDir; ++j)
                {
                    if (nNeiX + g_anDirClockwise[j][0] == nX
                        && nNeiY + g_anDirClockwise[j][1] == nY)
                    {
                        nCurDir = j;
                        break;
                    }
                }

                // set current boundary point
                nCurX = nNeiX;
                nCurY = nNeiY;

                // push contour point into the list
                pstContour[nCount++] = PointMake(nCurX, nCurY);
                if (nMaxContour <= nCount)
                {
                    bFinished = true;
                    break;
                }

                bFound = true;
                break;
            }
        }

#if 0
        // not found a valid point or finished
        if (!bFound || bFinished)
        {
            break;
        }
    } while (true);
#else
        // not found a valid point or finished
    } while (bFound && !bFinished);
#endif

    // adjust coordinates
    for (i = 0; i < nCount; ++i)
    {
        pstContour[i].nX += nLeft - nDelta;
        pstContour[i].nY += nTop - nDelta;
    }

    mdelete(pucSample);
    return true;
}

// trace contour while tracing region
bool TraceContour(const uchar *pucImg, const int nWidth, const int nHeight,
                  const int nX, const int nY, const UN_RECT_S &stRect,
                  const int nMinTh, const int nMaxTh,
                  UN_POINT_S *pstContour, const int nMaxContour, int &nCount)
{
    return TraceContour(pucImg, nWidth, nHeight, nX, nY,
        stRect.nLeft, stRect.nTop, stRect.nRight, stRect.nBottom,
        nMinTh, nMaxTh, pstContour, nMaxContour, nCount);
}

// trace contour while tracing region
bool TraceContour(const uchar *pucImg, const int nWidth, const int nHeight,
                  const int nX, const int nY, const Rect &stRect,
                  const int nMinTh, const int nMaxTh,
                  UN_POINT_S *pstContour, const int nMaxContour, int &nCount)
{
    return TraceContour(pucImg, nWidth, nHeight, nX, nY,
        stRect.x, stRect.y, stRect.x + stRect.width, stRect.y + stRect.height,
        nMinTh, nMaxTh, pstContour, nMaxContour, nCount);
}

bool GetMainColor(const uchar *pucImg, const uchar *pucMsk,
                  const int nWidth, const int nHeight,
                  const int nPitch, const UN_RECT_S &stRect,
                  uchar &ucR, uchar &ucG, uchar &ucB)
{
    if (g_bExpired || NULL == pucImg || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    const int nChannel = nPitch / max(1, nWidth);
    if (3 != nChannel && 4 != nChannel)
    {
        return false;
    }

    int i = 0, j = 0, k = 0;
    int nMax = 0;
    int nUnit = 8;
    int nLen = 256 / nUnit;

    int *pnHist3D = mnew int[nLen * nLen * nLen];
    if (NULL == pnHist3D)
    {
        return false;
    }

    memset(pnHist3D, 0, nLen * nLen * nLen * sizeof(int));
    if (NULL == pucMsk)
    {
        for (i = stRect.nTop; i < stRect.nBottom; i++)
        {
            for (j = stRect.nLeft; j < stRect.nRight; j++)
            {
                ucB = pucImg[i * nPitch + j * nChannel];
                ucG = pucImg[i * nPitch + j * nChannel + 1];
                ucR = pucImg[i * nPitch + j * nChannel + 2];
                ucB = (uchar) (ucB / nUnit);
                ucG = (uchar) (ucG / nUnit);
                ucR = (uchar) (ucR / nUnit);
                pnHist3D[ucB * nLen * nLen + ucG * nLen + ucR]++;
            }
        }
    }
    else
    {
        for (i = stRect.nTop; i < stRect.nBottom; i++)
        {
            for (j = stRect.nLeft; j < stRect.nRight; j++)
            {
                if (0 == pucMsk[i * nWidth + j])
                {
                    continue;
                }
                ucB = pucImg[i * nPitch + j * nChannel];
                ucG = pucImg[i * nPitch + j * nChannel + 1];
                ucR = pucImg[i * nPitch + j * nChannel + 2];
                ucB = (uchar) (ucB / nUnit);
                ucG = (uchar) (ucG / nUnit);
                ucR = (uchar) (ucR / nUnit);
                pnHist3D[ucB * nLen * nLen + ucG * nLen + ucR]++;
            }
        }
    }

    nMax = 0;
    for (i = 0; i < nLen; i++)
    {
        for (j = 0; j < nLen; j++)
        {
            for (k = 0; k < nLen; k++)
            {
                if (pnHist3D[i * nLen * nLen + j * nLen + k] > nMax)
                {
                    nMax = pnHist3D[i * nLen * nLen + j * nLen + k];
                    ucB = (uchar) (i);
                    ucG = (uchar) (j);
                    ucR = (uchar) (k);
                }

            }
        }
    }

    ucB = (uchar) (ucB * nUnit + nUnit / 2);
    ucG = (uchar) (ucG * nUnit + nUnit / 2);
    ucR = (uchar) (ucR * nUnit + nUnit / 2);

    mdelete(pnHist3D);
    return true;
}

bool GetMainColor(const uchar *pucImg, const uchar *pucMsk,
                  const int nWidth, const int nHeight,
                  const int nPitch, const Rect &stRect,
                  uchar &ucR, uchar &ucG, uchar &ucB)
{
    return GetMainColor(pucImg, pucMsk, nWidth, nHeight, nPitch,
        RectMake(stRect.x, stRect.y, stRect.width, stRect.height), ucR, ucG, ucB);
}

bool GetMainColor(const uchar *pucImg, const uchar *pucMsk,
                  const int nWidth, const int nHeight,
                  const int nPitch, uchar &ucR, uchar &ucG, uchar &ucB)
{
    return GetMainColor(pucImg, pucMsk, nWidth, nHeight, nPitch,
        RectMake(0, 0, nWidth, nHeight), ucR, ucG, ucB);
}

bool IsPointInConvex(const UN_POINT_S *pstPoints,
                     const int nPoints, const UN_POINT_S &stPoint)
{
    if (g_bExpired || NULL == pstPoints || 2 >= nPoints)
    {
        return false;
    }

    for (int i = 0; i < nPoints; ++i)
    {
        const UN_POINT_S &stBase = pstPoints[i];
        const UN_POINT_S &stPre = pstPoints[(i + 1) % nPoints];

        // same point, always true
        if (stBase.nX == stPre.nX && stBase.nY == stPre.nY)
        {
            continue;
        }

        // product
        int nProduct = (stPre.nX - stBase.nX) * (stPoint.nY - stBase.nY)
            - (stPoint.nX - stBase.nX) * (stPre.nY - stBase.nY);
        if (0 > nProduct) // > || >=
        {
            return false;
        }
    }

    return true;
}

bool IsPointInConvexStrict(const UN_POINT_S *pstPoints,
                           const int nPoints, const UN_POINT_S &stPoint)
{
    if (g_bExpired || NULL == pstPoints || 2 >= nPoints)
    {
        return false;
    }

    for (int i = 0; i < nPoints; ++i)
    {
        const UN_POINT_S &stBase = pstPoints[i];
        const UN_POINT_S &stPre = pstPoints[(i + 1) % nPoints];

        // same point, always true
        if (stBase.nX == stPre.nX && stBase.nY == stPre.nY)
        {
            continue;
        }

        // product
        int nProduct = (stPre.nX - stBase.nX) * (stPoint.nY - stBase.nY)
            - (stPoint.nX - stBase.nX) * (stPre.nY - stBase.nY);
        if (0 >= nProduct) // > || >=
        {
            return false;
        }
    }

    return true;
}

// clockwise
UN_POINT_S RotatePoint(const UN_POINT_S &stPoint, const UN_POINT_S &stCenter,
                       const float fSin, const float fCos)
{
    UN_POINT_S stRot, stTmp;
    stTmp.nX = stPoint.nX - stCenter.nX;
    stTmp.nY = stPoint.nY - stCenter.nY;

    stRot.nX = int (stTmp.nX * fCos - stTmp.nY * fSin);
    stRot.nY = int (stTmp.nX * fSin + stTmp.nY * fCos);
    return PointMake(stCenter.nX + stRot.nX, stCenter.nY + stRot.nY);
}

// clockwise
UN_POINT_S RotatePoint(const UN_POINT_S &stPoint, const UN_POINT_S &stCenter,
                       const float fAngle)
{
    const float fSin = sin(fAngle);
    const float fCos = cos(fAngle);

    return RotatePoint(stPoint, stCenter, fSin, fCos);
}

// clockwise
UN_POINT_F_S RotatePoint(const UN_POINT_F_S &stPoint, const UN_POINT_F_S &stCenter,
                         const float fSin, const float fCos)
{
    UN_POINT_F_S stRot, stTmp;
    stTmp.fX = stPoint.fX - stCenter.fX;
    stTmp.fY = stPoint.fY - stCenter.fY;

    stRot.fX = stTmp.fX * fCos - stTmp.fY * fSin;
    stRot.fY = stTmp.fX * fSin + stTmp.fY * fCos;
    return PointMake(stCenter.fX + stRot.fX, stCenter.fY + stRot.fY);
}

// clockwise
UN_POINT_F_S RotatePoint(const UN_POINT_F_S &stPoint, const UN_POINT_F_S &stCenter,
                         const float fAngle)
{
    const float fSin = sin(fAngle);
    const float fCos = cos(fAngle);

    return RotatePoint(stPoint, stCenter, fSin, fCos);
}

// rotate image, angle should be radian
bool RotateImage(const uchar *pucImgIn, uchar *pucImgOut,
                 const int nWidth, const int nHeight, const int nPitch,
                 const int nCenterX, const int nCenterY, const float fAngle)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (g_bExpired || NULL == pucImgIn || NULL == pucImgOut
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || 0 == nWidth * nHeight)
    {
        return false;
    }
    //    memset(pucImgOut, 255, sizeof(uchar) * nPitch * nHeight);
    memcpy(pucImgOut, pucImgIn, sizeof(uchar) * nPitch * nHeight);

    const float fSin = sin(fAngle);
    const float fCos = cos(fAngle);
    const int nScale = (1 << 10);
    const int nCos = (int) (fCos * nScale);
    const int nSin = (int) (fSin * nScale);

    // multiply (1 << 10) converting float to int for efficiency
    const int nConX = nCenterX * nScale;
    const int nConY = nCenterY * nScale;
    int nIndex = -nPitch, nDx = 0, nDy = 0, nX = 0, nY = 0;
    int nCosX = 0, nSinX = 0, nCosY = 0, nSinY = 0;
    int nOriXX = 0, nOriYY = 0;
    int nx1 = 0, nx2 = 0, ny1 = 0, ny2 = 0;
    int nx1y1 = 0, nx2y1 = 0, nx1y2 = 0, nx2y2 = 0;
    const uchar *p1 = NULL, *p2 = NULL;
    uchar *p3 = NULL;

    nCosY = (- nCenterY - 1) * nCos;
    nSinY = (- nCenterY - 1) * nSin;
    nCosX = (- nCenterX - 1) * nCos;
    nSinX = (- nCenterX - 1) * nSin;

    for (int i = 0; i < nHeight; i++)
    {
        nIndex += nPitch;
        nDy = i - nCenterY;
        nCosY += nCos;
        nSinY += nSin;

        nOriXX = nCosX - nSinY + nConX;
        nOriYY = nSinX + nCosY + nConY;
        for (int j = 0; j < nWidth; j++)
        {
            nDx = j - nCenterX;

            // coordinates in original image
            nOriXX += nCos;
            nOriYY += nSin;
            nx1 = nOriXX & (nScale - 1);
            ny1 = nOriYY & (nScale - 1);
            nX = (nOriXX - nx1 + 512) >> 10;
            nY = (nOriYY - ny1 + 512) >> 10;

            if (0 > nX || nWidth - 1 <= nX
                || 0 > nY || nHeight - 1 <= nY)
            {
                continue;
            }

            nx2 = nScale - nx1;
            ny2 = nScale - ny1;

            nx1y1 = nx1 * ny1;
            nx1y2 = nx1 * ny2;
            nx2y1 = nx2 * ny1;
            nx2y2 = nx2 * ny2;

            // copy pixel's data
            p1 = pucImgIn + nY * nPitch + nX * nChannels;
            p2 = p1 + nPitch;
            p3 = pucImgOut + nIndex + j * nChannels;

#if 1
            for (int k = 0; k < nChannels; k++)
            {
                *(p3 + k) = (uchar) (((*p1) * nx2y2 +
                    (*(p1 + nChannels)) * nx1y2 +
                    (*p2) * nx2y1 +
                    (*(p2 + nChannels)) * nx1y1 + nScale) >> 20);
                p1++;
                p2++;
            }
#else
            *p3 = (uchar) (((*p1) * nx2y2 +
                (*(p1 + nChannels)) * nx1y2 +
                (*p2) * nx2y1 +
                (*(p2 + nChannels)) * nx1y1 + nScale) >> 20);

            p1++;
            p2++;
            *(p3 + 1) = (uchar) (((*p1) * nx2y2 +
                (*(p1 + nChannels)) * nx1y2 +
                (*p2) * nx2y1 +
                (*(p2 + nChannels)) * nx1y1 + nScale) >> 20);

            p1++;
            p2++;
            *(p3 + 2) = (uchar) (((*p1) * nx2y2 +
                (*(p1 + nChannels)) * nx1y2 +
                (*p2) * nx2y1 +
                (*(p2 + nChannels)) * nx1y1 + nScale) >> 20);
#endif
        }
    }

    return true;
}

bool RotateImage(const uchar *pucIn, uchar *pucOut, const int nWidth, const int nHeight,
                 const int nPitch, const int nPitchDst, const eAngle eAgl)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucIn || NULL == pucOut || pucIn == pucOut
        || 0 >= nWidth || 0 >= nHeight || eAll == eAgl
        || (1 != nChannels && 3 != nChannels && 4 != nChannels) || g_bExpired)
    {
        return false;
    }

    int nW = nWidth, nH = nHeight;
    if (e90 == eAgl || e270 == eAgl)
    {
        nW = nHeight;
        nH = nWidth;
    }
    const int nChannelsDst = nPitchDst / max(1, nW);
    if ((1 != nChannelsDst && 3 != nChannelsDst && 4 != nChannelsDst)
        || nChannels != nChannelsDst)
    {
        return false;
    }

    if (e0 == eAgl)
    {
        const int nCopy = min(nPitch, nPitchDst);
        for (int i = 0; i < nH; i++)
        {
            memcpy(pucOut, pucIn, sizeof(pucIn[0]) * nCopy);
            pucIn += nPitch;
            pucOut += nPitchDst;
        }
    }
    else if (e90 == eAgl)
    {
        const int nCopy = min(nW * nChannels, nPitchDst);
        for (int i = 0; i < nH; ++i)
        {
            const uchar *pucCol = pucIn + (nHeight - 1) * nPitch + i * nChannels;
            uchar *pucRow = pucOut + i * nPitchDst;
            for (int j = 0; j < nCopy; ++j)
            {
                *pucRow++ = *pucCol++;
                if (0 != j && 0 == j % nChannels)
                {
                    pucCol -= nChannels + nPitch;
                }
            }
        }
    }
    else if (e180 == eAgl)
    {
        const int nCopy = min(nPitch, nPitchDst);
        for (int i = 0; i < nH; ++i)
        {
            const uchar *pucRow = pucIn + (nH - 1 - i) * nPitch + (nW - 1) * nChannels;
            uchar *pucRowDst = pucOut + i * nPitchDst;
            for (int j = 0; j < nCopy; ++j)
            {
                *pucRowDst++ = *pucRow++;
                if (0 != j && 0 == j % nChannels)
                {
                    pucRow -= nChannels * 2;
                }
            }
        }
    }
    else if (e270 == eAgl)
    {
        const int nCopy = min(nW * nChannels, nPitchDst);
        for (int i = 0; i < nH; ++i)
        {
            const uchar *pucCol = pucIn + (nWidth - i - 1) * nChannels;
            uchar *pucRow = pucOut + i * nPitchDst;
            for (int j = 0; j < nCopy; ++j)
            {
                *pucRow++ = *pucCol++;
                if (0 != j && 0 == j % nChannels)
                {
                    pucCol += nPitch - nChannels;
                }
            }
        }
    }

    return true;
}

bool FlipV(const uchar *pucIn, uchar *pucOut, const int nWidth, const int nHeight,
           const int nPitch, const int nPitchDst)
{
    const int nChannels = nPitch / max(1, nWidth);
    const int nChannelsDst = nPitchDst / max(1, nWidth);
    if (NULL == pucIn || NULL == pucOut || pucIn == pucOut
        || 0 >= nWidth || 0 >= nHeight || nChannelsDst != nChannels
        || (1 != nChannels && 3 != nChannels && 4 != nChannels) || g_bExpired)
    {
        return false;
    }

    const int nCopy = min(nPitchDst, nPitch);
    for (int i = 0; i < nHeight; ++i)
    {
        const uchar *pucRow = pucIn + (nHeight - 1 - i) * nPitch;
        uchar *pucRowDst = pucOut + i * nPitchDst;
        memcpy(pucRowDst, pucRow, sizeof(pucRow[0]) * nCopy);
    }

    return true;
}

bool FlipV(uchar *pucImg, const int nWidth, const int nHeight)
{
    if (NULL == pucImg || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    Mem mem;
    uchar *pucRow = mem[nWidth];
    uchar *pucTop = pucImg, *pucBtm = pucImg + (nHeight - 1) * nWidth;
    for (int i = 0; i < nHeight / 2; ++i)
    {
        memcpy(pucRow, pucBtm, sizeof(pucRow[0]) * nWidth);
        memcpy(pucBtm, pucTop, sizeof(pucRow[0]) * nWidth);
        memcpy(pucTop, pucRow, sizeof(pucRow[0]) * nWidth);
        pucTop += nWidth;
        pucBtm -= nWidth;
    }

    return true;
}

bool FlipH(uchar *pucImg, const int nWidth, const int nHeight)
{
    if (NULL == pucImg || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    for (int i = 0; i < nHeight; ++i)
    {
        uchar *pucLeft = pucImg + i * nWidth;
        uchar *pucRight = pucLeft + nWidth - 1;
        for (int j = 0; j < nWidth / 2; ++j)
        {
            Swap(*pucLeft, *pucRight);
            pucLeft++;
            pucRight--;
        }
    }

    return true;
}

bool FlipH(const uchar *pucIn, uchar *pucOut, const int nWidth, const int nHeight,
           const int nPitch, const int nPitchDst)
{
    const int nChannels = nPitch / max(1, nWidth);
    const int nChannelsDst = nPitchDst / max(1, nWidth);
    if (NULL == pucIn || NULL == pucOut || pucIn == pucOut
        || 0 >= nWidth || 0 >= nHeight || nChannelsDst != nChannels
        || (1 != nChannels && 3 != nChannels && 4 != nChannels) || g_bExpired)
    {
        return false;
    }

    for (int i = 0; i < nHeight; ++i)
    {
        const uchar *pucRow = pucIn + i * nPitch + (nWidth - 1) * nChannels;
        uchar *pucRowDst = pucOut + i * nPitchDst;
        for (int j = 0; j < nWidth; ++j)
        {
            for (int k = 0; k < nChannels; ++k)
            {
                *pucRowDst++ = *pucRow++;
            }
            pucRow -= nChannels * 2;
        }
    }

    return true;
}

bool AutoLevel(uchar *pucImg, const int nWidth, const int nHeight,
               const int nPitch, const int nPercent)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucImg || 0 >= nWidth || 0 >= nHeight
        || 0 > nPercent || 100 < nPercent || g_bExpired
        || (1 != nChannels && 3 != nChannels && 4 != nChannels))
    {
        return false;
    }

    int nDivide = 100, nRealPercent = nPercent;
    if (0 == nPercent || 100 == nPercent)
    {
        // 1/255
        nRealPercent = 1;
        nDivide = 255;
    }

    Mem mem;
    uchar *pucGray = pucImg;
    if (nPitch != nWidth)
    {
        pucGray = mem[nHeight * nWidth];
        CopyGrayData(pucImg, pucGray, nWidth, nHeight, nPitch);
    }

    // get histogram
    const int nScale = 1;
    int anHist[256] = {0};
    for (int i = 0; i < nHeight; i += nScale)
    {
        uchar *pucRow = pucGray + i * nWidth;
        for (int j = 0; j < nWidth; j += nScale)
        {
            anHist[*pucRow]++;
            pucRow += nScale;
        }
    }

    // adjust histogram
    const int nTotal = (nHeight / nScale) * (nWidth / nScale) - anHist[0];
    const int nCntTh = max(4, nTotal * nRealPercent / nDivide);
    int nMax = 1, nMin = 0;
    int nSum = 0;
    for (int i = 255; i > 0; --i)
    {
        nSum += anHist[i];
        if (nSum >= nCntTh)
        {
            nMax = i;
            break;
        }
    }

    nSum = 0;
    for (int i = 1; i < nMax; ++i)
    {
        nSum += anHist[i];
        if (nSum >= nCntTh)
        {
            nMin = i;
            break;
        }
    }


    const int nInterval = nMax - nMin;
    if (0 >= nInterval)
    {
        return false;
    }

    for (int i = 0; i <= nMin; ++i)
    {
        anHist[i] = 0;
    }
    for (int i = nMax; i <= 255; ++i)
    {
        anHist[i] = 255;
    }
    for (int i = nMin + 1; i < nMax; ++i)
    {
        anHist[i] = (i - nMin) * 255 / nInterval;
    }

    // adjust image
    if (1 == nChannels)
    {
        for (int i = 0; i < nHeight; ++i)
        {
            uchar *pucRow = pucImg + i * nPitch;
            for (int j = 0; j < nWidth; ++j)
            {
                *pucRow = (uchar) anHist[*pucRow];
                pucRow++;
            }
        }
    }
    else
    {
        for (int i = 0; i < nHeight; ++i)
        {
            uchar *pucRow = pucImg + i * nPitch;
            for (int j = 0; j < nWidth; ++j)
            {
                *pucRow = (uchar) anHist[*pucRow];
                *(pucRow + 1) = (uchar) anHist[*(pucRow + 1)];
                *(pucRow + 2) = (uchar) anHist[*(pucRow + 2)];
                pucRow += nChannels;
            }
        }
    }

    return true;
}

bool AdjustSaturation(uchar *pucImg, const int nWidth, const int nHeight,
                      const int nPitch, const int nSatDelta)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucImg || 0 >= nWidth || 0 >= nHeight
        || (3 != nChannels && 4 != nChannels)
        || (-100 > nSatDelta || 100 < nSatDelta))
    {
        return false;
    }

    uchar aucTable[256] = {0};
    int i = 0, j = 0;
    for (i = 0; i < 256; i++)
    {
        aucTable[i] = (uchar) max(0, min(255, (int) (i + nSatDelta)));
    }

    uchar ucR = 0, ucG = 0, ucB = 0;
    for (i = 0; i < nHeight; ++i)
    {
        uchar *pucRow = pucImg + i * nPitch;
        for (j = 0; j < nWidth; ++j)
        {
            ucB = pucRow[0];
            ucG = pucRow[1];
            ucR = pucRow[2];

            RGB2HSL(ucR, ucG, ucB);
            ucG = aucTable[ucG];
            HSL2RGB(ucR, ucG, ucB);

            pucRow[0] = ucB;
            pucRow[1] = ucG;
            pucRow[2] = ucR;

            pucRow += nChannels;
        }
    }

    return true;
}

bool AdjustContrast(uchar *pucImg, const int nWidth, const int nHeight,
                    const int nPercent, int *pnHist/* = NULL*/)
{
    if (0 > nPercent || 99 < nPercent)
    {
        return false;
    }

    int i, j, maxVal, minVal;
    int sum, total;
    int anHist[256] = {0};
    const int nStep = 4;
    int index = 0;

    for (i = 0; i < nHeight; i += nStep)
    {
        index = i * nWidth;
        for (j = 0; j < nWidth; j += nStep)
        {
            anHist[pucImg[index + j]]++;
        }
    }

    total = nWidth / nStep * nHeight / nStep - anHist[0] * nStep * nStep;
    minVal = total * nPercent / 100;
    maxVal = total / 100;

    sum = 0;
    for (i = 1; i < 255; i++)
    {
        sum += anHist[i];
        if (sum > minVal) break;
    }
    minVal = i;

    sum = 0;
    for (i = 255; i > 0; i--)
    {
        sum += anHist[i];
        if (sum > maxVal) break;
    }
    maxVal = i;
    total = maxVal - minVal;

    // too narrow
    if (32 > total)
    {
        if (NULL == pnHist)
        {
            for (i = 0; i < 256; ++i)
            {
                pnHist[i] = i;
            }
        }
        return false;
    }

    for (i = 0; i <= minVal; i++)
        anHist[i] = max(0, i >> 1);
    sum = max(0, minVal >> 1);
    for (i = maxVal; i < 256; i++)
        anHist[i] = min(255, i << 1);
    index = min(255, maxVal << 1);
    for (i = minVal+1; i < maxVal; i++)
        anHist[i] = min(255, (i-minVal)*index / total + sum);
    for (i = 0; i < nHeight; i++)
    {
        for (j = 0; j < nWidth; j++)
        {
            *pucImg = (uchar) (anHist[*pucImg]);
            pucImg++;
        }
    }
    if (NULL != pnHist)
    {
        memcpy(pnHist, anHist, sizeof(anHist));
    }

    return true;
}

bool AdjustContrast(uchar *pucImg, const int nWidth, const int nHeight,
                    const int nPitch, const int nPercent)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucImg || 0 >= nWidth || 0 >= nHeight
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || (0 > nPercent || 99 < nPercent))
    {
        return false;
    }

    // continues memory
    if (nPitch == nWidth)
    {
        return AdjustContrast(pucImg, nWidth, nHeight, nPercent);
    }

    Mem mem;
    uchar *pucGray = mem[nWidth * nHeight];
    if (NULL == pucGray)
    {
        return false;
    }

    CopyGrayData(pucImg, pucGray, nWidth, nHeight, nPitch);

    int anHist[256] = {0};
    bool bOk = AdjustContrast(pucGray, nWidth, nHeight, nPercent, anHist);
    if (!bOk)
    {
        return false;
    }

    if (1 == nChannels)
    {
        for (int i = 0; i < nHeight; ++i)
        {
            memcpy(pucImg, pucGray, sizeof(pucGray[0]) * nWidth);
            pucGray += nWidth;
            pucImg += nPitch;
        }
        return true;
    }

    const int nDelta = nPitch - nChannels * nWidth;
    for (int i = 0; i < nHeight; ++i)
    {
        for (int j = 0; j < nWidth; ++j)
        {
            for (int k = 0; k < min(3, nChannels); k++)
            {
                pucImg[k] = (uchar) anHist[pucImg[k]];
            }

            pucImg += nChannels;
        }

        pucImg += nDelta;
    }

    return true;
}

void EllipticAxis(const float fM11, const float fM02, const float fM20,
                  float &fAngle, float &fRa, float &fRb)
{

    fAngle = -0.5f * atan2(2.0f * fM11, fM02 - fM20);
    fAngle = (float) ((fAngle - M_PI / 2) * 180 / M_PI);
    fRa = sqrtf(8.0f * (fM20 + fM02
        + sqrtf((fM20 - fM02) * (fM20 - fM02) + 4.0f * fM11 * fM11))) / 2.0f;
    fRb = sqrtf(8.0f * (fM20 + fM02
        - sqrtf((fM20 - fM02) * (fM20 - fM02) + 4.0f * fM11 * fM11))) / 2.0f;
}

bool CalcRegionAngle(const uchar *pucMsk, const int nWidth, const int nHeight,
                     const uchar ucLabel, const UN_RECT_S &stRect, float &fAngle,
                     float *pRa/* = NULL*/, float *pRb/* = NULL*/)
{
    if (g_bExpired || NULL == pucMsk || 0 >= nWidth || 0 >= nHeight
        || !IsRoiOk(nWidth, nHeight, stRect))
    {
        return false;
    }

    float fX = 0.0f, fY = 0.0f;
    int nCount = 0;
    for (int i = stRect.nTop; i < stRect.nBottom; ++i)
    {
        int nIndex = i * nWidth;
        for (int j = stRect.nLeft; j < stRect.nRight; ++j)
        {
            if (ucLabel == pucMsk[nIndex + j])
            {
                fX += j;
                fY += i;
                nCount++;
            }
        }
    }

    if (0 >= nCount)
    {
        return false;
    }
    fX /= max(1, nCount);
    fY /= max(1, nCount);

    float fM11 = 0.0f, fM02 = 0.0f, fM20 = 0.0f;
    for (int i = stRect.nTop; i < stRect.nBottom; ++i)
    {
        int nIndex = i * nWidth;
        for (int j = stRect.nLeft; j < stRect.nRight; ++j)
        {
            if (ucLabel == pucMsk[nIndex + j])
            {
                fM11 += (j - fX) * (i - fY);
                fM02 += (i - fY) * (i - fY);
                fM20 += (j - fX) * (j - fX);
            }
        }
    }
    fM11 /= max(1, nCount);
    fM02 /= max(1, nCount);
    fM20 /= max(1, nCount);

    float fRa = 0.0f, fRb = 0.0f;
    EllipticAxis(fM11, fM02, fM20, fAngle, fRa, fRb);

    if (NULL != pRa)
    {
        *pRa = fRa;
    }
    if (NULL != pRb)
    {
        *pRb = fRb;
    }

    return true;
}

bool CalcRegionAngle(const uchar *pucImg, const int nWidth, const int nHeight,
                     const uchar ucMin, const uchar ucMax,
                     const UN_RECT_S &stRect, float &fAngle,
                     float *pRa/* = NULL*/, float *pRb/* = NULL*/)
{
    if (g_bExpired || NULL == pucImg || 0 >= nWidth || 0 >= nHeight
        || !IsRoiOk(nWidth, nHeight, stRect))
    {
        return false;
    }

    float fX = 0.0f, fY = 0.0f;
    int nCount = 0;
    for (int i = stRect.nTop; i < stRect.nBottom; ++i)
    {
        int nIndex = i * nWidth;
        for (int j = stRect.nLeft; j < stRect.nRight; ++j)
        {
            if (ucMin <= pucImg[nIndex + j] && ucMax >= pucImg[nIndex + j])
            {
                fX += j;
                fY += i;
                nCount++;
            }
        }
    }

    if (0 >= nCount)
    {
        return false;
    }
    fX /= max(1, nCount);
    fY /= max(1, nCount);

    float fM11 = 0.0f, fM02 = 0.0f, fM20 = 0.0f;
    for (int i = stRect.nTop; i < stRect.nBottom; ++i)
    {
        int nIndex = i * nWidth;
        for (int j = stRect.nLeft; j < stRect.nRight; ++j)
        {
            if (ucMin <= pucImg[nIndex + j] && ucMax >= pucImg[nIndex + j])
            {
                fM11 += (j - fX) * (i - fY);
                fM02 += (i - fY) * (i - fY);
                fM20 += (j - fX) * (j - fX);
            }
        }
    }
    fM11 /= max(1, nCount);
    fM02 /= max(1, nCount);
    fM20 /= max(1, nCount);

    float fRa = 0.0f, fRb = 0.0f;
    EllipticAxis(fM11, fM02, fM20, fAngle, fRa, fRb);

    if (NULL != pRa)
    {
        *pRa = fRa;
    }
    if (NULL != pRb)
    {
        *pRb = fRb;
    }

    return true;
}

bool CalcContourAngle(const UN_POINT_S *pstPoints, const int nPoints,
                      float &fAngle, float *pRa /* = NULL */, float *pRb /* = NULL */)
{
    if (g_bExpired || NULL == pstPoints || 1 >= nPoints)
    {
        return false;
    }

    float fX = 0.0f, fY = 0.0f;
    for (int i = 0; i < nPoints; ++i)
    {
        fX += pstPoints[i].nX;
        fY += pstPoints[i].nY;
    }
    fX /= nPoints;
    fY /= nPoints;

    float fM11 = 0.0f, fM02 = 0.0f, fM20 = 0.0f;
    for (int i = 0; i < nPoints; ++i)
    {
        fM11 += (pstPoints[i].nX - fX) * (pstPoints[i].nY - fY);
        fM02 += (pstPoints[i].nY - fY) * (pstPoints[i].nY - fY);
        fM20 += (pstPoints[i].nX - fX) * (pstPoints[i].nX - fX);
    }
    fM11 /= max(1, nPoints);
    fM02 /= max(1, nPoints);
    fM20 /= max(1, nPoints);

    float fRa = 0.0f, fRb = 0.0f;
    EllipticAxis(fM11, fM02, fM20, fAngle, fRa, fRb);

    if (NULL != pRa)
    {
        *pRa = fRa;
    }
    if (NULL != pRb)
    {
        *pRb = fRb;
    }

    return true;
}

void AdjustAngle(float &fAngle)
{
    const float fPi = 180.0f;
    while (0.0f > fAngle)
    {
        fAngle += fPi;
    }

    while (fPi <= fAngle)
    {
        fAngle -= fPi;
    }

    const float fHalfPi = fPi / 2, fFouthPi = fPi / 4;
    if (fFouthPi < fAngle && fFouthPi + fHalfPi >= fAngle)
    {
        fAngle -= fHalfPi;
    }
    else if (fFouthPi + fHalfPi < fAngle && fPi > fAngle)
    {
        fAngle -= fPi;
    }
}

bool LocalThreshold(const uchar *pucImg, uchar *pucBin, const int nWidth, const int nHeight,
                    const int nSize /* = 49 */, const int nTh /* = 5 */,
                    const LT_TYPE_E eType /* = LT_DARK */)
{
    if (g_bExpired || NULL == pucImg || NULL == pucBin
        || 0 >= nWidth || 0 >= nHeight || 0 >= nSize
        || nSize > min(nWidth, nHeight) || 0 > nTh || 255 < nTh)
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC1, (void *) pucImg, nWidth);
    Mat dst(nHeight, nWidth, CV_8UC1, pucBin, nWidth);

    blur(src, dst, Size(nSize, nSize));

    switch (eType)
    {
    case LT_DARK:
        for (int i = 0; i < nWidth * nHeight; ++i)
        {
            if (*pucImg <= *pucBin - nTh)
            {
                *pucBin = 0;
            }
            else
            {
                *pucBin = 255;
            }
            pucBin++;
            pucImg++;
        }
        break;
    case LT_LIGHT:
        for (int i = 0; i < nWidth * nHeight; ++i)
        {
            if (*pucImg >= *pucBin + nTh)
            {
                *pucBin = 255;
            }
            else
            {
                *pucBin = 0;
            }
            pucBin++;
            pucImg++;
        }
        break;
    case LT_EQUAL:
        for (int i = 0; i < nWidth * nHeight; ++i)
        {
            if (*pucImg >= *pucBin - nTh
                && *pucImg <= *pucBin + nTh)
            {
                *pucBin = 255;
            }
            else
            {
                *pucBin = 0;
            }
            pucBin++;
            pucImg++;
        }
        break;
    case LT_NOT_EQUAL:
        for (int i = 0; i < nWidth * nHeight; ++i)
        {
            if (*pucImg < *pucBin - nTh
                || *pucImg > *pucBin + nTh)
            {
                *pucBin = 255;
            }
            else
            {
                *pucBin = 0;
            }
            pucBin++;
            pucImg++;
        }
        break;
    default:
        return false;
    }

    return true;
}

// ref Halcon dyn_threshold
bool LocalThresholdH(const uchar *pucImg, uchar *pucBin, const int nWidth,
                     const int nHeight, const int nSize /* = 49 */,
                     const int nTh /* = 5 */, const LT_TYPE_E eType/* = LT_DARK*/)
{
    if (g_bExpired || NULL == pucImg || NULL == pucBin
        || 0 >= nWidth || 0 >= nHeight || 0 >= nSize
        || nSize > nWidth || 0 > nTh || 255 < nTh)
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC1, (void *) pucImg, nWidth);
    Mat dst(nHeight, nWidth, CV_8UC1, pucBin, nWidth);

    blur(src, dst, Size(nSize, 1));

    switch (eType)
    {
    case LT_DARK:
        for (int i = 0; i < nWidth * nHeight; ++i)
        {
            if (*pucImg <= *pucBin - nTh)
            {
                *pucBin = 0;
            }
            else
            {
                *pucBin = 255;
            }
            pucBin++;
            pucImg++;
        }
        break;
    case LT_LIGHT:
        for (int i = 0; i < nWidth * nHeight; ++i)
        {
            if (*pucImg >= *pucBin + nTh)
            {
                *pucBin = 255;
            }
            else
            {
                *pucBin = 0;
            }
            pucBin++;
            pucImg++;
        }
        break;
    case LT_EQUAL:
        for (int i = 0; i < nWidth * nHeight; ++i)
        {
            if (*pucImg >= *pucBin - nTh
                && *pucImg <= *pucBin + nTh)
            {
                *pucBin = 255;
            }
            else
            {
                *pucBin = 0;
            }
            pucBin++;
            pucImg++;
        }
        break;
    case LT_NOT_EQUAL:
        for (int i = 0; i < nWidth * nHeight; ++i)
        {
            if (*pucImg < *pucBin - nTh
                || *pucImg > *pucBin + nTh)
            {
                *pucBin = 255;
            }
            else
            {
                *pucBin = 0;
            }
            pucBin++;
            pucImg++;
        }
        break;
    default:
        return false;
    }

    return true;
}

// ref Halcon dyn_threshold
bool LocalThresholdV(const uchar *pucImg, uchar *pucBin, const int nWidth,
                     const int nHeight, const int nSize /* = 49 */,
                     const int nTh /* = 5 */, const LT_TYPE_E eType/* = LT_DARK*/)
{
    if (g_bExpired || NULL == pucImg || NULL == pucBin
        || 0 >= nWidth || 0 >= nHeight || 0 >= nSize
        || nSize > nHeight || 0 > nTh || 255 < nTh)
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC1, (void *) pucImg, nWidth);
    Mat dst(nHeight, nWidth, CV_8UC1, pucBin, nWidth);

    blur(src, dst, Size(1, nSize));

    switch (eType)
    {
    case LT_DARK:
        for (int i = 0; i < nWidth * nHeight; ++i)
        {
            if (*pucImg <= *pucBin - nTh)
            {
                *pucBin = 0;
            }
            else
            {
                *pucBin = 255;
            }
            pucBin++;
            pucImg++;
        }
        break;
    case LT_LIGHT:
        for (int i = 0; i < nWidth * nHeight; ++i)
        {
            if (*pucImg >= *pucBin + nTh)
            {
                *pucBin = 255;
            }
            else
            {
                *pucBin = 0;
            }
            pucBin++;
            pucImg++;
        }
        break;
    case LT_EQUAL:
        for (int i = 0; i < nWidth * nHeight; ++i)
        {
            if (*pucImg >= *pucBin - nTh
                && *pucImg <= *pucBin + nTh)
            {
                *pucBin = 255;
            }
            else
            {
                *pucBin = 0;
            }
            pucBin++;
            pucImg++;
        }
        break;
    case LT_NOT_EQUAL:
        for (int i = 0; i < nWidth * nHeight; ++i)
        {
            if (*pucImg < *pucBin - nTh
                || *pucImg > *pucBin + nTh)
            {
                *pucBin = 255;
            }
            else
            {
                *pucBin = 0;
            }
            pucBin++;
            pucImg++;
        }
        break;
    default:
        return false;
    }

    return true;
}

int Roundness(const UN_POINT_S *pstContour, const int nLen)
{
    if (g_bExpired || NULL == pstContour || 1 >= nLen)
    {
        return false;
    }

    int nX = 0, nY = 0;
    for (int i = 0; i < nLen; ++i)
    {
        nX += pstContour[i].nX;
        nY += pstContour[i].nY;
    }
    nX = (nX + nLen / 2) / nLen;
    nY = (nY + nLen / 2) / nLen;

    float fDist = 0.0f;
    for (int i = 0; i < nLen; ++i)
    {
        int nDeltaX = nX - pstContour[i].nX;
        int nDeltaY = nY - pstContour[i].nY;
        fDist += sqrtf(nDeltaX * nDeltaX + nDeltaY * nDeltaY + 0.0f);
    }
    fDist /= nLen;

    float fSigma = 0.0f;
    for (int i = 0; i < nLen; ++i)
    {
        int nDeltaX = nX - pstContour[i].nX;
        int nDeltaY = nY - pstContour[i].nY;
        float fTmp = sqrtf(nDeltaX * nDeltaX + nDeltaY * nDeltaY + 0.0f);
        fSigma += (fTmp - fDist) * (fTmp - fDist);
    }
    fSigma /= nLen;
    fSigma = sqrtf(fSigma);

    return (int) (100 * (1 - fSigma / fDist));
}

// fast adaptive non-minimum suppression algorithm
bool AdaptiveNonMinSuppress(LOCAL_MINIMUM_S *pstMinima, const int nMinima,
                            const int nWidth, const int nHeight, const int nWantNum)
{
    if (g_bExpired || NULL == pstMinima || 0 >= nMinima || 0 >= nWantNum
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    // want more than already have
    if (nMinima <= nWantNum)
    {
        return true;
    }

    std::sort(pstMinima, pstMinima + nMinima);

    int *pnDist = mnew int[nMinima];
    if (NULL == pnDist)
    {
        return false;
    }
    memset(pnDist, 0, sizeof(int) * nMinima);

    // calculate distance
    int nX = max(pstMinima[0].nX, nWidth - pstMinima[0].nX);
    int nY = max(pstMinima[0].nY, nHeight - pstMinima[0].nY);
    const int nMaxDist = nX * nX + nY * nY;
    pnDist[0] = nMaxDist;

    for (int i = 1; i < nMinima; ++i)
    {
        int nMinDist = nMaxDist, nTmp = 0;

        // first step, calculating distances doesn't consider
        // the pixels that have the same gray value
        for (int j = 0; j < nMinima && pstMinima[i].ucGray > pstMinima[j].ucGray; ++j)
        {
            if (j == i)
            {
                continue;
            }
            nX = pstMinima[i].nX - pstMinima[j].nX;
            nY = pstMinima[i].nY - pstMinima[j].nY;
            nTmp = nX * nX + nY * nY;
            if (nTmp < nMinDist)
            {
                nMinDist = nTmp;
            }
        }

        pnDist[i] = nMinDist;
    }

    for (int i = 0; i < nWantNum; ++i)
    {
        int nMaxDistTmp = pnDist[i], nMaxIndex = i;
        for (int j = i + 1; j < nMinima; ++j)
        {
            if (pnDist[j] > nMaxDistTmp)
            {
                nMaxDistTmp = pnDist[j];
                nMaxIndex = j;
            }
        }

        // when a minimum is going to be recorded
        // recalculate minimal distance of those pixels of the same gray value
        // once a minimum is added, the pixels that nearby with the same value shouldn't be added
        for (int j = i; j < nMinima; ++j)
        {
            if (pstMinima[j].ucGray != pstMinima[nMaxIndex].ucGray
                || j == nMaxIndex)
            {
                continue;
            }

            nX = pstMinima[j].nX - pstMinima[nMaxIndex].nX;
            nY = pstMinima[j].nY - pstMinima[nMaxIndex].nY;
            pnDist[j] = min(nX * nX + nY * nY, pnDist[j]);
        }

        // swap the maximum to the front
        Swap(pstMinima[i], pstMinima[nMaxIndex]);
        Swap(pnDist[i], pnDist[nMaxIndex]);
    }

    mdelete(pnDist);

    return true;
}

// fast adaptive non-minimum suppression algorithm
bool AdaptiveNonMinSuppressEx(LOCAL_MINIMUM_S *pstMinima, const int nMinima,
                              const int nWidth, const int nHeight, const int nWantNum)
{
    if (g_bExpired || NULL == pstMinima || 0 >= nMinima || 0 >= nWantNum
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    // want more than already have
    if (nMinima <= nWantNum)
    {
        return true;
    }

    std::sort(pstMinima, pstMinima + nMinima);

    int *pnDist = mnew int[nMinima * 2];
    if (NULL == pnDist)
    {
        return false;
    }
    memset(pnDist, 0, sizeof(int) * nMinima * 2);
    int *pnDistSort = pnDist + nMinima;

    // calculate distance
    int nX = max(pstMinima[0].nX, nWidth - pstMinima[0].nX);
    int nY = max(pstMinima[0].nY, nHeight - pstMinima[0].nY);
    const int nMaxDist = nX * nX + nY * nY;
    pnDist[0] = nMaxDist;

    for (int i = 1; i < nMinima; ++i)
    {
        int nMinDist = nMaxDist, nTmp = 0;

        // first step, calculating distances doesn't consider
        // the pixels that have the same gray value
        // and also do not consider the previous pixels with small distance
        for (int j = 0; j < nMinima && pstMinima[i].ucGray > pstMinima[j].ucGray; ++j)
        {
            if (j == i)
            {
                continue;
            }
            nX = pstMinima[i].nX - pstMinima[j].nX;
            nY = pstMinima[i].nY - pstMinima[j].nY;
            nTmp = nX * nX + nY * nY;
            if (nTmp < nMinDist)
            {
                nMinDist = nTmp;
            }
        }

        pnDist[i] = nMinDist;
    }

    memcpy(pnDistSort, pnDist, sizeof(int) * nMinima);
    std::sort(pnDistSort, pnDistSort + nMinima);

    int nCount = 0;
    int nDistIndex = nMinima - 1;
    while (nCount < nWantNum && 0 <= nDistIndex)
    {
        int nCurDist = pnDistSort[nDistIndex--];

        for (int i = nCount; i < nMinima; ++i)
        {
            if (pnDist[i] >= nCurDist)
            {
                // change distance of pixels of the same gray value
                for (int j = nCount + 1; j < nMinima; ++j)
                {
                    if (j != i && pstMinima[j].ucGray == pstMinima[i].ucGray)
                    {
                        nX = pstMinima[i].nX - pstMinima[j].nX;
                        nY = pstMinima[i].nY - pstMinima[j].nY;
                        pnDist[j] = min(pnDist[j], nX * nX + nY * nY);
                    }
                }

                Swap(pstMinima[nCount], pstMinima[i]);
                Swap(pnDist[nCount], pnDist[i]);
                nCount++;
            }
        }
    }

    mdelete(pnDist);

    return true;
}

// fast adaptive non-minimum suppression with a distance threshold
// local minima would have a minimal distance as nDistTh indicates
bool AdaptiveNonMinSuppressTh(LOCAL_MINIMUM_S *pstMinima, const int nMinima,
                              const int nWidth, const int nHeight,
                              const int nDistTh, int &nWantNum)
{
    if (g_bExpired || NULL == pstMinima || 0 >= nMinima || 0 >= nWantNum
        || 0 >= nWidth || 0 >= nHeight || 0 >= nDistTh)
    {
        return false;
    }

    // want more than already have
    if (nMinima <= nWantNum)
    {
        return true;
    }

    const int nDistTh2 = nDistTh * nDistTh;
    std::sort(pstMinima, pstMinima + nMinima);

    int *pnDist = mnew int[nMinima];
    if (NULL == pnDist)
    {
        return false;
    }
    memset(pnDist, 0, sizeof(int) * nMinima);

    // calculate distance
    int nX = max(pstMinima[0].nX, nWidth - pstMinima[0].nX);
    int nY = max(pstMinima[0].nY, nHeight - pstMinima[0].nY);
    const int nMaxDist = nX * nX + nY * nY;
    pnDist[0] = nMaxDist;

    for (int i = 1; i < nMinima; ++i)
    {
        int nMinDist = nMaxDist, nTmp = 0;

        // first step, calculating distances doesn't consider
        // the pixels that have the same gray value
        // and also do not consider the previous pixels with small distance
        for (int j = 0; j < nMinima && pstMinima[i].ucGray > pstMinima[j].ucGray; ++j)
        {
            if (j == i || (j < i && nDistTh2 > pnDist[j]))
            {
                continue;
            }
            nX = pstMinima[i].nX - pstMinima[j].nX;
            nY = pstMinima[i].nY - pstMinima[j].nY;
            nTmp = nX * nX + nY * nY;
            if (nTmp < nMinDist)
            {
                nMinDist = nTmp;
            }
        }

        pnDist[i] = nMinDist;
    }

    for (int i = 0; i < nWantNum; ++i)
    {
        int nMaxDistTmp = pnDist[i], nMaxIndex = i;
        for (int j = i + 1; j < nMinima; ++j)
        {
            if (pnDist[j] > nMaxDistTmp)
            {
                nMaxDistTmp = pnDist[j];
                nMaxIndex = j;
            }
        }

        // check minimal distance
        if (nDistTh2 > nMaxDistTmp)
        {
            nWantNum = i;
            break;
        }

        // when a minimum is going to be recorded
        // recalculate minimal distance of those pixels of the same gray value
        // once a minimum is added, the pixels that nearby with the same value shouldn't be added
        for (int j = i; j < nMinima; ++j)
        {
            if (pstMinima[j].ucGray != pstMinima[nMaxIndex].ucGray
                || j == nMaxIndex)
            {
                continue;
            }

            nX = pstMinima[j].nX - pstMinima[nMaxIndex].nX;
            nY = pstMinima[j].nY - pstMinima[nMaxIndex].nY;
            pnDist[j] = min(nX * nX + nY * nY, pnDist[j]);
        }

        // swap the maximum to the front
        Swap(pstMinima[i], pstMinima[nMaxIndex]);
        Swap(pnDist[i], pnDist[nMaxIndex]);
    }

    mdelete(pnDist);

    return true;
}

bool DistanceTrans(const uchar *pucImg, const int nWidth, const int nHeight, int *pnDist)
{
    if (g_bExpired || NULL == pucImg || NULL == pnDist || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    int i = 0, j = 0;
    int d1, d2, d3, d4;
    const int a = 3, b = 4, k = 3;
    const int nImageSize = nWidth * nHeight;
    int *pnTmpImg = mnew int[nImageSize];
    if (NULL == pnTmpImg)
    {
        return false;
    }

    // (1) fill 0 at places of interest
    // (2) fill MAX_DIST where is background
    const int MAX_DIST = (int) (sqrtf((float)(nWidth * nWidth + nHeight * nHeight)) + 1);
    int *pnOut = pnDist, *pnTmp = pnTmpImg;
    for (i = 0; i < nImageSize; i++)
    {
        *pnOut = *pucImg++;
        *pnTmp++ = *pnOut++;
    }

    // first pass: scan from top-left corner
    pnTmp = pnTmpImg;
    for (i = 0; i < nHeight; i++)
    {
        for (j = 0; j < nWidth; j++)
        {
            if (0 == (*pnTmp))
            {
                pnTmp++;
                continue;
            }

            d1 = d2 = d3 = d4 = MAX_DIST;
            if (j > 0 && i > 0)
            {
                d1 = *(pnTmp - 1 - nWidth) + b;
            }
            if (i > 0)
            {
                d2 = *(pnTmp - nWidth) + a;
            }
            if (i > 0 && j+1 < nWidth)
            {
                d3 = *(pnTmp + 1 - nWidth) + b;
            }
            if (j > 0)
            {
                d4 = *(pnTmp - 1) + a;
            }

            *pnTmp = min(min(d1, d2), min(d3, d4));
            pnTmp++;
        }
    }

    // second pass: scan from bottom-right corner
    pnOut = pnDist + nImageSize - 1;
    pnTmp = pnTmpImg + nImageSize - 1;
    for (i = nHeight - 1; i >= 0; i--)
    {
        for (j = nWidth - 1; j >= 0; j--)
        {
            if (0 == (*pnOut))
            {
                pnOut--;
                pnTmp--;
                continue;
            }

            d1 = d2 = d3 = d4 = MAX_DIST;
            if (j + 1 < nWidth)
            {
                d1 = *(pnOut + 1) + a;
            }
            if (i + 1 < nHeight && j > 0)
            {
                d2 = *(pnOut - 1 + nWidth) + b;
            }
            if (i + 1 < nHeight)
            {
                d3 = *(pnOut + nWidth) + a;
            }
            if (i + 1 < nHeight && j + 1 < nWidth)
            {
                d4 = *(pnOut + 1 + nWidth) + b;
            }

            *pnOut = min(*pnTmp, min(min(d1, d2), min(d3, d4)));
            pnOut--;
            pnTmp--;
        }
    }

    pnOut = pnDist;
    for (i = 0; i < nImageSize; ++i)
    {
        *pnOut = ((*pnOut) * 2 + k) / (2 * k);
        pnOut++;
    }

    mdelete(pnTmpImg);
    return true;
}

bool DistanceTrans(const uchar *pucImg, const int nWidth, const int nHeight, uchar *pucDist)
{
    if (g_bExpired || NULL == pucImg || NULL == pucDist || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    const int nImgSize = nWidth * nHeight;
    int *pnDist = mnew int[nImgSize];
    if (NULL == pnDist)
    {
        return false;
    }

    bool bOk = DistanceTrans(pucImg, nWidth, nHeight, pnDist);
    if (!bOk)
    {
        mdelete(pnDist);
        return false;
    }

    int nMaxDist = 0;
    for (int i = 0; i < nImgSize; ++i)
    {
        nMaxDist = max(pnDist[i], nMaxDist);
    }
    nMaxDist = max(1, nMaxDist);
    for (int i = 0; i < nImgSize; ++i)
    {
        pucDist[i] = (uchar) (pnDist[i] * 255 / nMaxDist);
    }

    mdelete(pnDist);
    return true;
}

int DistOfRects(const int nLeft1, const int nRight1, const int nTop1, const int nBottom1,
                const int nLeft2, const int nRight2, const int nTop2, const int nBottom2)
{
    int nDist = 0;
    if (nRight2 < nLeft1)
    {
        if (nBottom2 < nTop1)
        {
            nDist = max(nTop1 - nBottom2, nLeft1 - nRight2);
        }
        else if (nTop2 > nBottom1)
        {
            nDist = max(nTop2 - nBottom1, nLeft1 - nRight2);
        }
        else
        {
            nDist = nLeft1 - nRight2;
        }
    }
    else if (nLeft2 > nRight1)
    {
        if (nBottom2 < nTop1)
        {
            nDist = max(nTop1 - nBottom2, nLeft2 - nRight1);
        }
        else if (nTop2 > nBottom1)
        {
            nDist = max(nTop2 - nBottom1, nLeft2 - nRight1);
        }
        else
        {
            nDist = nLeft2 - nRight1;
        }
    }
    else
    {
        if (nBottom2 < nTop1)
        {
            nDist = nTop1 - nBottom2;
        }
        else if (nTop2 > nBottom1)
        {
            nDist = nTop2 - nBottom1;
        }
        else
        {
            nDist = 0;
        }
    }

    return nDist;
}

int DistOfRects(const UN_RECT_S &stRect1, const UN_RECT_S &stRect2)
{
    return DistOfRects(stRect1.nLeft, stRect1.nRight, stRect1.nTop, stRect1.nBottom,
        stRect2.nLeft, stRect2.nRight, stRect2.nTop, stRect2.nBottom);
}

int DistOfRects(const Rect &stRect1, const Rect &stRect2)
{
    return DistOfRects(stRect1.x, stRect1.x + stRect1.width,
        stRect1.y, stRect1.y + stRect1.height,
        stRect2.x, stRect2.x + stRect2.width,
        stRect2.y, stRect2.y + stRect2.height);
}

// image subtract
bool SubImage(const uchar *pucImg1, const uchar *pucImg2, uchar *pucOut,
              const int nWidth, const int nHeight,
              const int nScale/* = 2*/, const int nAdd/* = 128*/)
{
    if (g_bExpired || NULL == pucImg1 || NULL == pucImg2 || NULL == pucOut
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    const int nImageSize = nWidth * nHeight;
    int nSub = 0;
    for (int i = 0; i < nImageSize; ++i)
    {
        nSub = ((int) *pucImg1++) - *pucImg2++;
        *pucOut++ = (uchar) (nSub * nScale + nAdd);
    }

    return true;
}

// image subtract, max(0, sub)
bool SubImageMax(const uchar *pucImg1, const uchar *pucImg2, uchar *pucOut,
                 const int nWidth, const int nHeight,
                 const int nScale/* = 2*/, const int nAdd/* = 128*/)
{
    if (g_bExpired || NULL == pucImg1 || NULL == pucImg2 || NULL == pucOut
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    const int nImageSize = nWidth * nHeight;
    int nSub = 0;
    for (int i = 0; i < nImageSize; ++i)
    {
        nSub = ((int) *pucImg1++) - *pucImg2++;
        *pucOut++ = (uchar) (max(0, nSub) * nScale + nAdd);
    }

    return true;
}

// image subtract, abs(sub)
bool SubImageAbs(const uchar *pucImg1, const uchar *pucImg2, uchar *pucOut,
                 const int nWidth, const int nHeight,
                 const int nScale/* = 2*/, const int nAdd/* = 128*/)
{
    if (g_bExpired || NULL == pucImg1 || NULL == pucImg2 || NULL == pucOut
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    const int nImageSize = nWidth * nHeight;
    int nSub = 0;
    for (int i = 0; i < nImageSize; ++i)
    {
        nSub = ((int) *pucImg1++) - *pucImg2++;
        *pucOut++ = (uchar) (abs(nSub) * nScale + nAdd);
    }

    return true;
}

int CalcPyramidLevel(const int nSize, const int nMinSize)
{
    int nCur = nSize;
    int nLevel = 1;
    while (nCur > nMinSize)
    {
        nLevel++;
        nCur = (nCur + 1) / 2;
    }

    if (nCur < nMinSize)
    {
        nLevel--;
    }

    return nLevel;
}

bool CreatePyramid(const uchar *pucImg, uchar *pucPyramid, UN_IMAGE_INFO_S *pstPym,
                   const int nWidth, const int nHeight, const int nLevels)
{
    if (g_bExpired || NULL == pucImg || NULL == pucPyramid || NULL == pstPym
        || 0 >= nWidth || 0 >= nHeight || 0 >= nLevels)
    {
        return false;
    }

    pstPym[0].pucBuffer = (uchar *) pucImg; //lint !e1773
    pstPym[0].nWidth = nWidth;
    pstPym[0].nHeight = nHeight;

    int nFactor = 2;
    uchar *pucTmp = pucPyramid;
    for (int i = 1; i < nLevels; ++i)
    {
        DownSample(pucImg, pucTmp, nWidth, nHeight, nWidth, nFactor);
        const int nW = nWidth / nFactor;
        const int nH = nHeight / nFactor;

        pstPym[i].pucBuffer = pucTmp;
        pstPym[i].nWidth = nW;
        pstPym[i].nHeight = nH;

        pucTmp += nW * nH;
        nFactor *= 2;
    }

    return true;
} //lint !e429

// compare data binary
bool IsTheSame(const uchar *pData1, const int nLen1,
               const uchar *pData2, const int nLen2)
{
    if (nLen1 != nLen2)
    {
        return false;
    }

    const int nLen = nLen1 / sizeof(int);
    const int *pnData1 = (const int *) pData1;
    const int *pnData2 = (const int *) pData2;
    for (int i = 0; i < nLen; ++i)
    {
        if (*pnData1 != *pnData2)
        {
            return false;
        }
        pnData1++;
        pnData2++;
    }

    const char *pcData1 = (const char *) pnData1;
    const char *pcData2 = (const char *) pnData2;
    for (unsigned int i = 0; i < nLen1 - nLen * sizeof(int); ++i)
    {
        if (*pcData1 != *pcData2)
        {
            return false;
        }
        pcData1++;
        pcData2++;
    }

    return true;
}

bool Invert(uchar *pucImg, const int nWidth, const int nHeight)
{
    if (g_bExpired || NULL == pucImg || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    for (int i = 0; i < nWidth * nHeight; ++i)
    {
        *pucImg = 255 - *pucImg;
        pucImg++;
    }

    return true;
}

int CalcThreshold(const uchar *pucImg, const int nWidth, const int nHeight,
                  const int nLeft, const int nTop, const int nRight, const int nBottom,
                  int &nThAdaptive, int *pnThBackground, int *pnThForeground)
{
    if (g_bExpired || NULL == pucImg || !IsRoiOk(nWidth, nHeight, nLeft, nTop, nRight, nBottom)
        || 0 >= nWidth || 0 >= nHeight)
    {
        return -1;
    }

    int nMinVal = 0, nMaxVal = 255, nThreshold = 0, nOld = 0;
    int nIndex = 0;
    int nLowwer = 0, nHigher = 0;
    int Hist[256] = {0};
    int nStep = 4; // sampling rate
    for (int i = 0; i < nHeight; i += nStep)
    {
        nIndex = i * nWidth;
        for (int j = 0; j < nWidth; j += nStep)
        {
            Hist[pucImg[nIndex+j]]++;
        }
    }
    for (int i = 255; i > 0; i--)
    {
        if (Hist[i] > 0)
        {
            nMaxVal = i;
            break;
        }
    }

    nThreshold = (nMinVal + nMaxVal) / 2;
    while (abs(nThreshold - nOld) > 1)
    {
        nMinVal = 0;
        nMaxVal = 0;
        nLowwer = 0;
        nHigher = 0;

        for (int i = 0; i <= nThreshold; i++)
        {
            nMinVal+= Hist[i] * i;
            nLowwer += Hist[i];
        }
        for (int i = nThreshold + 1; i <= 255; i++)
        {
            nMaxVal += Hist[i] * i;
            nHigher += Hist[i];
        }

        if (nHigher == 0)
        {
            nMaxVal = nThreshold;
        }
        else
        {
            nMaxVal /= nHigher;
        }
        if (nLowwer == 0)
        {
            nMinVal = nThreshold;
        }
        else
        {
            nMinVal /= nLowwer;
        }
        nOld = nThreshold;
        nThreshold = (nMaxVal + nMinVal) / 2;
    }

    nThAdaptive = nThreshold;
    if (NULL != pnThBackground)
    {
        *pnThBackground = nMinVal;
    }
    if (NULL != pnThForeground)
    {
        *pnThForeground = nMaxVal;
    }

    return 0;
}

int CalcThreshold(const uchar *pucImg, const int nWidth, const int nHeight,
                  int &nThAdaptive, int *pnThBackground, int *pnThForeground)
{
    return CalcThreshold(pucImg, nWidth, nHeight, 0, 0, nWidth, nHeight,
        nThAdaptive, pnThBackground, pnThForeground);
}

int CalcThreshold(const uchar *pucImg, const int nWidth, const int nHeight,
                  const UN_RECT_S &stRect, int &nThAdaptive,
                  int *pnThBackground, int *pnThForeground)
{
    return CalcThreshold(pucImg, nWidth, nHeight, stRect.nLeft, stRect.nTop,
        stRect.nRight, stRect.nBottom, nThAdaptive, pnThBackground, pnThForeground);
}

int CalcThreshold(const uchar *pucImg, const int nWidth, const int nHeight,
                  const Rect &stRect, int &nThAdaptive,
                  int *pnThBackground, int *pnThForeground)
{
    return CalcThreshold(pucImg, nWidth, nHeight, stRect.x, stRect.y,
        stRect.x + stRect.width, stRect.y + stRect.height,
        nThAdaptive, pnThBackground, pnThForeground);
}

bool SetCode(const int nCode)
{
    char *pcCode = (char *) nCode;
    if (NULL == pcCode)
    {
        return false;
    }
#ifndef COMMONEX_SPECIAL
    char acCode[32] = {0};
    int nCnt = 0;
    acCode[nCnt++] = 'A';
    acCode[nCnt++] = 'l';
    acCode[nCnt++] = 'g';
    acCode[nCnt++] = '0';
    acCode[nCnt++] = '.';
    acCode[nCnt++] = 'R';
    acCode[nCnt++] = 'D';
    acCode[nCnt++] = '@';
    acCode[nCnt++] = 'U';
    acCode[nCnt++] = 'N';
    acCode[nCnt++] = 'i';
    acCode[nCnt++] = 'C';
    acCode[nCnt++] = '#';
    acCode[nCnt++] = '3';
    acCode[nCnt++] = '3';
    acCode[nCnt++] = '1';
    acCode[nCnt++] = 'c';
    acCode[nCnt++] = '\0';
    if (g_bExpired)
    {
        // not expired if password is ok
        g_bExpired = 0 != strcmp(acCode, pcCode);
    }
#endif

    return true;
}

bool SaveBmp(const uchar *pucImg, const int nWidth, const int nHeight, const int nPitch, const char *pcFile)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucImg || NULL == pcFile || 0 >= strlen(pcFile)
        || 0 >= nWidth || 0 >= nHeight
        || (1 != nChannels && 3 != nChannels && 4 != nChannels))
    {
        return false;
    }

    FILE *pfImg = NULL;
    (void) fopen_s(&pfImg, pcFile, "wb");
    if (NULL == pfImg)
    {
        return false;
    }
    uchar aucHeader[54] = { 0 }, aucDelta[4] = {0};
    const int nDelta = (4 - (3 * nWidth) % 4) % 4;
    // save as 24 bit bitmap
    const int nBufSize = (3 * nWidth + nDelta) * nHeight;
    const int nFileSize = 54 + nBufSize;
    aucHeader[0] = 'B';
    aucHeader[1] = 'M';
    aucHeader[0x02] = nFileSize & 0xFF;
    aucHeader[0x03] = (nFileSize >> 8) & 0xFF;
    aucHeader[0x04] = (nFileSize >> 16) & 0xFF;
    aucHeader[0x05] = (nFileSize >> 24) & 0xFF;
    aucHeader[0x0A] = 0x36;
    aucHeader[0x0E] = 0x28;
    aucHeader[0x12] = nWidth & 0xFF;
    aucHeader[0x13] = (nWidth >> 8) & 0xFF;
    aucHeader[0x14] = (nWidth >> 16) & 0xFF;
    aucHeader[0x15] = (nWidth >> 24) & 0xFF;
    aucHeader[0x16] = nHeight & 0xFF;
    aucHeader[0x17] = (nHeight >> 8) & 0xFF;
    aucHeader[0x18] = (nHeight >> 16) & 0xFF;
    aucHeader[0x19] = (nHeight >> 24) & 0xFF;
    aucHeader[0x1A] = 1;
    aucHeader[0x1B] = 0;
    aucHeader[0x1C] = 24;
    aucHeader[0x1D] = 0;
    aucHeader[0x22] = nBufSize & 0xFF;
    aucHeader[0x23] = (nBufSize >> 8) & 0xFF;
    aucHeader[0x24] = (nBufSize >> 16) & 0xFF;
    aucHeader[0x25] = (nBufSize >> 24) & 0xFF;
    aucHeader[0x27] = 0x1;
    aucHeader[0x2B] = 0x1;
    fwrite(aucHeader, 1, 54, pfImg);

    if (1 == nChannels)
    {
        for (int i = 0; i < nHeight; i++)
        {
            const uchar *ptr = pucImg + (nHeight - 1 - i) * nPitch;
            for (int j = 0; j < nWidth; j++)
            {
                uchar ucValue = *(ptr++);
                fputc(ucValue, pfImg);
                fputc(ucValue, pfImg);
                fputc(ucValue, pfImg);
            }
            fwrite(aucDelta, 1, nDelta, pfImg);
        }
    }
    else
    {
        for (int i = 0; i < nHeight; i++)
        {
            const uchar *ptr = pucImg + (nHeight - 1 - i) * nPitch;
            for (int j = 0; j < nWidth; j++)
            {
                fputc(ptr[0], pfImg);
                fputc(ptr[1], pfImg);
                fputc(ptr[2], pfImg);
                ptr += nChannels;
            }
            fwrite(aucDelta, 1, nDelta, pfImg);
        }
    }

    fclose(pfImg);
    return true;
}

bool Equalize(uchar *pucImg, const int nWidth, const int nHeight, const int nPitch)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucImg || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || 0 == nWidth * nHeight || g_bExpired)
    {
        return false;
    }

    for (int k = 0; k < min(3, nChannels); ++k)
    {
        // get histogram
        const int nScale = 4;
        int anHist[256] = {0};
        for (int i = 0; i < nHeight; i += nScale)
        {
            uchar *pucRow = pucImg + i * nPitch + k;
            for (int j = 0; j < nWidth; j += nScale)
            {
                anHist[*pucRow]++;
                pucRow += nScale * nChannels;
            }
        }

        // calculate final histogram
        for (int i = 1; i < 256; ++i)
        {
            anHist[i] += anHist[i - 1];
        }
        for (int i = 0; i < 256; ++i)
        {
            anHist[i] = anHist[i] * 255 / anHist[255];
        }

        // adjust image
        for (int i = 0; i < nHeight; ++i)
        {
            uchar *pucRow = pucImg + i * nPitch + k;
            for (int j = 0; j < nWidth; ++j)
            {
                *pucRow = (uchar) anHist[*pucRow];
                pucRow += nChannels;
            }
        }
    }

    return true;
}
