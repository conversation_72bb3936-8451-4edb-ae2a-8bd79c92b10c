{"name": "tmap-parser-js", "version": "1.0.0", "description": "Node.js TMAP file parser for medical imaging", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "cli": "node src/cli.js", "test": "node src/test.js", "dev": "node --watch src/index.js"}, "bin": {"tmap-parser": "./src/cli.js"}, "keywords": ["tmap", "medical", "imaging", "parser", "pathology"], "author": "TMAP Parser Team", "license": "MIT", "dependencies": {"chalk": "^5.3.0", "commander": "^11.1.0", "ora": "^7.0.1", "pretty-bytes": "^7.0.0", "struct-compile": "^1.0.0", "table": "^6.8.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}}