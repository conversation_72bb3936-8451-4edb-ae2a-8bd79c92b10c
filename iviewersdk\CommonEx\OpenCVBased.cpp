#include <opencv2/opencv.hpp>
#include "./CommonEx.h"
#include "./Templates.h"

using namespace cv;

extern bool g_bExpired;
#if _MSC_VER < 1900
static CvSVM g_svmClassifier;
#endif

/**
* @method       RotateImage
* @access       public
* @brief        rotate image
* @param        const uchar * pucImg
* @param        uchar * pucRst
* @param        const int nWidth
* @param        const int nHeight
* @param        const int nPitch
* @param        const float fCenterX
* @param        const float fCenterY
* @param        const float fAngle
* @return       bool
* <AUTHOR> <EMAIL>
* @date         2014-03-22
* @history      initial draft
*/
bool RotateImage(const uchar *pucImg, uchar *pucRst, const int nWidth,
                 const int nHeight, const int nPitch, const float fCenterX,
                 const float fCenterY, const float fAngle)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucImg || NULL == pucRst
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || 0 == nWidth * nHeight || g_bExpired)
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC(nChannels), (void *) pucImg, nPitch);
    Mat dst(nHeight, nWidth, CV_8UC(nChannels), (void *) pucRst, nPitch);
    Mat trans = getRotationMatrix2D(Point2f(fCenterX, fCenterY), fAngle * 180 / M_PI, 1.0);

    warpAffine(src, dst, trans, Size(dst.cols, dst.rows), INTER_LINEAR);

    return true;
}

/**
* @method       RotateImage
* @access       public
* @brief        rotate image with roi
* @param        const uchar * pucImg
* @param        uchar * pucRst
* @param        const int nWidth
* @param        const int nHeight
* @param        const int nPitch
* @param        const int nLeft
* @param        const int nTop
* @param        const int nRight
* @param        const int nBottom
* @param        const float fCenterX
* @param        const float fCenterY
* @param        const float fAngle
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2015-01-14
* @history      initial draft
*/
bool RotateImage(const uchar *pucImg, uchar *pucRst,
                 const int nWidth, const int nHeight, const int nPitch,
                 const int nLeft, const int nTop, const int nRight, const int nBottom,
                 const float fCenterX, const float fCenterY, const float fAngle,
                 const bool bProcessRoiOnly/* = true*/)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucImg || NULL == pucRst
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || 0 == nWidth * nHeight || g_bExpired)
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC(nChannels), (void *) pucImg, nPitch);
    Mat dst(nHeight, nWidth, CV_8UC(nChannels), (void *) pucRst, nPitch);
    Mat trans = getRotationMatrix2D(Point2f(fCenterX, fCenterY), fAngle * 180 / M_PI, 1.0);
    Mat sub(src, Rect(nLeft, nTop, nRight - nLeft, nBottom - nTop));
    Mat roi(dst, Rect(nLeft, nTop, nRight - nLeft, nBottom - nTop));
    Mat bak;
    warpAffine(sub, bak, trans, Size(sub.cols, sub.rows), INTER_LINEAR);

    if (!bProcessRoiOnly)
    {
        src.copyTo(dst);
    }
    bak.copyTo(roi);

    return true;
}

bool Erode(const uchar *pucImg, uchar *pucRst, const int nWidth, const int nHeight,
           const int nPitch, const int nSize, const Shape shape)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucImg || NULL == pucRst
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || 0 == nWidth * nHeight || 0 >= nSize || g_bExpired)
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC(nChannels), (void *) pucImg, nPitch);
    Mat dst(nHeight, nWidth, CV_8UC(nChannels), (void *) pucRst, nPitch);

    Mat element = getStructuringElement(shape,
        Size(nSize, nSize), Point(nSize / 2, nSize / 2));
    morphologyEx(src, dst, MORPH_ERODE, element);

    return true;
}

bool Dilate(const uchar *pucImg, uchar *pucRst, const int nWidth, const int nHeight,
            const int nPitch, const int nSize, const Shape shape)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucImg || NULL == pucRst
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || 0 == nWidth * nHeight || 0 >= nSize || g_bExpired)
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC(nChannels), (void *) pucImg, nPitch);
    Mat dst(nHeight, nWidth, CV_8UC(nChannels), (void *) pucRst, nPitch);

    Mat element = getStructuringElement(shape,
        Size(nSize, nSize), Point(nSize / 2, nSize / 2));
    morphologyEx(src, dst, MORPH_DILATE, element);

    return true;
}

/**
* @method       Close
* @access       public
* @brief        close
* @param        const uchar * pucImg
* @param        uchar * pucRst
* @param        const int nWidth
* @param        const int nHeight
* @param        const int nPitch
* @param        const int nSize
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2014-05-08
* @history      initial draft
*/
bool Close(const uchar *pucImg, uchar *pucRst, const int nWidth, const int nHeight,
           const int nPitch, const int nSize, const Shape shape)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucImg || NULL == pucRst
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || 0 == nWidth * nHeight || 0 >= nSize || g_bExpired)
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC(nChannels), (void *) pucImg, nPitch);
    Mat dst(nHeight, nWidth, CV_8UC(nChannels), (void *) pucRst, nPitch);

    Mat element = getStructuringElement(shape,
        Size(nSize, nSize), Point(nSize / 2, nSize / 2));
    morphologyEx(src, dst, MORPH_CLOSE, element);

    return true;
}

/**
* @method       Open
* @access       public
* @brief        open
* @param        const uchar * pucImg
* @param        uchar * pucRst
* @param        const int nWidth
* @param        const int nHeight
* @param        const int nPitch
* @param        const int nSize
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2014-05-08
* @history      initial draft
*/
bool Open(const uchar *pucImg, uchar *pucRst, const int nWidth, const int nHeight,
          const int nPitch, const int nSize, const Shape shape)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucImg || NULL == pucRst
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || 0 == nWidth * nHeight || 0 >= nSize || g_bExpired)
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC(nChannels), (void *) pucImg, nPitch);
    Mat dst(nHeight, nWidth, CV_8UC(nChannels), (void *) pucRst, nPitch);

    Mat element = getStructuringElement(shape,
        Size(nSize, nSize), Point(nSize / 2, nSize / 2));
    morphologyEx(src, dst, MORPH_OPEN, element);

    return true;
}

bool Smooth(const uchar *pucImg, uchar *pucRst, const int nWidth,
            const int nHeight, const int nPitch, const int nSize)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucImg || NULL == pucRst
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || 0 == nWidth * nHeight || 0 >= nSize || g_bExpired)
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC(nChannels), (void *) pucImg, nPitch);
    Mat dst(nHeight, nWidth, CV_8UC(nChannels), (void *) pucRst, nPitch);

    blur(src, dst, Size(nSize, nSize));

    return true;
}

bool SmoothV(const uchar *pucImg, uchar *pucRst, const int nWidth,
             const int nHeight, const int nPitch, const int nSize)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucImg || NULL == pucRst
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || 0 == nWidth * nHeight || 0 >= nSize || g_bExpired)
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC(nChannels), (void *) pucImg, nPitch);
    Mat dst(nHeight, nWidth, CV_8UC(nChannels), (void *) pucRst, nPitch);

    blur(src, dst, Size(1, nSize));

    return true;
}

bool SmoothH(const uchar *pucImg, uchar *pucRst, const int nWidth,
             const int nHeight, const int nPitch, const int nSize)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucImg || NULL == pucRst
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || 0 == nWidth * nHeight || 0 >= nSize || g_bExpired)
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC(nChannels), (void *) pucImg, nPitch);
    Mat dst(nHeight, nWidth, CV_8UC(nChannels), (void *) pucRst, nPitch);

    blur(src, dst, Size(nSize, 1));

    return true;
}

bool Gaussian(const uchar *pucImg, uchar *pucRst, const int nWidth, const int nHeight,
              const int nPitch, const int nSize, const float fSigmaX, const float fSigmaY)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucImg || NULL == pucRst
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || 0 == nWidth * nHeight || 0 >= nSize || g_bExpired)
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC(nChannels), (void *) pucImg, nPitch);
    Mat dst(nHeight, nWidth, CV_8UC(nChannels), (void *) pucRst, nPitch);

    GaussianBlur(src, dst, Size(nSize, nSize), fSigmaX, fSigmaY);

    return true;
}

bool Median(const uchar *pucImg, uchar *pucRst, const int nWidth,
            const int nHeight, const int nPitch, const int nSize)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucImg || NULL == pucRst
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || 0 == nWidth * nHeight || 0 >= nSize || g_bExpired)
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC(nChannels), (void *) pucImg, nPitch);
    Mat dst(nHeight, nWidth, CV_8UC(nChannels), (void *) pucRst, nPitch);

    medianBlur(src, dst, nSize);

    return true;
}

bool Resize(const uchar *pucImg, uchar *pucRst, const int nWidth, const int nHeight,
            const int nPitch, const int nWidthDst, const int nHeightDst, const int nPitchDst,
            const int nType/* = RESIZE_LINEAR*/)
{
    const int nChannels = nPitch / max(1, nWidth);
    const int nChannelsDst = nPitchDst / max(1, nWidthDst);
    if (NULL == pucImg || NULL == pucRst
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || (1 != nChannelsDst && 3 != nChannelsDst && 4 != nChannelsDst)
        || nChannels != nChannelsDst
        || 0 == nWidth * nHeight
        || 0 > nType || RESIZE_ALL <= nType
        || 0 == nWidthDst * nHeightDst || g_bExpired)
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC(nChannels), (void *) pucImg, nPitch);
    Mat dst(nHeightDst, nWidthDst, CV_8UC(nChannelsDst), (void *) pucRst, nPitchDst);
    resize(src, dst, Size(nWidthDst, nHeightDst), 0.0, 0.0, nType % RESIZE_ALL);

    return true;
}

bool ConvertColor(const uchar *pucImg, uchar *pucRst, const int nWidth, const int nHeight,
                  const int nPitch, const int nWidthDst, const int nHeightDst,
                  const int nPitchDst, const Color color)
{
    const int nChannels = nPitch / max(1, nWidth);
    const int nChannelsDst = nPitchDst / max(1, nWidthDst);
    if (NULL == pucImg || NULL == pucRst
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || (1 != nChannelsDst && 3 != nChannelsDst && 4 != nChannelsDst)
        /*|| nChannels != nChannelsDst*/
        || 0 == nWidth * nHeight
        || 0 == nWidthDst * nHeightDst || g_bExpired)
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC(nChannels), (void *) pucImg, nPitch);
    Mat dst(nHeightDst, nWidthDst, CV_8UC(nChannelsDst), (void *) pucRst, nPitchDst);
    cvtColor(src, dst, (int) color);

    return true;
}

bool Watershed(const uchar *pucImg, uchar *pucRst, const int nWidth, const int nHeight)
{
    if (NULL == pucImg || NULL == pucRst
        || 0 == nWidth * nHeight || g_bExpired)
    {
        return false;
    }

    const int nImgSize = nWidth * nHeight;
    Mat src(nHeight, nWidth, CV_8UC1, (void *) pucImg, nWidth), color;
    cvtColor(src, color, COLOR_GRAY2BGR);
    Mat markers = Mat(nHeight, nWidth, CV_32SC1);
    int *pnMark = (int *) markers.data;
    int anHist[256] = {0};
    for (int i = 0; i < nImgSize; ++i)
    {
        anHist[*pucRst]++;
        *pnMark++ = *pucRst++;
    }

    // at least two non zero labels
    int nNonZero = 0;
    for (int i = 1; i < 256; ++i)
    {
        if (0 < anHist[i])
        {
            nNonZero++;
        }
    }
    if (2 > nNonZero)
    {
        return false;
    }

    pnMark -= nImgSize;
    pucRst -= nImgSize;

    watershed(color, markers);
    for (int i = 0; i < nImgSize; ++i)
    {
        uchar ucLabel = 0;
        if (-1 != *pnMark)
        {
            ucLabel = *pnMark % 256;
        }

        *pucRst++ = ucLabel;
        pnMark++;
    }

    return true;
}

int64 GetTick(void)
{
    return getTickCount();
}

float GetElapsedTime(const int64 t)
{
    int64 cur = getTickCount();
    if (cur < t)
    {
        return 0.0f;
    }

    return (float) ((cur - t) * 1E3 / getTickFrequency());
}

bool CalcOffset(const uchar *pucImgRef, const uchar *pucImgSap, const int nWidth,
                const int nHeight, const int nMaxOffsetX, const int nMaxOffsetY,
                const int nSizeUsed, int &nXOffset, int &nYOffset, float &fScore)
{
    if (NULL == pucImgRef || NULL == pucImgSap
        || nWidth < nMaxOffsetX * 2 + nSizeUsed + 2
        || nHeight < nMaxOffsetY * 2 + nSizeUsed + 2
        || 0 > nMaxOffsetX || 0 > nMaxOffsetY || 0 >= nSizeUsed || g_bExpired)
    {
        return false;
    }

    Mat ref(nHeight, nWidth, CV_8UC1, (void *) pucImgRef, nWidth);
    Mat sap(nHeight, nWidth, CV_8UC1, (void *) pucImgSap, nWidth);
    const int nX = nWidth / 2 - nSizeUsed / 2;
    const int nY = nHeight / 2 - nSizeUsed / 2;
    Mat refSub = Mat(ref, Rect(nX - nMaxOffsetX, nY - nMaxOffsetY,
        nSizeUsed + nMaxOffsetX * 2, nSizeUsed + nMaxOffsetY * 2));
    Mat sapSub = Mat(sap, Rect(nX, nY, nSizeUsed, nSizeUsed));
    Mat res;
    matchTemplate(refSub, sapSub, res, CV_TM_CCOEFF_NORMED);
    float *pfData = (float *) res.data;
    const int nIndex = FindMaxIndex(pfData, res.rows * res.cols);
    fScore = pfData[nIndex] * 100;
    nXOffset = nMaxOffsetX - nIndex % res.cols;
    nYOffset = nMaxOffsetY - nIndex / res.cols;

    return true;
}

bool MatchTemplate(const uchar *pucImgRef, const int nWidthRef, const int nHeightRef,
                   const uchar *pucImgSap, const int nWidthSap, const int nHeightSap,
                   int &nXOffset, int &nYOffset, float &fScore)
{
    if (NULL == pucImgRef || NULL == pucImgSap
        || 0 >= nWidthRef || 0 >= nHeightRef
        || 0 >= nWidthSap || 0 >= nHeightSap || g_bExpired)
    {
        return false;
    }

    Mat ref(nHeightRef, nWidthRef, CV_8UC1, (void *) pucImgRef, nWidthRef);
    Mat sap(nHeightSap, nWidthSap, CV_8UC1, (void *) pucImgSap, nWidthSap);
    Mat res;
    matchTemplate(ref, sap, res, CV_TM_CCOEFF_NORMED);
    float *pfData = (float *) res.data;
    const int nIndex = FindMaxIndex(pfData, res.rows * res.cols);
    fScore = pfData[nIndex] * 100;
    nXOffset = nIndex % res.cols;
    nYOffset = nIndex / res.cols;

    return true;
}

#if 0
// this function makes dll much larger
COMMONEX_API
bool SaveImage(const uchar *pucImg, const int nWidth, const int nHeight,
               const int nPitch, const char *pcFile);

bool SaveImage(const uchar *pucImg, const int nWidth, const int nHeight,
               const int nPitch, const char *pcFile)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucImg || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || 0 == nWidth * nHeight || g_bExpired)
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC(nChannels), (void *) pucImg, nPitch);

    return imwrite(pcFile, src);
}
#endif

bool InitJudger(const char *pcFile, const char *pcData, const char *pcLabel)
{
    if (NULL == pcFile || NULL == pcData || NULL == pcLabel || g_bExpired
        || 0 >= strlen(pcFile) || 0 >= strlen(pcData) || 0 >= strlen(pcLabel))
    {
        return false;
    }

    FileStorage fs;
    bool bOk = fs.open(pcFile, FileStorage::READ);
    if (!bOk)
    {
        return false;
    }

    Mat SVM_TrainingData;
    Mat SVM_Classes;
    fs[pcData] >> SVM_TrainingData;
    fs[pcLabel] >> SVM_Classes;
    if (SVM_TrainingData.empty() || SVM_Classes.empty())
    {
        return false;
    }

#if _MSC_VER < 1900
    //Set SVM params
    CvSVMParams SVM_params;
    SVM_params.svm_type = CvSVM::C_SVC;
    SVM_params.kernel_type = CvSVM::LINEAR; //CvSVM::LINEAR;
    SVM_params.degree = 0;
    SVM_params.gamma = 1;
    SVM_params.coef0 = 0;
    SVM_params.C = 1;
    SVM_params.nu = 0;
    SVM_params.p = 0;
    SVM_params.term_crit = cvTermCriteria(CV_TERMCRIT_ITER, 1000, 0.01);

    // Train SVM
    bOk = g_svmClassifier.train(SVM_TrainingData, SVM_Classes, Mat(), Mat(), SVM_params);
    return bOk;
#else
    return false;
#endif
}

int Judge(const uchar *pucImg, const int nWidth, const int nHeight, const int nPitch)
{
#if _MSC_VER < 1900
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucImg || 0 >= nWidth || 0 >= nHeight
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || 0 >= g_svmClassifier.get_var_count() || g_bExpired)
    {
        return -1;
    }

    Mat img(nHeight, nWidth, CV_8UC(nChannels), (void *) pucImg, nPitch), dst;
    resize(img, dst, Size(32, 32));
    dst = dst.reshape(1, 1);
    dst.convertTo(dst, CV_32FC1);
    int nResponse = (int) g_svmClassifier.predict(dst);

    return nResponse;
#else
    return -1;
#endif
}

// would cause dll loading failed in XP
// bool Equalize(uchar *pucImg, const int nWidth, const int nHeight, const int nPitch)
// {
//     const int nChannels = nPitch / max(1, nWidth);
//     if (NULL == pucImg || (1 != nChannels && 3 != nChannels && 4 != nChannels)
//         || 0 == nWidth * nHeight || g_bExpired)
//     {
//         return false;
//     }
// 
//     Mat src(nHeight, nWidth, CV_8UC(nChannels), (void *) pucImg, nPitch);
// 
//     equalizeHist(src, src);
// 
//     return true;
// }

/**
 * Perform one thinning iteration.
 * Normally you wouldn't call this function directly from your code.
 *
 * Parameters:
 * 		im    Binary image with range = [0,1]
 * 		iter  0=even, 1=odd
 */
void thinningIteration(cv::Mat& img, int iter)
{
    CV_Assert(img.channels() == 1);
    CV_Assert(img.depth() != sizeof(uchar));
    CV_Assert(img.rows > 3 && img.cols > 3);

    cv::Mat marker = cv::Mat::zeros(img.size(), CV_8UC1);

    int nRows = img.rows;
    int nCols = img.cols;

    if (img.isContinuous()) {
        nCols *= nRows;
        nRows = 1;
    }

    int x, y;
    uchar *pAbove;
    uchar *pCurr;
    uchar *pBelow;
    uchar *nw, *no, *ne;    // north (pAbove)
    uchar *we, *me, *ea;
    uchar *sw, *so, *se;    // south (pBelow)

    uchar *pDst;

    // initialize row pointers
    pAbove = NULL;
    pCurr  = img.ptr<uchar>(0);
    pBelow = img.ptr<uchar>(1);

    for (y = 1; y < img.rows-1; ++y) {
        // shift the rows up by one
        pAbove = pCurr;
        pCurr  = pBelow;
        pBelow = img.ptr<uchar>(y+1);

        pDst = marker.ptr<uchar>(y);

        // initialize col pointers
        no = pAbove;
        ne = pAbove + 1;
        me = pCurr;
        ea = pCurr + 1;
        so = pBelow;
        se = pBelow + 1;

        for (x = 1; x < img.cols-1; ++x) {
            // shift col pointers left by one (scan left to right)
            nw = no;
            no = ne;
            ne = pAbove + x + 1;
            we = me;
            me = ea;
            ea = pCurr + x + 1;
            sw = so;
            so = se;
            se = pBelow + x + 1;

            int A  = (*no == 0 && *ne == 1) + (*ne == 0 && *ea == 1) + 
                     (*ea == 0 && *se == 1) + (*se == 0 && *so == 1) + 
                     (*so == 0 && *sw == 1) + (*sw == 0 && *we == 1) +
                     (*we == 0 && *nw == 1) + (*nw == 0 && *no == 1);
            int B  = *no + *ne + *ea + *se + *so + *sw + *we + *nw;
            int m1 = iter == 0 ? (*no * *ea * *so) : (*no * *ea * *we);
            int m2 = iter == 0 ? (*ea * *so * *we) : (*no * *so * *we);

            if (A == 1 && (B >= 2 && B <= 6) && m1 == 0 && m2 == 0)
                pDst[x] = 1;
        }
    }

    img &= ~marker;
}

/**
 * Function for thinning the given binary image
 *
 * Parameters:
 * 		src  The source image, binary with range = [0,255]
 * 		dst  The destination image
 */
void thinning(cv::Mat& dst)
{
    // input is binary image (using threshold)
//     dst /= 255; // convert to binary image
// 
    cv::Mat prev = cv::Mat::zeros(dst.size(), CV_8UC1);
    cv::Mat diff;

    do {
        thinningIteration(dst, 0);
        thinningIteration(dst, 1);
        cv::absdiff(dst, prev, diff);
        dst.copyTo(prev);
    } 
    while (cv::countNonZero(diff) > 0);

    dst *= 255;
}

bool Thinning(uchar *pucImg, const int nWidth, const int nHeight,
              const uchar ucMin, const uchar ucMax,
              const int nLeft, const int nTop, const int nRight, const int nBottom)
{
    if (NULL == pucImg || 0 >= nWidth || 0 >= nHeight || g_bExpired
        || !IsRoiOk(nWidth, nHeight, nLeft, nTop, nRight, nBottom)
        || ucMin > ucMax || 255 <= ucMax - ucMin)
    {
        return false;
    }

    const int nW = nRight - nLeft;
    const int nH = nBottom - nTop;
    Mem mem;
    uchar *pucCrop = mem[nW * nH];
    Mat img(nH, nW, CV_8UC1, pucCrop, nW);
#if 0
    CopyGrayData(pucImg, pucCrop, nWidth, nHeight, nWidth, nLeft, nTop, nRight, nBottom);

    threshold(img, img, ucTh, 1, CV_THRESH_BINARY);
#else
    // slower than OpenCV
    for (int i = 0; i < nH; ++i)
    {
        uchar *pucRowCrop = pucCrop + i * nW;
        uchar *pucRowImg = pucImg + (i + nTop) * nWidth + nLeft;
        for (int j = 0; j < nW; ++j)
        {
            const uchar ucValue = *pucRowImg++;
            if (ucMin > ucValue || ucMax < ucValue)
            {
                *pucRowCrop++ = 0;
            }
            else
            {
                *pucRowCrop++ = 1;
            }
        }
    }
#endif

    thinning(img);

    for (int i = 0; i < nH; ++i)
    {
        uchar *pucThin = pucCrop + i * nW;
        uchar *pucRow = pucImg + (i + nTop) * nWidth + nLeft;
        for (int j = 0; j < nW; ++j)
        {
            *pucRow++ &= *pucThin++;
        }
    }

    return true;
}

bool Thinning(uchar *pucImg, const int nWidth, const int nHeight,
              const uchar ucMin, const uchar ucMax)
{
    return Thinning(pucImg, nWidth, nHeight, ucMin, ucMax, 0, 0, nWidth, nHeight);
}

bool Thinning(uchar *pucImg, const int nWidth, const int nHeight,
              const uchar ucMin, const uchar ucMax, const UN_RECT_S &stRect)
{
    return Thinning(pucImg, nWidth, nHeight, ucMin, ucMax,
        stRect.nLeft, stRect.nTop, stRect.nRight, stRect.nBottom);
}

bool Thinning(uchar *pucImg, const int nWidth, const int nHeight,
              const uchar ucMin, const uchar ucMax, const Rect &stRect)
{
    return Thinning(pucImg, nWidth, nHeight, ucMin, ucMax,
        stRect.x, stRect.y, stRect.x + stRect.width, stRect.y + stRect.height);
}

float ContourArea(const int *pnPoints, const int nNum)
{
    if (NULL == pnPoints || 2 >= nNum)
    {
        return 0;
    }

    vector<Point> vecContour;
    for (int i = 0; i < nNum; ++i)
    {
        vecContour.push_back(Point(pnPoints[i * 2], pnPoints[i * 2 + 1]));
    }

    return (float) contourArea(vecContour, false);
}

float ContourArea(const float *pfPoints, const int nNum)
{
    if (NULL == pfPoints || 2 >= nNum)
    {
        return 0;
    }

    vector<Point2f> vecContour;
    for (int i = 0; i < nNum; ++i)
    {
        vecContour.push_back(Point2f(pfPoints[i * 2], pfPoints[i * 2 + 1]));
    }

    return (float) contourArea(vecContour, false);
}

Mat g_lut1, g_lut3, g_lut4, g_lutChannels[4];

// initialize LUT
bool InitLut(const uchar *pucLutB, const uchar *pucLutG, const uchar *pucLutR)
{
    if (NULL == pucLutB || NULL == pucLutG || NULL == pucLutR)
    {
        return false;
    }

    if (g_lutChannels[0].empty())
    {
        g_lutChannels[0].create(1, 256, CV_8UC1);
        g_lutChannels[1].create(1, 256, CV_8UC1);
        g_lutChannels[2].create(1, 256, CV_8UC1);
        g_lutChannels[3].create(1, 256, CV_8UC1);
    }

    memcpy(g_lutChannels[0].data, pucLutB, sizeof(pucLutB[0]) * 256);
    memcpy(g_lutChannels[1].data, pucLutG, sizeof(pucLutG[0]) * 256);
    memcpy(g_lutChannels[2].data, pucLutR, sizeof(pucLutR[0]) * 256);
    for (int i = 0; i < 256; ++i)
    {
        g_lutChannels[3].data[i] = (uchar)i;
    }
    merge(g_lutChannels, 1, g_lut1);
    merge(g_lutChannels, 3, g_lut3);
    merge(g_lutChannels, 4, g_lut4);
    return true;
}

// LUT
bool Lut(const uchar *pucIn, /*uchar *pucOut, */const int nWidth, const int nHeight, const int nPitch)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucIn || /*NULL == pucOut || */0 >= nWidth || 0 >= nHeight
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || g_lut3.empty())
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC(nChannels), (void *) pucIn, nPitch);
    Mat *pLuts[4] = {&g_lut1, NULL, &g_lut3, &g_lut4};
    LUT(src, *pLuts[nChannels - 1], src);

    return true;
}

void ReleaseLut()
{
    for (int i = 0; i < 4; ++i)
    {
        g_lutChannels[i].release();
    }

    g_lut3.release();
    g_lut4.release();
}

bool Lut(const uchar *pucIn, uchar *pucOut, const int nWidth, const int nHeight, const int nPitch,
         const uchar *pucLutB, const uchar *pucLutG, const uchar *pucLutR)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucIn || NULL == pucOut || NULL == pucLutB
        || NULL == pucLutG || NULL == pucLutR
        || 0 >= nWidth || 0 >= nHeight
        || (1 != nChannels && 3 != nChannels && 4 != nChannels))
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC(nChannels), (void *) pucIn, nPitch);
    Mat dst(nHeight, nWidth, CV_8UC(nChannels), (void *) pucOut, nPitch);
    Mat lutB(1, 256, CV_8UC1, (void *) pucLutB);
    Mat lutG(1, 256, CV_8UC1, (void *) pucLutG);
    Mat lutR(1, 256, CV_8UC1, (void *) pucLutR);
    Mat lut;
    vector<Mat> luts;
    luts.push_back(lutB);
    luts.push_back(lutG);
    luts.push_back(lutR);
    merge(luts, lut);
    LUT(src, lut, dst);

    return true;
}

bool Sharpen(unsigned char *pucImg, const int nWidth, const int nHeight, const int nPitch,
             const float fFactor)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucImg || 0 >= nWidth || 0 >= nHeight
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || 0.0f >= fFactor)
    {
        return false;
    }

#if 1
    Mat img(nHeight, nWidth, CV_8UC(nChannels), pucImg, nPitch);
    Mat dst;
    blur(img, dst, Size(5, 5));
    img += (img - dst) * fFactor;
#else
    // sharpen
    float afElem[3 * 3] = {0.0f};
    afElem[0 * 3 + 0] = -1.0f;
    afElem[0 * 3 + 1] = -1.0f;
    afElem[0 * 3 + 2] = -1.0f;
    afElem[1 * 3 + 0] = -1.0f;
    afElem[1 * 3 + 1] = 9.0f + fFactor;
    afElem[1 * 3 + 2] = -1.0f;
    afElem[2 * 3 + 0] = -1.0f;
    afElem[2 * 3 + 1] = -1.0f;
    afElem[2 * 3 + 2] = -1.0f;
    Mat elem(3, 3, CV_32FC1, afElem);

    Mat img(nHeight, nWidth, CV_8UC(nChannels), pucImg, nPitch);
    filter2D(img, img, -1, elem);
#endif

    return true;
}

bool ConvexHull(const UN_POINT_S *pstPoints, bool *pbFlags, const int nNum)
{
    if (NULL == pstPoints || NULL == pbFlags || 3 > nNum)
    {
        return false;
    }

    vector<Point> points;
    for (int i = 0; i < nNum; ++i)
    {
        Point pt;
        pt.x = pstPoints[i].nX;
        pt.y = pstPoints[i].nY;
        points.push_back(pt);
    }

    vector<int> hull;
    convexHull(Mat(points), hull, true);
    memset(pbFlags, false, nNum);
    for (int i = 0; i < (int) hull.size(); ++i)
    {
        pbFlags[hull[i]] = true;
    }

    return true;
}

bool MeanStd(const uchar *pucImg, const int nWidth, const int nHeight, const int nPitch,
             float &fMean, float &fStd)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucImg || 0 >= nWidth || 0 >= nHeight
        || (1 != nChannels && 3 != nChannels && 4 != nChannels))
    {
        return false;
    }

    Mem mem;
    uchar *pucSrc = (uchar*)pucImg;
    if (1 != nChannels)
    {
        pucSrc = mem[nWidth * nHeight];
        CopyGrayData(pucImg, pucSrc, nWidth, nHeight, nPitch);
    }
    Mat img(nHeight, nWidth, CV_8UC1, pucSrc, 1 == nChannels ? nPitch : nWidth);
    Mat avg, dev;
    meanStdDev(img, avg, dev);
    fMean = float(((double*)avg.data)[0]);
    fStd = float(((double*)dev.data)[0]);

    return true;
}
