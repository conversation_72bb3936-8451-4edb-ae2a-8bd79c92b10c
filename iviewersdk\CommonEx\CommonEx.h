/**
* @date         2014-02-17
* @filename     CommonEx.h
* @purpose      commonly used functions, some based on OpeCV
* @version      3.5.9
* @history      initial draft
* <AUTHOR> UNIC, China
* @copyright    UNIC Technologies Inc, 2009-2016. All rights reserved.
*/

#ifndef __COMMON_ALGORITHM_EX_H__
#define __COMMON_ALGORITHM_EX_H__

#ifdef COMMONEX_EXPORTS
#define COMMONEX_API __declspec(dllexport)
#elif defined COMMONEX_SPECIAL
#define COMMONEX_API
#else
#define COMMONEX_API __declspec(dllimport)
#endif

#include <string.h>
#include <algorithm>
#include "./AlgoInterface.h"

namespace cv
{
    template<typename _Tp> class Rect_;
    typedef Rect_<int> Rect;
}
using cv::Rect;

// candidate traced area
typedef struct tagTracedArea
{
    int nX; // x of start tracing point
    int nY; // y of start tracing point
    int nArea; // area of traced region
    int nXGravity; // x of gravity center of traced region
    int nYGravity; // y of gravity center of traced region
    int nGray; // average gray
    int nScore; // score, not used by tracing function
    int nLabel; // label of traced region
    int nMin; // minimal gray value
    int nMax; // maximal gray value
    // rect
    int nLeft;
    int nTop;
    int nRight;
    int nBottom;
}TRACE_AREA_S;

typedef struct tagComplex
{
    double real,imag;
}COMPLEX_S;

// Directions
#ifndef DIR_E
typedef enum eDIR
{
    DIR_FOUR = 4,
    DIR_EIGHT = 8
}DIR_E;
#endif

typedef enum eLTType
{
    LT_DARK = 0,
    LT_LIGHT,
    LT_EQUAL,
    LT_NOT_EQUAL,

    LT_BUTT
}LT_TYPE_E;

// directions
const int g_anDirections[DIR_EIGHT][2] =
{{-1, 0}, {1, 0}, {0, 1}, {0, -1},      // four-neighbor
{-1, -1}, {-1, 1}, {1, -1}, {1, 1}};    // plus this four position to make eight-neighbor

// clockwise directions, FROM TOP TO BOTTOM y is INCREASING
const int g_anDirClockwise[DIR_EIGHT][2] =
{{-1, 0}, {-1, -1}, {0, -1}, {1, -1}, {1, 0}, {1, 1}, {0, 1}, {-1, 1}};

// round integer fast
COMMONEX_API
int RoundIntFast(const float f);

// quick sqrt of float
COMMONEX_API
float SqrtFloatFast(const float f);

// quick sqrt of int
COMMONEX_API
uint SqrtIntFast(uint n);

// get gray image data of source image
// pucOut should be allocated by user
// nPitch means number of bytes of a line of the image
// 1-channel, 3-channel and 4-channel images are supported
COMMONEX_API
bool CopyGrayData(const uchar *pucIn, uchar *pucOut,
                  const int nWidth, const int nHeight, const int nPitch,
                  const int nLeft, const int nTop, const int nRight, const int nBottom);

COMMONEX_API
bool CopyGrayData(const uchar *pucIn, uchar *pucOut,
                  const int nWidth, const int nHeight, const int nPitch);

COMMONEX_API
bool CopyGrayData(const uchar *pucIn, uchar *pucOut,
                  const int nWidth, const int nHeight, const int nPitch, const UN_RECT_S &stRect);

COMMONEX_API
bool CopyGrayData(const uchar *pucIn, uchar *pucOut,
                  const int nWidth, const int nHeight, const int nPitch, const Rect &stRect);

// get color image data of source image
COMMONEX_API
bool CopyColorData(const uchar *pucIn, uchar *pucOut,
                   const int nWidth, const int nHeight, const int nPitchIn, const int nPitchOut,
                   const int nLeft, const int nTop, const int nRight, const int nBottom);

COMMONEX_API
bool CopyColorData(const uchar *pucIn, uchar *pucOut,
                   const int nWidth, const int nHeight, const int nPitchIn, const int nPitchOut);

COMMONEX_API
bool CopyColorData(const uchar *pucIn, uchar *pucOut,
                   const int nWidth, const int nHeight, const int nPitchIn, const int nPitchOut,
                   const UN_RECT_S &stRect);

COMMONEX_API
bool CopyColorData(const uchar *pucIn, uchar *pucOut,
                   const int nWidth, const int nHeight, const int nPitchIn, const int nPitchOut,
                   const Rect &stRect);

// get average value by statistics
COMMONEX_API
float GetMeanByStat(const float *pfData, const int nNum,
                    const float fMinScale, const float fMaxScale);

// detect edge in a roi, edge = max(nDx, nDy);
// pucEdge should be allocated by user
// both images should have continues memory and should be of the same size
COMMONEX_API
bool DetectEdge(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                const int nLeft, const int nTop, const int nRight, const int nBottom,
                const int nTh = 8);

COMMONEX_API
bool DetectEdge(const uchar *pucImg, uchar *pucEdge, const int nWidth,
                const int nHeight, const int nTh = 8);

COMMONEX_API
bool DetectEdge(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                const UN_RECT_S &stRect, const int nTh = 8);

COMMONEX_API
bool DetectEdge(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                const Rect &stRect, const int nTh = 8);

// detect horizontal edge in a roi +128, edge = 128 + (Y(i-1) - Y(i)) / 2
// both images should have continues memory and should be of the same size
COMMONEX_API
bool DetectEdgeH(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const int nLeft, const int nTop, const int nRight, const int nBottom);

COMMONEX_API
bool DetectEdgeH(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight);

COMMONEX_API
bool DetectEdgeH(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const UN_RECT_S &stRect);

COMMONEX_API
bool DetectEdgeH(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const Rect &stRect);

// detect vertical edge in a roi +128, edge = 128 + (X(i-1) - X(i)) / 2
// both images should have continues memory and should be of the same size
COMMONEX_API
bool DetectEdgeV(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const int nLeft, const int nTop, const int nRight, const int nBottom);

COMMONEX_API
bool DetectEdgeV(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight);

COMMONEX_API
bool DetectEdgeV(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const UN_RECT_S &stRect);

COMMONEX_API
bool DetectEdgeV(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const Rect &stRect);

// detect dialog edge in a roi +128, edge = 128 + (X(i-nWidth-1) - X(i)) / 2
// both images should have continues memory and should be of the same size
COMMONEX_API
bool DetectEdgeD(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const int nLeft, const int nTop, const int nRight, const int nBottom);

COMMONEX_API
bool DetectEdgeD(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight);

COMMONEX_API
bool DetectEdgeD(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const UN_RECT_S &stRect);

COMMONEX_API
bool DetectEdgeD(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const Rect &stRect);

// detect anti-dialog edge in a roi +128, edge = 128 + (X(i-nWidth+1) - X(i)) / 2
// both images should have continues memory and should be of the same size
COMMONEX_API
bool DetectEdgeA(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const int nLeft, const int nTop, const int nRight, const int nBottom);

COMMONEX_API
bool DetectEdgeA(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight);

COMMONEX_API
bool DetectEdgeA(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const UN_RECT_S &stRect);

COMMONEX_API
bool DetectEdgeA(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                 const Rect &stRect);

// detect horizontal edge in a roi +128
// both images should have continues memory and should be of the same size
COMMONEX_API
bool DetectEdgeHC(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                  const int nLeft, const int nTop, const int nRight, const int nBottom);

COMMONEX_API
bool DetectEdgeHC(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight);

COMMONEX_API
bool DetectEdgeHC(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                  const UN_RECT_S &stRect);

COMMONEX_API
bool DetectEdgeHC(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                  const Rect &stRect);

// detect vertical edge in a roi +128
// both images should have continues memory and should be of the same size
COMMONEX_API
bool DetectEdgeVC(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                  const int nLeft, const int nTop, const int nRight, const int nBottom);

COMMONEX_API
bool DetectEdgeVC(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight);

COMMONEX_API
bool DetectEdgeVC(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                  const UN_RECT_S &stRect);

COMMONEX_API
bool DetectEdgeVC(const uchar *pucImg, uchar *pucEdge, const int nWidth, const int nHeight,
                  const Rect &stRect);

// detect edge and save magnitude and direction
// nScale means half distance between two pixels
// nMagTh is the threshold of magnitude
COMMONEX_API
bool DetectEdge(const uchar *pucIn, uchar *pucMag, uchar *pucDir,
                int nWidth, int nHeight, int nScale, int nMagTh);

// non-maximum suppression
COMMONEX_API
bool NonMaximumSuppress(const uchar *pucMag, const uchar *pucDir, uchar *pucOut,
                        int nWidth, int nHeight, const int nTh);

// down sample an image to speed up
// destination image should have the same color bits as source image
// pucOut should allocated by user, its size is: nWidth / nFactor, nHeight / nFactor
// nPitch means number of bytes of a line of the image
// 1-channel, 3-channel and 4-channel images are supported
COMMONEX_API
bool DownSample(const uchar *pucImg, uchar *pucOut, const int nWidth, const int nHeight,
                const int nPitch, const int nFactor = 4);

// down sample an image to speed up and make it gray
// pucOut should allocated by user, its size is: nWidth / nFactor, nHeight / nFactor
// nPitch means number of bytes of a line of the image
// 1-channel, 3-channel and 4-channel images are supported
COMMONEX_API
bool DownSampleGray(const uchar *pucImg, uchar *pucOut, const int nWidth, const int nHeight,
                    const int nPitch, const int nFactor = 4);

// down sample a color image to speed up, and separate each channel
// pucB, pucG and pucR should allocated by user, its size is: nWidth / nFactor, nHeight / nFactor
// nPitch means number of bytes of a line of the image
// 1-channel, 3-channel and 4-channel images are supported
COMMONEX_API
bool DownSampleSplit(const uchar *pucImg, uchar *pucB, uchar *pucG, uchar *pucR,
                     const int nWidth, const int nHeight,
                     const int nPitch, const int nFactor = 4);

// project horizontally
// pnRow should be allocated by user, its size must be no less than nHeight
// nAdjust means each value in pucImg should be plussed by
// if bInverted is true, it means pixels' value will be inverted in projection
COMMONEX_API
bool ProjectH(const uchar *pucImg, const int nWidth, const int nHeight,
              const int nLeft, const int nTop, const int nRight, const int nBottom,
              int *pnRow, const int nAdjust = 0, const bool bInverted = false);

// project horizontally
COMMONEX_API
bool ProjectH(const uchar *pucImg, const int nWidth, const int nHeight,
              int *pnRow, const int nAdjust = 0, const bool bInverted = false);

// project horizontally
COMMONEX_API
bool ProjectH(const uchar *pucImg, const int nWidth, const int nHeight,
              const UN_RECT_S &stRect,
              int *pnRow, const int nAdjust = 0, const bool bInverted = false);

// project horizontally
COMMONEX_API
bool ProjectH(const uchar *pucImg, const int nWidth, const int nHeight,
              const Rect &stRect,
              int *pnRow, const int nAdjust = 0, const bool bInverted = false);

// project vertically
// pnCol should be allocated by user, its size must be no less than nWidth
// nAdjust means each value in pucImg should be plussed by
// if bInverted is true, it means pixels' value will be inverted in projection
COMMONEX_API
bool ProjectV(const uchar *pucImg, const int nWidth, const int nHeight,
              const int nLeft, const int nTop, const int nRight, const int nBottom,
              int *pnCol, const int nAdjust = 0, const bool bInverted = false);

// project vertically
COMMONEX_API
bool ProjectV(const uchar *pucImg, const int nWidth, const int nHeight,
              int *pnCol, const int nAdjust = 0, const bool bInverted = false);

// project vertically
COMMONEX_API
bool ProjectV(const uchar *pucImg, const int nWidth, const int nHeight,
              const UN_RECT_S &stRect,
              int *pnCol, const int nAdjust = 0, const bool bInverted = false);

// project vertically
COMMONEX_API
bool ProjectV(const uchar *pucImg, const int nWidth, const int nHeight,
              const Rect &stRect,
              int *pnCol, const int nAdjust = 0, const bool bInverted = false);

// trace one region, RECOMMENDED
// pucMask should be of the same size as pucImg, and it should be initialized to zeros
// (x, y) is the start tracing point
// nMinTh and nMaxTh is the minimal and the maximal threshold of tracing pixels
// ucLabel is the index of the traced region, which is labeled in pucMask
COMMONEX_API
bool TraceRegion(const uchar *pucImg, uchar *pucMask, const int nWidth, const int nHeight,
                 const int x, const int y, const int nMinTh, const int nMaxTh,
                 const uchar ucLabel, TRACE_AREA_S &stTrace, const bool bEightNeighbor = false);

// trace one region (the function above is recommended)
// NOTE, anQueue does not have to be allocated dynamically, using array would be fine
// const int QUEUESIZE = 1024; // or even greater, like 4096
// int anQueue[QUEUESIZE][2];
// pucMask should be of the same size as pucImg, and it should be initialized to zeros
// (x, y) is the start tracing point
// nMinTh and nMaxTh is the minimal and the maximal threshold of tracing pixels
// ucLabel is the index of the traced region, which is labeled in pucMask
COMMONEX_API
bool TraceOneRegion(const uchar *pucImg, uchar *pucMask, const int nWidth, const int nHeight,
                    const int x, const int y, const int nMinTh, const int nMaxTh,
                    const uchar ucLabel, TRACE_AREA_S &stTrace, int anQueue[][2],
                    const int QUEUESIZE, const bool bEightNeighbor = false);

// trace the best region by updating threshold step by step
COMMONEX_API
bool TraceTheBestRegion(const uchar *pucImg, const uchar *pucBkg,
                        uchar *pucMask, uchar *pucTmp,
                        const int nWidth, const int nHeight,
                        const int x, const int y, const uchar ucStartTh,
                        const uchar ucMinTh, const uchar ucMaxTh, const uchar ucGlobalTh,
                        const int nMinArea, const int nMaxArea, const int nRefSize,
                        const float fMinRatio, const float fMaxRatio,
                        const uchar ucLabel, TRACE_AREA_S &stTrace,
                        int anQueue[][2], int anEdge[][2], const int QUEUESIZE,
                        const bool bEightNeighbor = false);

// trace one region with edge points
COMMONEX_API
bool TraceOneRegion(const uchar *pucImg, uchar *pucMask,
                    const int nWidth, const int nHeight,
                    const uchar ucMinTh, const uchar ucMaxTh,
                    const uchar ucLabel, TRACE_AREA_S &stTrace,
                    int pnQueue[][2], int pnEdge[][2], const int QUEUESIZE,
                    int &nEdge, int &nAddArea, int &nGraySum, const bool bEightNeighbor = false);

COMMONEX_API
bool CalcTraceAreaScore(const uchar *pucMask, const uchar *pucBkg,
                        const int nWidth, const int nHeight,
                        const float fMinRatio, const float fMaxRatio,
                        const int nRefSize, const uchar ucTh,
                        TRACE_AREA_S &stTrace);

COMMONEX_API
bool CalcTraceAreaScore(const uchar *pucMask, const uchar *pucBkg,
                        const int nWidth, const int nHeight,
                        const float fMinRatio, const float fMaxRatio,
                        const int nRefSize, const uchar ucTh,
                        const int anEdge[][2], const int nEdge,
                        TRACE_AREA_S &stTrace);

COMMONEX_API
bool IsNewTraceAreaBetter(const TRACE_AREA_S &stRef, const TRACE_AREA_S &stNew,
                          const float fMinRatio, const float fMaxRatio,
                          const int nRefArea, const int nMinScore);

// Label image border
COMMONEX_API
void LabelBorder(uchar *pucImg, const int nWidth, const int nHeight,
                 const UN_RECT_S &stRect, const uchar ucLabel);

// Label image border, like drawing a rectangle in the image
// ucLabel is the pixel value of the rectangle
// the rectangle is the whole image
COMMONEX_API
void LabelBorder(uchar *pucImg, const int nWidth, const int nHeight, const uchar ucLabel);

COMMONEX_API
void LabelBorder(uchar *pucImg, const int nWidth, const int nHeight,
                 const Rect &stRect, const uchar nLabel);

// compute angle (basic function)
COMMONEX_API
void ComputeAngle(const int nDx, const int nDy, uchar &ucAng);

// compute angle according to gray information
COMMONEX_API
void CalcGrayAngle(const uchar **pucImg, const int nWidth, const int nHeight,
                   const int x, const int y, uchar &nAngle);

// compute angle according to gray information
COMMONEX_API
void CalcGrayAngle(const uchar *pucImg, const int nWidth, const int nHeight,
                   const int x, const int y, uchar &nAngle);

// compute angle according to position
COMMONEX_API
void CalcPosAngle(const float x, const float y, const int j, const int i, uchar &nAngle);

// fill the gaps between two points
// nLength is the maximal length of pstPoints, which was allocated by user
COMMONEX_API
const int ConnectTwoPoints(const UN_POINT_S &stPoint1, const UN_POINT_S &stPoint2,
                           UN_POINT_S *pstPoints, const int nLength);

// otsu threshold
COMMONEX_API
uchar otsu(const uchar *pucImg, const int nWidth,
           const int nHeight, const UN_RECT_S &stRect);
COMMONEX_API
uchar otsu_log(const uchar *pucImg, const int nWidth,
               const int nHeight, const UN_RECT_S &stRect);
COMMONEX_API
uchar otsu_contrast(const uchar *pucImg, const int nWidth,
                    const int nHeight, const UN_RECT_S &stRect);
COMMONEX_API
uchar otsu(const uchar *pucImg, const int nWidth, const int nHeight);
COMMONEX_API
uchar otsu_log(const uchar *pucImg, const int nWidth, const int nHeight);
COMMONEX_API
uchar otsu_contrast(const uchar *pucImg, const int nWidth, const int nHeight);
COMMONEX_API
uchar otsu(const uchar *pucImg, const int nWidth,
           const int nHeight, const Rect &stRect);
COMMONEX_API
uchar otsu_log(const uchar *pucImg, const int nWidth,
               const int nHeight, const Rect &stRect);
COMMONEX_API
uchar otsu_contrast(const uchar *pucImg, const int nWidth,
                    const int nHeight, const Rect &stRect);

// fit line, fA * x + fB * y + fC = 0
COMMONEX_API
bool FitLine(const UN_POINT_S *pstPoints, const int nNum, float &fA, float &fB, float &fC);

// calculate line fitting error
COMMONEX_API
float FitErrorLine(const UN_POINT_S *pstPoints, const int nNum,
                   const float fA, const float fB, const float fC);

// fit line twice and calculate fitting error
COMMONEX_API
bool FitLineRefit(const UN_POINT_S *pstFit, const int nNum,
                  float &fA, float &fB, float &fC, float &fFitError);

// fit line, fA * x + fB * y + fC = 0
COMMONEX_API
bool FitLine(const UN_POINT_F_S *pstPoints, const int nNum, float &fA, float &fB, float &fC);

// calculate line fitting error
COMMONEX_API
float FitErrorLine(const UN_POINT_F_S *pstPoints, const int nNum,
                   const float fA, const float fB, const float fC);

// fit line twice and calculate fitting error
COMMONEX_API
bool FitLineRefit(const UN_POINT_F_S *pstFit, const int nNum,
                  float &fA, float &fB, float &fC, float &fFitError);

// fit circle
COMMONEX_API
bool FitCircle(const UN_POINT_S *pstPoints, const int nNum, float &fX, float &fY, float &fR);

// fit circle
COMMONEX_API
bool FitCircle(const UN_POINT_S* pstPoints, const uchar *pucWeight, const int nNum,
               float &fX, float &fY, float &fR);

// calculate line fitting error
COMMONEX_API
float FitErrorCircle(const UN_POINT_S *pstPoints, const int nNum,
                     const float fX, const float fY, const float fR);

// fit line twice and calculate fitting error
COMMONEX_API
bool FitCircleRefit(const UN_POINT_S *pstFit, const int nNum,
                    float &fX, float &fY, float &fR, float &fFitError);

// fitting a rectangle using contour points, fA and fB is width and height of rectangle
COMMONEX_API
bool FitRectangle(const UN_POINT_S *pstPoints, const int nLen,
                  float &fAngle, float &fA, float &fB, UN_POINT_F_S *pstRect = NULL);

// fitting a rectangle using contour points, fAngle is initialized
// nResolution: divide two degrees into how many pieces
COMMONEX_API
bool FitRectangle(const UN_POINT_S *pstPoints, const int nLen, const int nResolution,
                  float &fAngle, float &fA, float &fB, UN_POINT_F_S *pstRect = NULL);

// estimate angle of rectangle, not so accurate
COMMONEX_API
bool EstimateRectAngle(const UN_POINT_S *pstPoints, const int nLen, float &fAngle);

// check if a point is an 8-neighbor of any points in current line
COMMONEX_API
bool IsInLine(const UN_POINT_S *pstLine, const int nLen, const UN_POINT_S &stPoint);

// check if a point is an 8-neighbor of any points
COMMONEX_API
bool IsEightNeighbors(const UN_POINT_S *pstPoints, const int nLen, const UN_POINT_S &stPoint);

// initial local minimum point
typedef struct tagLocalMinimum
{
    int nX;
    int nY;
    uchar ucGray;
#ifdef __cplusplus
    bool operator <(const tagLocalMinimum &stMin) const
    {
        return ucGray < stMin.ucGray;
    }
#endif
}LOCAL_MINIMUM_S;

// detect local minima
// nStep means finding a local minimum is the area nStepXnStep
// ucTh is the threshold, all minima should be darker than ucTh
// pLocalMin should be allocated by user, and nMax indicates the length of pLocalMin
// nNumber is the number of local minima that found
COMMONEX_API
bool CalcLocalMinimum(const uchar *pImg, uchar *pSmooth,
                      const int nWidth, const int nHeight, const int nStep,
                      const uchar ucTh, LOCAL_MINIMUM_S *pstLocalMin, int &nNumber);

// detect local minima
COMMONEX_API
bool CalcLocalMinimum(const uchar *pucImg, const int nWidth, const int nHeight,
                      const int nLeft, const int nTop, const int nRight, const int nBottom,
                      const int nStep, const uchar ucTh,
                      LOCAL_MINIMUM_S *pLocalMin, const int nMax, int &nNumber);

// calculate offset, template matching
// pucImgRef and pucImgSap is teh reference image and teh sample image respectively
// (nXOffset, nYOffset) is the matching point
// nScore is the matching score, [0, 100]
// nStepX and nStepY are the down-sample factor for speedup
COMMONEX_API
bool CalcOffset(const uchar *pucImgRef, const int nWidth, const int nHeight,
                const uchar *pucImgSap, const int nWidthIn, const int nHeightIn,
                int &nXOffset, int &nYOffset, int &nScore,
                const int nStepX = 4, const int nStepY = 4);

// find a pair of weak and strong
COMMONEX_API
bool FindWeakStrong(const int *pnData, const int nLength, const int nCenter,
                    const int nMin, const int nMax, int &nStart, int &nEnd);

// find a pair of weak and strong, rectangle with sub-pixel precision
COMMONEX_API
bool FindWeakStrong(const uchar *pucH, const uchar *pucV, const int nWidth, const int nHeight,
                    const int nLeft, const int nTop, const int nRight, const int nBottom,
                    int *pnCol, int *pnRow, float &fLeft, float &fTop,
                    float &fRight, float &fBottom);

// find two strong
COMMONEX_API
bool FindTwoStrong(const int *pnData, const int nLength, const int nCenter,
                   const int nMin, const int nMax, int &nStart, int &nEnd);

// find two weak
COMMONEX_API
bool FindStrongWeak(const int *pnData, const int nLength, const int nCenter,
                    const int nMin, const int nMax, int &nStart, int &nEnd);

// convert rgb to hsl from CxImage
// ucR: R -> H
// ucG: G -> S
// ucB: B -> L
COMMONEX_API
void RGB2HSL(uchar &ucR, uchar &ucG, uchar &ucB);

// convert rgb to hsl from CxImage
// pucImg should be color image
// nPitch means number of bytes of a line of the image
// pucHue, pucSat and pucLam should be allocated by user
COMMONEX_API
bool RGB2HSL(const uchar *pucImg, const int nWidth, const int nHeight,
             const int nPitch, uchar *pucH, uchar *pucS, uchar *pucL);

// convert rgb to YUV
COMMONEX_API
void RGB2Yuv(uchar &ucR, uchar &ucG, uchar &ucB);

// convert rgb to YUV
COMMONEX_API
bool RGB2Yuv(const uchar *pucImg, const int nWidth, const int nHeight,
             const int nPitch, uchar *pucY, uchar *pucU, uchar *pucV);

COMMONEX_API
float Hue2RGB(float n1, float n2, float hue);

// convert hsl to rgb from CxImage
// ucH: H -> R
// ucS: S -> G
// ucL: L -> B
COMMONEX_API
void HSL2RGB(uchar &ucH, uchar &ucS, uchar &ucL);

// convert hsl to rgb from CxImage
// pucImg should be color image and it should be allocated by user
// nPitch means number of bytes of a line of the image
COMMONEX_API
bool HSL2RGB(const uchar *pucHue, const uchar *pucSat, const uchar *pucLam,
             uchar *pucImg, const int nWidth, const int nHeight, const int nPitch);

// convert rgb to lab
COMMONEX_API
void RGB2Lab(uchar &ucR, uchar &ucG, uchar &ucB);

COMMONEX_API
bool RGB2Lab(const uchar *pucImg, const int nWidth, const int nHeight,
             const int nPitch, uchar *pucL, uchar *puca, uchar *pucb);

// split rgb channels
// pucImg should be color image
// nPitch means number of bytes of a line of the image
// pucB, pucG and pucR should be allocated by user
COMMONEX_API
bool SplitRGB(const uchar *pucImg, const int nWidth, const int nHeight,
              const int nPitch, uchar *pucB, uchar *pucG, uchar *pucR);

// merge rgb channels
// pucImg should be color image and it should be allocated by user
// nPitch means number of bytes of a line of the image
COMMONEX_API
bool MergeRGB(uchar *pucImg, const int nWidth, const int nHeight, const int nPitch,
              const uchar *pucB, const uchar *pucG, const uchar *pucR);

// extract single channel (0 ~ 3)
// pucChannel should be allocated by user
// its size should be no less than (nWidthXnHeight)
// pucImg can be of 1-channel, 3-channel or 4-channel
// 1-channel: nChannel should be 0 only
// 3-channel: nChannel belongs to [0, 2]
// 4-channel: nChannel belongs to [0, 3]
COMMONEX_API
bool ExtractChannel(const uchar *pucImg, uchar *pucChannel, const int nWidth,
                    const int nHeight, const int nPitch, const int nChannel);

COMMONEX_API
bool ExtractChannel(const uchar *pucImg, uchar *pucChannel, const int nWidth,
                    const int nHeight, const int nPitch, const int nChannel, const UN_RECT_S &stRect);

COMMONEX_API
bool ExtractChannel(const uchar *pucImg, uchar *pucChannel, const int nWidth,
                    const int nHeight, const int nPitch, const int nChannel,
                    const int nLeft, const int nTop, const int nRight, const int nBottom);

// extract single channel (0 ~ 3)
// pucChannel should be allocated by user
// its size should be no less than (nWidth/nScale, nHeight/nScale)
// pucImg can be of 1-channel, 3-channel or 4-channel
// 1-channel: nChannel should be 0 only
// 3-channel: nChannel belongs to [0, 2]
// 4-channel: nChannel belongs to [0, 3]
// nScale means the down-sample factor
COMMONEX_API
bool ExtractChannel(const uchar *pucImg, uchar *pucChannel, const int nWidth,
                    const int nHeight, const int nPitch, const int nChannel, const int nScale);

// check roi, return false if there is any error
COMMONEX_API
bool IsRoiOk(const int nWidth, const int nHeight, const int nLeft,
             const int nTop, const int nRight, const int nBottom);

// check roi, return false if there is any error
COMMONEX_API
bool IsRoiOk(const int nWidth, const int nHeight, const UN_RECT_S &stRect);

COMMONEX_API
bool IsRoiOk(const int nWidth, const int nHeight, const Rect &stRect);

// trace contour
// 0 <= nMinTh <= nMaxTh <= 255, 255 > nMaxTh - nMinTh, nX and nY are NOT used
COMMONEX_API
bool TraceContour(const uchar *pucImg, const int nWidth, const int nHeight,
                  const int nX, const int nY, const int nLeft, const int nTop,
                  const int nRight, const int nBottom, const int nMinTh, const int nMaxTh,
                  UN_POINT_S *pstContour, const int nMaxContour, int &nCount);

// trace contour
// 0 <= nMinTh <= nMaxTh <= 255, 255 > nMaxTh - nMinTh, nX and nY are NOT used
COMMONEX_API
bool TraceContour(const uchar *pucImg, const int nWidth, const int nHeight,
                  const int nX, const int nY, const UN_RECT_S &stRect,
                  const int nMinTh, const int nMaxTh,
                  UN_POINT_S *pstContour, const int nMaxContour, int &nCount);

// trace contour
// 0 <= nMinTh <= nMaxTh <= 255, 255 > nMaxTh - nMinTh, nX and nY are NOT used
COMMONEX_API
bool TraceContour(const uchar *pucImg, const int nWidth, const int nHeight,
                  const int nX, const int nY, const Rect &stRect,
                  const int nMinTh, const int nMaxTh,
                  UN_POINT_S *pstContour, const int nMaxContour, int &nCount);

// get main color, supports color image only
// pucMsk could be NULL, if it's not NULL, 
// the pixels with corresponding non-zero pucMsk will be processed
COMMONEX_API
bool GetMainColor(const uchar *pucImg, const uchar *pucMsk,
                  const int nWidth, const int nHeight,
                  const int nPitch, const UN_RECT_S &stRect,
                  uchar &ucR, uchar &ucG, uchar &ucB);

COMMONEX_API
bool GetMainColor(const uchar *pucImg, const uchar *pucMsk,
                  const int nWidth, const int nHeight,
                  const int nPitch, const Rect &stRect,
                  uchar &ucR, uchar &ucG, uchar &ucB);

COMMONEX_API
bool GetMainColor(const uchar *pucImg, const uchar *pucMsk,
                  const int nWidth, const int nHeight,
                  const int nPitch, uchar &ucR, uchar &ucG, uchar &ucB);

// check if point is in a convex polygon, points have to be stored clockwise or anti-clockwise
COMMONEX_API
bool IsPointInConvex(const UN_POINT_S *pstPoints, const int nPoints,
                     const UN_POINT_S &stPoint);

// check if point is strictly in a convex polygon, points have to be stored clockwise or anti-clockwise
COMMONEX_API
bool IsPointInConvexStrict(const UN_POINT_S *pstPoints,
                           const int nPoints, const UN_POINT_S &stPoint);

// clockwise
COMMONEX_API
UN_POINT_S RotatePoint(const UN_POINT_S &stPoint, const UN_POINT_S &stCenter,
                       const float fSin, const float fCos);

// rotate stPoint in clockwise, and take stCenter as rotation center
// fAngle is the rotation angle (in radian)
COMMONEX_API
UN_POINT_S RotatePoint(const UN_POINT_S &stPoint, const UN_POINT_S &stCenter,
                       const float fAngle);

// rotate stPoint in clockwise, and take stCenter as rotation center
// fSin and fCos is sin(angle) and cos(angle) respectively
COMMONEX_API
UN_POINT_F_S RotatePoint(const UN_POINT_F_S &stPoint, const UN_POINT_F_S &stCenter,
                         const float fSin, const float fCos);

// rotate stPoint in clockwise, and take stCenter as rotation center
// fAngle is the rotation angle (in radian)
COMMONEX_API
UN_POINT_F_S RotatePoint(const UN_POINT_F_S &stPoint, const UN_POINT_F_S &stCenter,
                         const float fAngle);

// rotate image, angle should be radian, anti-clockwise
// pucOut should be allocated by user
// (nCenterX, nCenterY) is the rotation center
// fAngle is the rotation angle (in radian)
COMMONEX_API
bool RotateImage(const uchar *pucIn, uchar *pucOut,
                 const int nWidth, const int nHeight, const int nPitch,
                 const int nCenterX, const int nCenterY, const float fAngle);

typedef enum eAngle
{
    e0,
    e90,
    e180,
    e270,
    eAll
}eAngle;

// pucIn and pucOut can NOT be the same
COMMONEX_API
bool RotateImage(const uchar *pucIn, uchar *pucOut, const int nWidth, const int nHeight,
                 const int nPitch, const int nPitchDst, const eAngle eAgl);

// pucIn and pucOut can NOT be the same
COMMONEX_API
bool FlipV(const uchar *pucIn, uchar *pucOut, const int nWidth, const int nHeight,
           const int nPitch, const int nPitchDst);

// support gray image only
COMMONEX_API
bool FlipV(uchar *pucImg, const int nWidth, const int nHeight);

// pucIn and pucOut can NOT be the same
COMMONEX_API
bool FlipH(const uchar *pucIn, uchar *pucOut, const int nWidth, const int nHeight,
           const int nPitch, const int nPitchDst);

// support gray image only
COMMONEX_API
bool FlipH(uchar *pucImg, const int nWidth, const int nHeight);

// nPercent [0, 100], 0 or 100 means totally automatically
COMMONEX_API
bool AutoLevel(uchar *pucImg, const int nWidth, const int nHeight,
               const int nPitch, const int nPercent);

// equalize histogram
COMMONEX_API
bool Equalize(uchar *pucImg, const int nWidth, const int nHeight, const int nPitch);

// adjust contrast, use this function if pucImg is gray
// nPercent [1, 99]
// anHist could be null otherwise the histogram will be copied
COMMONEX_API
bool AdjustContrast(uchar *pucImg, const int nWidth, const int nHeight,
                    const int nPercent, int anHist[256] = NULL);

// adjust contrast
// nPercent [1, 99]
COMMONEX_API
bool AdjustContrast(uchar *pucImg, const int nWidth, const int nHeight,
                    const int nPitch, const int nPercent);

// nSatDelta [-100, 100]
COMMONEX_API
bool AdjustSaturation(uchar *pucImg, const int nWidth, const int nHeight,
                      const int nPitch, const int nSatDelta);

// calculate angle of region labeled as ucLabel
// positive: anti-clockwise, negative: clockwise
// angle is not robust when w and h are almost the same
COMMONEX_API
bool CalcRegionAngle(const uchar *pucMsk, const int nWidth, const int nHeight,
                     const uchar ucLabel, const UN_RECT_S &stRect, float &fAngle,
                     float *pRa = NULL, float *pRb = NULL);

// calculate angle of all pixels whose gray level is between ucMin and ucMax
// positive: anti-clockwise, negative: clockwise
// angle is not robust when w and h are almost the same
COMMONEX_API
bool CalcRegionAngle(const uchar *pucImg, const int nWidth, const int nHeight,
                     const uchar ucMin, const uchar ucMax,
                     const UN_RECT_S &stRect, float &fAngle,
                     float *pRa = NULL, float *pRb = NULL);

// calculate angle, ra and rb of contour points
// angle is not robust when w and h are almost the same
COMMONEX_API
bool CalcContourAngle(const UN_POINT_S *pstPoints, const int nPoints,
                      float &fAngle, float *pRa = NULL, float *pRb = NULL);

// calculate elliptic axis parameters (angle, ra, rb)
COMMONEX_API
void EllipticAxis(const float fM11, const float fM02, const float fM20,
                  float &fAngle, float &fRa, float &fRb);

// adjust angle for rotation, [-45, 45]
COMMONEX_API
void AdjustAngle(float &fAngle);

// local threshold, enhance dark area if eType is LT_DARK
COMMONEX_API
bool LocalThreshold(const uchar *pucImg, uchar *pucBin, const int nWidth,
                    const int nHeight, const int nSize = 49,
                    const int nTh = 5, const LT_TYPE_E eType = LT_DARK);

// local threshold, enhance dark area if eType is LT_DARK
COMMONEX_API
bool LocalThresholdH(const uchar *pucImg, uchar *pucBin, const int nWidth,
                     const int nHeight, const int nSize = 49,
                     const int nTh = 5, const LT_TYPE_E eType = LT_DARK);

// local threshold, enhance dark area if eType is LT_DARK
COMMONEX_API
bool LocalThresholdV(const uchar *pucImg, uchar *pucBin, const int nWidth,
                     const int nHeight, const int nSize = 49,
                     const int nTh = 5, const LT_TYPE_E eType = LT_DARK);

// return roundness score of some points, [0, 100]
// nLen is the number of the points in pstContour
COMMONEX_API
int Roundness(const UN_POINT_S *pstContour, const int nLen);

// fast adaptive non-minimum suppression algorithm
// pstMinima: array of local minima and nMinima is the number of them
// nWidth and nHeight is the image size
// nWantNum means how many local minima does the user want
COMMONEX_API
bool AdaptiveNonMinSuppress(LOCAL_MINIMUM_S *pstMinima, const int nMinima,
                            const int nWidth, const int nHeight, const int nWantNum);

// fast adaptive non-minimum suppression algorithm
COMMONEX_API
bool AdaptiveNonMinSuppressEx(LOCAL_MINIMUM_S *pstMinima, const int nMinima,
                              const int nWidth, const int nHeight, const int nWantNum);

// fast adaptive non-minimum suppression with a distance threshold
// local minima would have a minimal distance as nDistTh indicates
// pstMinima: array of local minima and nMinima is the number of them
// nWidth and nHeight is the image size
// nWantNum means how many local minima does the user want
COMMONEX_API
bool AdaptiveNonMinSuppressTh(LOCAL_MINIMUM_S *pstMinima, const int nMinima,
                              const int nWidth, const int nHeight,
                              const int nDistTh, int &nWantNum);

// distance transform
COMMONEX_API
bool DistanceTrans(const uchar *pucImg, const int nWidth, const int nHeight, int *pnDist);

// distance transform, distances were scaled to [0, 255]
COMMONEX_API
bool DistanceTrans(const uchar *pucImg, const int nWidth, const int nHeight, uchar *pucDist);

COMMONEX_API
int DistOfRects(const int nLeft1, const int nRight1, const int nTop1, const int nBottom1,
                const int nLeft2, const int nRight2, const int nTop2, const int nBottom2);

COMMONEX_API
int DistOfRects(const UN_RECT_S &stRect1, const UN_RECT_S &stRect2);

COMMONEX_API
int DistOfRects(const Rect &stRect1, const Rect &stRect2);

// image subtract
// pucOut = (pucImg1 - pucImg2) * nScale + nAdd
COMMONEX_API
bool SubImage(const uchar *pucImg1, const uchar *pucImg2, uchar *pucOut,
              const int nWidth, const int nHeight, const int nScale = 2, const int nAdd = 128);

// image subtract, max(0, sub)
// pucOut = max(0, pucImg1 - pucImg2) * nScale + nAdd
COMMONEX_API
bool SubImageMax(const uchar *pucImg1, const uchar *pucImg2, uchar *pucOut,
                 const int nWidth, const int nHeight,
                 const int nScale = 2, const int nAdd = 128);

// image subtract, abs(sub)
// pucOut = abs(pucImg1 - pucImg2) * nScale + nAdd
COMMONEX_API
bool SubImageAbs(const uchar *pucImg1, const uchar *pucImg2, uchar *pucOut,
                 const int nWidth, const int nHeight,
                 const int nScale = 2, const int nAdd = 128);

// calculate levels of pyramid
COMMONEX_API
int CalcPyramidLevel(const int nSize, const int nMinSize);

// create pyramid, sub images will be stored in pucPyramid, [1/4, 1/16, 1/64, ...]
COMMONEX_API
bool CreatePyramid(const uchar *pucImg, uchar *pucPyramid, UN_IMAGE_INFO_S *pstPym,
                   const int nWidth, const int nHeight, const int nLevels);

// compare data binary
COMMONEX_API
bool IsTheSame(const uchar *pData1, const int nLen1,
               const uchar *pData2, const int nLen2);

// invert image
COMMONEX_API
bool Invert(uchar *pucImg, const int nWidth, const int nHeight);

// calculate threshold of an image (with a ROI)
// nThAdaptive: adaptive threshold
// *pnThBackground: background threshold
// *pnThForeground: foreground threshold
COMMONEX_API
int CalcThreshold(const uchar *pucImg, const int nWidth, const int nHeight,
                  const int nLeft, const int nTop, const int nRight, const int nBottom,
                  int &nThAdaptive, int *pnThBackground = NULL, int *pnThForeground = NULL);

COMMONEX_API
int CalcThreshold(const uchar *pucImg, const int nWidth, const int nHeight,
                  int &nThAdaptive, int *pnThBackground = NULL, int *pnThForeground = NULL);

COMMONEX_API
int CalcThreshold(const uchar *pucImg, const int nWidth, const int nHeight,
                  const UN_RECT_S &stRect, int &nThAdaptive,
                  int *pnThBackground = NULL, int *pnThForeground = NULL);

COMMONEX_API
int CalcThreshold(const uchar *pucImg, const int nWidth, const int nHeight,
                  const Rect &stRect, int &nThAdaptive,
                  int *pnThBackground = NULL, int *pnThForeground = NULL);

COMMONEX_API
bool SetCode(const int nCode);

// fAngle should be in radian
COMMONEX_API
bool RotateImage(const uchar *pucImgIn, uchar *pucImgOut,
                 const int nWidth, const int nHeight, const int nPitch,
                 const float fCenterX, const float fCenterY, const float fAngle);

// fAngle should be in radian
COMMONEX_API
bool RotateImage(const uchar *pucImgIn, uchar *pucImgOut,
                 const int nWidth, const int nHeight, const int nPitch,
                 const int nLeft, const int nTop, const int nRight, const int nBottom,
                 const float fCenterX, const float fCenterY, const float fAngle,
                 const bool bProcessRoiOnly = true);

enum Shape
{
    SHAPE_RECT = 0,
    SHAPE_CROSS,
    SHAPE_ELLIPSE
};

// nSize is diameter
COMMONEX_API
bool Erode(const uchar *pucImg, uchar *pucRst, const int nWidth, const int nHeight,
           const int nPitch, const int nSize, const Shape shape = SHAPE_RECT);

// nSize is diameter
COMMONEX_API
bool Dilate(const uchar *pucImg, uchar *pucRst, const int nWidth, const int nHeight,
            const int nPitch, const int nSize, const Shape shape = SHAPE_RECT);

// nSize is diameter
COMMONEX_API
bool Close(const uchar *pucImg, uchar *pucRst, const int nWidth, const int nHeight,
           const int nPitch, const int nSize, const Shape shape = SHAPE_RECT);

// nSize is diameter
COMMONEX_API
bool Open(const uchar *pucImg, uchar *pucRst, const int nWidth, const int nHeight,
          const int nPitch, const int nSize, const Shape shape = SHAPE_RECT);

// nSize is diameter
COMMONEX_API
bool Smooth(const uchar *pucImg, uchar *pucRst, const int nWidth,
            const int nHeight, const int nPitch, const int nSize);

// nSize is diameter
COMMONEX_API
bool SmoothV(const uchar *pucImg, uchar *pucRst, const int nWidth,
             const int nHeight, const int nPitch, const int nSize);

// nSize is diameter
COMMONEX_API
bool SmoothH(const uchar *pucImg, uchar *pucRst, const int nWidth,
             const int nHeight, const int nPitch, const int nSize);

// nSize is diameter
COMMONEX_API
bool Gaussian(const uchar *pucImg, uchar *pucRst, const int nWidth,
              const int nHeight, const int nPitch, const int nSize,
              const float fSigmaX, const float fSigmaY);

// nSize is diameter
COMMONEX_API
bool Median(const uchar *pucImg, uchar *pucRst, const int nWidth,
            const int nHeight, const int nPitch, const int nSize);

enum
{
    RESIZE_NN = 0, // nearest neighbor
    RESIZE_LINEAR, // bilinear
    RESIZE_CUBIC, // bicubic
    RESIZE_AREA, // area
    RESIZE_LANCZOS4, // lanczos4

    RESIZE_ALL
};

COMMONEX_API
bool Resize(const uchar *pucImg, uchar *pucRst, const int nWidth, const int nHeight,
            const int nPitch, const int nWidthDst, const int nHeightDst, const int nPitchDst,
            const int nType = RESIZE_LINEAR);

// do NOT modify the following values
enum Color
{
    BGR2RGB = 4,
    RGB2BGR = BGR2RGB,

    BGR2GRAY = 6,
    RGB2GRAY = 7,
    GRAY2BGR = 8,
    GRAY2RGB = GRAY2BGR,
    GRAY2BGRA = 9,
    GRAY2RGBA = GRAY2BGRA,
    BGRA2GRAY = 10,
    RGBA2GRAY = 11,

    BGR2XYZ = 32,
    RGB2XYZ = 33,
    XYZ2BGR = 34,
    XYZ2RGB = 35,

    BGR2YCrCb = 36,
    RGB2YCrCb = 37,
    YCrCb2BGR = 38,
    YCrCb2RGB = 39,

    BGR2HSV = 40,
    RGB2HSV = 41,

    BGR2LAB = 44,
    RGB2LAB = 45,

    BayerBG2BGR = 46,
    BayerGB2BGR = 47,
    BayerRG2BGR = 48,
    BayerGR2BGR = 49,

    BayerBG2RGB = BayerRG2BGR,
    BayerGB2RGB = BayerGR2BGR,
    BayerRG2RGB = BayerBG2BGR,
    BayerGR2RGB = BayerGB2BGR,

    BGR2Luv = 50,
    RGB2Luv = 51,
    BGR2HLS = 52,
    RGB2HLS = 53,

    HSV2BGR = 54,
    HSV2RGB = 55,

    Lab2BGR = 56,
    Lab2RGB = 57,
    Luv2BGR = 58,
    Luv2RGB = 59,
    HLS2BGR = 60,
    HLS2RGB = 61,

    BGR2HSV_FULL = 66,
    RGB2HSV_FULL = 67,
    BGR2HLS_FULL = 68,
    RGB2HLS_FULL = 69,

    HSV2BGR_FULL = 70,
    HSV2RGB_FULL = 71,
    HLS2BGR_FULL = 72,
    HLS2RGB_FULL = 73,

    BGR2YUV = 82,
    RGB2YUV = 83,
    YUV2BGR = 84,
    YUV2RGB = 85,

    BayerBG2GRAY = 86,
    BayerGB2GRAY = 87,
    BayerRG2GRAY = 88,
    BayerGR2GRAY = 89
};

COMMONEX_API
bool ConvertColor(const uchar *pucImg, uchar *pucRst, const int nWidth, const int nHeight,
                  const int nPitch, const int nWidthDst, const int nHeightDst,
                  const int nPitchDst, const Color color);

// at least two non zero labels in pucSeed
COMMONEX_API
bool Watershed(const uchar *pucImg, uchar *pucSeed, const int nWidth, const int nHeight);

COMMONEX_API
int64 GetTick(void);

// ms, GetTick should be evoked first
COMMONEX_API
float GetElapsedTime(const int64 t);

// reference image and sample image should be of the same size
COMMONEX_API
bool CalcOffset(const uchar *pucImgRef, const uchar *pucImgSap, const int nWidth,
                const int nHeight, const int nMaxOffsetX, const int nMaxOffsetY,
                const int nSizeUsed, int &nXOffset, int &nYOffset, float &fScore);

// match template
COMMONEX_API
bool MatchTemplate(const uchar *pucImgRef, const int nWidthRef, const int nHeightRef,
                   const uchar *pucImgSap, const int nWidthSap, const int nHeightSap,
                   int &nXOffset, int &nYOffset, float &fScore);

// thinning, pixels < ucMin or > ucMax will be zeros
COMMONEX_API
bool Thinning(uchar *pucImg, const int nWidth, const int nHeight,
              const uchar ucMin, const uchar ucMax);

// thinning, pixels < ucMin or > ucMax will be zeros
COMMONEX_API
bool Thinning(uchar *pucImg, const int nWidth, const int nHeight,
              const uchar ucMin, const uchar ucMax,
              const int nLeft, const int nTop, const int nRight, const int nBottom);

// thinning, pixels < ucMin or > ucMax will be zeros
COMMONEX_API
bool Thinning(uchar *pucImg, const int nWidth, const int nHeight,
              const uchar ucMin, const uchar ucMax, const UN_RECT_S &stRect);

// thinning, pixels < ucMin or > ucMax will be zeros
COMMONEX_API
bool Thinning(uchar *pucImg, const int nWidth, const int nHeight,
              const uchar ucMin, const uchar ucMax, const Rect &stRect);

// save image (support bmp only)
// image will be save as 24 bit bitmap no matter the input image is gray or not
COMMONEX_API
bool SaveBmp(const uchar *pucImg, const int nWidth, const int nHeight,
             const int nPitch, const char *pcFile);

// initial judger
COMMONEX_API
bool InitJudger(const char *pcFile, const char *pcData, const char *pcLabel);

// judge image data
COMMONEX_API
int Judge(const uchar *pucImg, const int nWidth, const int nHeight, const int nPitch);

// calculate optimal size for FFT
COMMONEX_API
bool FFTSize(const int nWidth, const int nHeight, int &nFFTWidth, int &nFFTHeight);

// FFT for image (with padding if size of not of power of 2)
// nFFTWidth and nFFTHeight should be calculated using FFTSize
// pstFFT should be allocated by user its size is nFFTWidth * nFFTHeight
COMMONEX_API
bool FFT2D(const uchar *pucImg, const int nWidth, const int nHeight,
           COMPLEX_S *pstFFT, const int nFFTWidth, const int nFFTHeight);

// FFT for image (with padding if size of not of power of 2)
// nFFTWidth and nFFTHeight should be calculated using FFTSize
// pstFFT should be allocated by user its size is nFFTWidth * nFFTHeight
COMMONEX_API
bool FFT2D(const double *pdImg, const int nWidth, const int nHeight,
           COMPLEX_S *pstFFT, const int nFFTWidth, const int nFFTHeight);

// inverse FFT for image
// if FFT convolve with another, image will have offset(-1, -1) when transformed back
// bNormailze means normalize FFT data or not
// nFFTWidth and nFFTHeight should be calculated using FFTSize
// pstFFT should be allocated by user its size is nFFTWidth * nFFTHeight
COMMONEX_API
bool FFT2D(COMPLEX_S *pstFFT, const int nFFTWidth, const int nFFTHeight,
           uchar *pucImg, const int nWidth, const int nHeight,
           const bool bNormailze = false);

// FFT for image with any size
// pstFFT should be allocated by user its size is nWidth * nHeight
COMMONEX_API
bool FFT2DCV(const uchar *pucImg, const int nWidth, const int nHeight, COMPLEX_S *pstFFT);

// FFT for image with any size
// pstFFT should be allocated by user its size is nWidth * nHeight
COMMONEX_API
bool FFT2DCV(const double *pdImg, const int nWidth, const int nHeight,
             COMPLEX_S *pstFFT, const int nFFTWidth, const int nFFTHeight);

// inverse FFT for image with any size, bNormailze means normalize FFT data or not
// pucImg should be allocated by user its size is nWidth * nHeight
COMMONEX_API
bool FFT2DCV(const COMPLEX_S *pstFFT, const int nWidth, const int nHeight,
             uchar *pucImg, const bool bNormailze);

// convert FFT image to normal image for displaying
// 0 <= nFactor, pucTmp should be of the same size as pstFFT
COMMONEX_API
bool FFT2Display(const COMPLEX_S *pstFFT, const int nFFTWidth, const int nFFTHeight,
                 uchar *pucTmp, const int nFactor = 16);

// convolve in frequency domain
COMMONEX_API
bool Conv(const COMPLEX_S *pstFFT1, const COMPLEX_S *pstFFT2,
          const int nWidth, const int nHeight, COMPLEX_S *pstResult);

// multiply FFT image with double image (usually it's Gaussian filter)
COMMONEX_API
bool Conv(const COMPLEX_S *pstFFT1, const double *pdFilter,
          const int nWidth, const int nHeight, COMPLEX_S *pstResult);

// correlation in frequency domain
COMMONEX_API
bool Corr(const COMPLEX_S *pstFFT1, const COMPLEX_S *pstFFT2,
          const int nWidth, const int nHeight, COMPLEX_S *pstResult);

// create Gaussian filter in frequency domain (shifted)
COMMONEX_API
bool CreateGaussian(double *pdGauss, const int nWidth, const int nHeight,
                    const double dSigmaX, const double dSigmaY);

// create ideal band-pass filter in frequency domain (shifted)
// 0 < dLowFreq < 1.0, 0 < dHighFreq < 1.0, dLowFreq < dHighFreq
COMMONEX_API
bool CreateBandpassIdeal(double *pdBandpass, const int nWidth, const int nHeight,
                         const double dLowFreq, const double dHighFreq);

// create Gaussian band-pass filter in frequency domain (shifted)
// 0 < dLowFreq < 1.0, 0 < dHighFreq < 1.0, dLowFreq < dHighFreq
COMMONEX_API
bool CreateBandPassStd(double *pdBandStd, const int nWidth, const int nHeight,
                       const double dLowFreq, const double dHighFreq);

// calculate angle of image using FFT
COMMONEX_API
bool FFTCalcAngle(const uchar *pucImg, const int nWidth, const int nHeight, float &fAngle);

// calculate area in a contour, *pnPoints {x, y, x, y, ...}
COMMONEX_API
float ContourArea(const int *pnPoints, const int nNum);

// calculate area in a contour, *pfPoints {x, y, x, y, ...}
COMMONEX_API
float ContourArea(const float *pfPoints, const int nNum);

// initialize LUT
COMMONEX_API
bool InitLut(const uchar *pucLutB, const uchar *pucLutG, const uchar *pucLutR);

// LUT
COMMONEX_API
bool Lut(const uchar *pucIn, const int nWidth, const int nHeight, const int nPitch);

// release LUT
COMMONEX_API
void ReleaseLut();

// LUT
COMMONEX_API
bool Lut(const uchar *pucIn, uchar *pucOut, const int nWidth, const int nHeight, const int nPitch,
         const uchar *pucLutB, const uchar *pucLutG, const uchar *pucLutR);

// Sharpen
COMMONEX_API
bool Sharpen(uchar *pucImg, const int nWidth, const int nHeight, const int nPitch,
             const float fFactor);

// Convex hull
COMMONEX_API
bool ConvexHull(const UN_POINT_S *pstPoints, bool *pbFlags, const int nNum);

// mean and standard variation
COMMONEX_API
bool MeanStd(const uchar *pucImg, const int nWidth, const int nHeight, const int nPitch,
             float &fMean, float &fStd);

#endif // __COMMON_ALGORITHM_EX_H__
