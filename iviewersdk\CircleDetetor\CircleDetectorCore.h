// circle detector
// Copyright (C) UNIC Technologies. All rights reserved.
// History:
// (1) 20120711: <NAME_EMAIL>
// (2) 20121117: change nSmoothRadius to fScale by Steve

#ifndef CIRCLE_DETECTOR_H
#define CIRCLE_DETECTOR_H

#include <vector>
using namespace std;
#include "AlgInterface.h"
#include "uvision.h"
#include "EdgeDetector.h"
using namespace EdgeDetector;

#ifndef min
#define min(a, b)            (((a) < (b)) ? (a) : (b))
#endif
#ifndef max
#define max(a, b)            (((a) > (b)) ? (a) : (b))
#endif

class CircleDetector : public CBaseInspector
{
public:
	CircleDetector();
	~CircleDetector();

	virtual Type GetType() const;

	// name and description of inspector
	virtual void GetNameAndDescription(std::wstring &name, std::wstring &description) const;

	// input image
	virtual int GetNumImages() const;
	virtual bool GetImageInfo(int i, std::wstring &name, std::wstring &description) const;
	virtual bool SetImage(int i, void *image, int width, int height, int bytesPerLine, int bitsPerPixel);

	// input regions
	virtual int GetNumRegionUsages() const;
	virtual bool GetRegionUsageInfo(int i, std::wstring &name, std::wstring &description, int &usage, int &typeOption) const;
	virtual bool SetRegions(const std::vector<CRegion> &regions);

	// parameters
	virtual int GetNumParameters() const;
	virtual bool GetParameter(int i, CParameter &param) const;

	virtual bool SetParameter(int i, const CParameter &param);

	// input templates
	virtual int GetNumTemplates() const;
	virtual bool GetTemplateInfo(int i, std::wstring &name, std::wstring &description) const;
	virtual bool SetTemplate(int i, const std::wstring &pathName);

	// reference input
	virtual int GetNumReferenceInputs() const;
	virtual bool GetReferenceInputInfo(int i, CResultItem::Type &type) const;
	virtual bool SetReferenceInput(int i, const CResultItem &referenceInput);

	// do inspection
	virtual bool Inspect();

	// get results
	virtual int GetNumResultItems() const;
	virtual bool GetResultItemInfo(int i, CResultItem &resultItem) const;
	virtual bool GetResultItems(std::vector<CResultItem> &resultItems) const;

	// image registration
	virtual bool GetRegistrationResult(float &x, float &y, float &angle, float &refX, float &refY, float &scale) const;
	virtual bool SetRegistrationInfo(float x, float y, float angle, float refX, float refY, float scale);

public:
	enum Polarity
	{
		kPolarityBright,
		kPolarityDark,
		kPolarityAny
	};
	enum Channel
	{
		Channel_Gray,
		Channel_Red,
		Channel_Green,
		Channel_Blue
	};

	enum RoiUsage
	{
		RoiUsage_Include = 0
	};

	struct Param
	{
		Polarity ePolarity;
		int nEdgeTh; // low threshold in hysteresis thresholding, high threshold is 2 times of nEdgeTh
		int smoothRadius;
		float fRealMinDiameter;//unic:mm
		float fRealMaxDiameter;//unic:mm
		float fEpsilon;
		int nMaxCircles; // max number of circles to detect
		int nMinScore;
		int nDownSample2sqr;
		Channel channel;
		float fPixelSize;// unit:mm
		bool bDumpImage;
		Param()
		{
			ePolarity = kPolarityDark;
			nEdgeTh = 5;
			smoothRadius = 1;
			fRealMinDiameter = 0.02f;
			fRealMaxDiameter = 0.03f;
			fEpsilon = 0.01f;
			nMaxCircles = 1;
			nMinScore = 60;
			nDownSample2sqr = 0;
			channel = Channel_Gray;
			fPixelSize = 1.00f;
			bDumpImage = false;
		}
	};

	struct Circle
	{
		float fX, fY, fR;
		int nScore;
		Polarity ePolarity;
	};

    int SetPara(const Param &para);
  
private:
	Img m_testImg;

	Param m_param;
	vector<Circle> m_circles;
	vector<EdgeChain> m_edgeChains;

	float m_alignX;
	float m_alignY;
	float m_alignAngle;
	float m_alignRefX;
	float m_alignRefY;
	float m_alignScale;

	vector<CRegion> m_rois;

private:
	void Detect(unsigned char *pImg, int nImgWidth, int nImgHeight);
	void DetectHough(unsigned char *pImg, int nImgWidth, int nImgHeight);
	//void DownSampleDetect(unsigned char *pImg, int nImgWidth, int nImgHeight);

	// fitting based method
	bool FitCircleRANSAC(vector<EdgePoint> &points, float fMinRadius, float fMaxRadius, float fEpsilon, Circle &circle);
	void MeanSmooth1d(const int *pSrc, int *pDst, int nSrcStep, int nDstStep, int n, int nR);

	// verify circle
	void VerifyCircle(unsigned char *pMagImg, unsigned char *pDirImg, int nImgWidth, int nImgHeight, Polarity polarity, float fEpsilon, Circle &circle);
    void GetValidCirclePoints(unsigned char *pNmsImg, unsigned char *pDirImg, int nImgWidth, int nImgHeight, 
                      Polarity ePolarity, float fEpsilon, Circle &circle, vector<EdgePoint> &points);

	// post processing
	void RemoveWeakCircles(vector<Circle> &circles, int n);
  
	bool FitCircle3Points(float fX[], float fY[], float &fCenterX, float &fCenterY, float &fRadius);
	bool FitCircleLS(vector<float> &points, float &fCenterX, float &fCenterY, float &fRadius);
	bool FitCircleWLS(vector<float> &points, float &fCenterX, float &fCenterY, float &fRadius);
	void DownSampleImage(Img &src, Img &dst);//LYY


	void ClearResults() { m_circles.clear(); }
	bool SetTestImage(unsigned char *img, int imgWidth, int imgHeight, int bytesPerLine, int bitsPerPixel);//LYY
};

#endif // CIRCLE_DETECTOR_H
