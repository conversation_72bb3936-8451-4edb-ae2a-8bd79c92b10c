#ifndef HX_ST_REPORT_H__
#define HX_ST_REPORT_H__
#include "reports.h"
class STReport : public PathologyReport
{
public:
    STReport();
    ~STReport();
    virtual std::string type();

    virtual void type(const std::string& typeName);

    virtual void process(PathologyImageItemPtr imgItem, const std::vector<WTensor>& outputs = std::vector<WTensor>())override;
	std::string ids_to_string(const int* ids,int size);
	std::string get_raw_text_data()const;
	inline int get_label_type()const { return label_type_; }
	inline std::string text() const { return text_; }
private:
	std::string raw_text_data_;
	std::string text_;
	int label_type_ = -1;

};

#endif
