/**
* @date         2014-07-16
* @filename     Clearness.cpp
* @purpose      example class for using CAlgoInterface class
* @version      1.0
* @history      initial draft
* <AUTHOR> UNIC, Beijing, China
* @copyright    UNIC Technologies, 2005-2014. All rights reserved.
*/

#include <opencv2/opencv.hpp>
#include "./AlgoInterface.h"
#include "./Clearness.h"
#include "../CommonEx/CommonEx.h"

using namespace cv;

static const char *g_acFocus[] =
{
    "Vollath",
    "Tenengrad",
    "Brenner",
    "BlurMetric",
    "UNIC",
    "VollathAbs"
};

static const char *g_acColor[] =
{
    "Gray",
    "GRGB",
    "GBGR",
    "RGBG",
    "BGRG",
    "Color"
};

/**
* @method       CreateGapObject
* @access       public
* @brief        access point for loading DLL
* @return       void * __cdecl
* <AUTHOR> <EMAIL>
* @date         2012-07-18
* @history      initial draft
*/
extern "C" __declspec(dllexport) void * __cdecl CreateGapObject()
{
    return mnew CClearness();
}

/**
* @method       DestroyGapObject
* @access       public
* @brief        destroy an object
* @param        CAlgoInterface * & pAlgo
* @return       bool __cdecl
* <AUTHOR> Lei, <EMAIL>
* @date         2013-03-04
* @history      initial draft
*/
extern "C" __declspec(dllexport) bool __cdecl DestroyGapObject(CAlgoInterface *&pAlgo)
{
    CClearness *pThis = dynamic_cast<CClearness *>(pAlgo);
    if (NULL == pThis)
    {
        return false;
    }

    delete pThis;
    pAlgo = NULL;
    return true;
}

/**
* @method       CClearness::CClearness
* @access       public
* @brief        construct function
* @param        void
* @return
* <AUTHOR> Lei, <EMAIL>
* @date         2012-07-18
* @history      initial draft
*/
CClearness::CClearness(void)
{
    // initialization
    m_pucImgSrc = NULL;
    m_pucImgH = NULL;
    m_pucImgV = NULL;
    m_pucImgTpl = NULL;
    m_nWidth = 0;
    m_nHeight = 0;
    m_nChannels = 0;
    m_nImgSize = 0;
    m_nLeft = 0;
    m_nTop = 0;
    m_bHasTemplate = false;
    char acCode[32] = {0};
    int nCnt = 0;
    acCode[nCnt++] = 'A';
    acCode[nCnt++] = 'l';
    acCode[nCnt++] = 'g';
    acCode[nCnt++] = '0';
    acCode[nCnt++] = '.';
    acCode[nCnt++] = 'R';
    acCode[nCnt++] = 'D';
    acCode[nCnt++] = '@';
    acCode[nCnt++] = 'U';
    acCode[nCnt++] = 'N';
    acCode[nCnt++] = 'I';
    acCode[nCnt++] = 'C';
    acCode[nCnt++] = '#';
    acCode[nCnt++] = '3';
    acCode[nCnt++] = '2';
    acCode[nCnt++] = '2';
    acCode[nCnt++] = '\0';
    SetCode((int) acCode);
}

/**
* @method       CClearness::~CClearness
* @access       public
* @brief        destruct function
* @param        void
* @return
* <AUTHOR> Lei, <EMAIL>
* @date         2012-07-18
* @history      initial draft
*/
CClearness::~CClearness(void)
{
    mdelete(m_pucImgSrc);
} //lint !e1740

/**
* @method       CClearness::PackPara
* @access       private
* @brief        pack parameters
* @param        void
* @return       void
* <AUTHOR> Lei, <EMAIL>
* @date         2012-07-18
* @history      initial draft
*/
int CClearness::PackPara(void)
{
    StartPackPara();
    AddGroupPara("#Focus type");
    UI_RADIO_S stRadio;
    for (int i = 0; i < sizeof(g_acFocus) / sizeof(g_acFocus[0]); ++i)
    {
        stRadio.bChecked = i == m_para.nFocusType;
        AddElemPara(stRadio, g_acFocus[i]);
    }

    AddGroupPara(g_acFocus[FOCUS_UNIC]);
    for (int i = 0; i < sizeof(g_acColor) / sizeof(g_acColor[0]); ++i)
    {
        stRadio.bChecked = i == m_para.nColorType;
        AddElemPara(stRadio, g_acColor[i]);
    }
    UI_EDIT_S stEdit;
    stEdit.nMin = 1;
    stEdit.nMax = 4;
    stEdit.nValue = m_para.nScale;
    AddElemPara(stEdit, "Scale");
    UI_CHECK_S stCheck;
    stCheck.bChecked = m_para.bUseColor;
    AddElemPara(stCheck, "Use color");

    AddGroupPara(g_acFocus[FOCUS_VOLLATH_ABS]);
    stCheck.bChecked = m_para.bAdjust;
    AddElemPara(stCheck, "Adjust focus");
    stEdit.nMin = 1;
    stEdit.nMax = 255;
    stEdit.nValue = m_para.ucBkgTh;
    AddElemPara(stEdit, "Background threshold");

    return 0;
}

/**
* @method       CClearness::UnpackPara
* @access       private
* @brief        unpack parameters
* @param        void
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-07-16
* @history      initial draft
*/
int CClearness::UnpackPara(void)
{
    StartUnpackPara();
    UI_RADIO_S stRadio;
    for (int i = 0; i < sizeof(g_acFocus) / sizeof(g_acFocus[0]); ++i)
    {
        GetValuePara("#Focus type", g_acFocus[i], &stRadio);
        if (stRadio.bChecked)
        {
            m_para.nFocusType = i;
            break;
        }
    }

    for (int i = 0; i < sizeof(g_acColor) / sizeof(g_acColor[0]); ++i)
    {
        GetValuePara(g_acFocus[FOCUS_UNIC], g_acColor[i], &stRadio);
        if (stRadio.bChecked)
        {
            m_para.nColorType = i;
            break;
        }
    }

    UI_EDIT_S stEdit;
    GetValuePara(g_acFocus[FOCUS_UNIC], "Scale", &stEdit);
    m_para.nScale = stEdit.nValue;

    UI_CHECK_S stCheck;
    GetValuePara(g_acFocus[FOCUS_UNIC], "Use color", &stCheck);
    m_para.bUseColor = stCheck.bChecked;

    GetValuePara(g_acFocus[FOCUS_VOLLATH_ABS], "Adjust focus", &stCheck);
    m_para.bAdjust = stCheck.bChecked;
    GetValuePara(g_acFocus[FOCUS_VOLLATH_ABS], "Background threshold", &stEdit);
    m_para.ucBkgTh = (uchar) stEdit.nValue;

    return 0;
}

/**
* @method       CClearness::SetImage
* @access       virtual private
* @brief        set extra images (template or something)
* @param        const int nIndex
* @param        const UN_IMAGE_INFO_S * pstImg
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-16
* @history      initial draft
*/
int CClearness::SetImage(const int nIndex, const UN_IMAGE_INFO_S *pstImg)
{
    m_bHasTemplate = false;
    if (0 != nIndex || NULL == pstImg)
    {
        return 1;
    }

    int nRv = SetImage(pstImg, true);
    m_bHasTemplate = 0 == nRv;

    return nRv;
}

// do the main job using this input image
int CClearness::DoInspect(const unsigned char *pucImg, const int nWidth, const int nHeight,
    const int nPitch, const int nLeft, const int nTop,
    const int nRight, const int nBottom)
{
    UN_IMAGE_INFO_S stImg;
    stImg.pucBuffer = const_cast<unsigned char*>(pucImg);
    stImg.nWidth = nWidth;
    stImg.nHeight = nHeight;
    stImg.nPitch = nPitch;
    stImg.nLeft = nLeft;
    stImg.nRight = nRight;
    stImg.nTop = nTop;
    stImg.nBottom = nBottom;
    
    return DoInspect(&stImg);
}

/**
* @method       CClearness::DoInspect
* @access       public
* @brief        do the main job
* @param        const UN_IMAGE_INFO_S * pstImg
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-16
* @history      initial draft
*/
int CClearness::DoInspect(const UN_IMAGE_INFO_S *pstImg)
{
    m_fFocus = 0.0f;
    m_fScore = 0.0f;
    m_fBkg = 0.0f;

    // allocate memory and copy gray image
    int nRv = SetImage(pstImg, false);
    if (0 != nRv)
    {
        return nRv;
    }

    const int nWidth = m_nWidth, nHeight = m_nHeight;
    switch (m_para.nFocusType)
    {
    case FOCUS_VOLLATH:
        m_fFocus = Vollath(m_pucImgSrc, nWidth, nHeight);
        break;
    case FOCUS_TENENGRAD:
        m_fFocus = Tenengrad(m_pucImgSrc, nWidth, nHeight);
        break;
    case FOCUS_BRENNER:
        m_fFocus = Brenner(m_pucImgSrc, nWidth, nHeight);
        break;
    case FOCUS_BLUR_METRIC:
        m_fFocus = BlurMetric(m_pucImgSrc, m_pucImgH, m_pucImgV, nWidth, nHeight);
        break;
    case FOCUS_UNIC:
        m_fFocus = FocusUnic(m_pucImgSrc, m_pucImgH, m_pucImgV, nWidth, nHeight,
            m_para.nColorType, m_para.bUseColor, m_para.nScale);
        break;
    case FOCUS_VOLLATH_ABS:
        m_fFocus = VollathAbs(m_pucImgSrc, m_pucImgH, m_pucImgV, nWidth, nHeight);
        break;
    default:
        nRv = 1;
        break;
    }

    return 0;
}

/**
* @method       CClearness::PackResult
* @access       private
* @brief        pack results
* @param        void
* @return       void
* <AUTHOR> Lei, <EMAIL>
* @date         2012-07-18
* @history      initial draft
*/
int CClearness::PackResult(void)
{
    StartPackResult();
    AddGroupResult("Focus");
    UN_ARRAY_CHAR_S stChar;
    stChar.pcData = (char *) g_acFocus[m_para.nFocusType];
    stChar.nNum = strlen(g_acFocus[m_para.nFocusType]);
    AddElemResult(stChar, "Algorithm");
    AddGroupResult("Focus");
    AddElemResult(m_fFocus, "Clearness");
    if (FOCUS_UNIC == m_para.nFocusType && m_para.bUseColor)
    {
        AddGroupResult("Focus");
        AddElemResult(m_fScore, "Color score");
    }
    if (m_para.bAdjust && FOCUS_VOLLATH_ABS == m_para.nFocusType)
    {
        AddGroupResult("Focus");
        AddElemResult(IsGoodForJudge(), "Good for judge");
    }
    return 0;
}

/**
* @method       CClearness::PackDisplay
* @access       private
* @brief        pack display
* @param        void
* @return       void
* <AUTHOR> Lei, <EMAIL>
* @date         2012-07-18
* @history      initial draft
*/
int CClearness::PackDisplay(void)
{
    StartPackDisp();
    AddGroupDisp("Pad");
    return 0;
}

/**
* @method       CClearness::CheckImageInfo
* @access       public
* @brief        check image information
* @param        const UN_IMAGE_INFO_S * pstImg
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-24
* @history      initial draft
*/
int CClearness::CheckImageInfo(const UN_IMAGE_INFO_S *pstImg) const
{
    // check input parameters
    if (NULL == pstImg || NULL == pstImg->pucBuffer)
    {
        return 1;
    }

    if (IMAGE_MIN_SIZE > pstImg->nWidth || IMAGE_MIN_SIZE > pstImg->nHeight
        || IMAGE_MAX_SIZE < pstImg->nWidth || IMAGE_MAX_SIZE < pstImg->nHeight
        || pstImg->nPitch < pstImg->nWidth)
    {
        return 1;
    }

    if (0 == pstImg->nLeft && 0 == pstImg->nRight
        && 0 == pstImg->nTop && 0 == pstImg->nBottom)
    {
        memcpy((void *) &pstImg->nRight, &pstImg->nWidth, sizeof(pstImg->nWidth));
        memcpy((void *) &pstImg->nBottom, &pstImg->nHeight, sizeof(pstImg->nHeight));
        return 0;
    }
    else if (0 > pstImg->nLeft || 0 > pstImg->nTop
        || pstImg->nWidth < pstImg->nRight || pstImg->nHeight < pstImg->nBottom
        || pstImg->nLeft >= pstImg->nRight || pstImg->nTop >= pstImg->nBottom)
    {
        return 1;
    }

    if (pstImg->nLeft + IMAGE_MIN_SIZE > pstImg->nRight
        || pstImg->nTop + IMAGE_MIN_SIZE > pstImg->nBottom)
    {
        return 1;
    }

    return 0;
}

/**
* @method       CClearness::SetImage
* @access       private
* @brief        allocate memory and copy gray image
* @param        const UN_IMAGE_INFO_S * pstImg
* @param        const bool bTemplate
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-16
* @history      initial draft
*/
int CClearness::SetImage(const UN_IMAGE_INFO_S *pstImg, const bool bTemplate)
{
    // check input image
    int nRv = CheckImageInfo(pstImg);
    if (0 != nRv)
    {
        return nRv;
    }

    // check image's channel
    const int nChannel = pstImg->nPitch / pstImg->nWidth;
    if (1 != nChannel && 3 != nChannel && 4 != nChannel)
    {
        return 1;
    }

    // special checking for UNIC
    const bool bUnic = FOCUS_UNIC == m_para.nFocusType;
    if (bUnic && ((COLOR_TRUE == m_para.nColorType && 1 == nChannel)
        || (COLOR_TRUE != m_para.nColorType && 1 != nChannel)))
    {
        return 1;
    }
    if (bUnic && COLOR_GRAY == m_para.nColorType && m_para.bUseColor)
    {
        m_para.bUseColor = false;
    }
    if (bUnic && !m_para.bUseColor)
    {
        const int *pnCh = &nChannel;
        *((int *) pnCh) = 1;
    }

#define COPY_COLOR_IMAGE
#define USE_ROI
#ifdef USE_ROI
    int nWidth = pstImg->nRight - pstImg->nLeft;
    int nHeight = pstImg->nBottom - pstImg->nTop;
    m_nLeft = pstImg->nLeft;
    m_nTop = pstImg->nTop;
#else
    int nWidth = pstImg->nWidth, nHeight = pstImg->nHeight;
    m_nLeft = 0;
    m_nTop = 0;
#endif

    // allocate memory
#ifdef COPY_COLOR_IMAGE
    if (m_nWidth != nWidth || m_nHeight != nHeight || m_nChannels != nChannel)
#else
    if (m_nWidth != nWidth || m_nHeight != nHeight)
#endif
    {
        mdelete(m_pucImgSrc);

        const int nImgSize = nWidth * nHeight;
#ifdef COPY_COLOR_IMAGE
        const int nImgNum = bUnic ? nChannel + 3 : 3;
#else
        const int nImgNum = 1 + 3;
#endif

        // allocate memory for saving source image
        m_pucImgSrc = mnew uchar[nImgSize * nImgNum];
        if (NULL == m_pucImgSrc)
        {
            return 1;
        }

#ifdef COPY_COLOR_IMAGE
        m_nChannels = bUnic ? nChannel : 1;
#else
        m_nChannels = 1;
#endif
        m_nWidth = nWidth;
        m_nHeight = nHeight;
        m_nImgSize = m_nWidth * m_nHeight;
        m_pucImgH = m_pucImgSrc + m_nImgSize * m_nChannels;
        m_pucImgV = m_pucImgH + m_nImgSize;
        m_pucImgTpl = m_pucImgV + m_nImgSize;
    }

#ifdef USE_ROI
    const int nLeft = pstImg->nLeft, nRight = pstImg->nRight;
    const int nTop = pstImg->nTop, nBottom = pstImg->nBottom;
#else
    const int nLeft = 0, nRight = m_nWidth;
    const int nTop = 0, nBottom = m_nHeight;
#endif

#ifdef COPY_COLOR_IMAGE
    uchar *pucImg = bTemplate ? m_pucImgTpl : m_pucImgSrc;
    if (bUnic && m_para.bUseColor && 3 <= m_nChannels)
    {
        CopyColorData(pstImg->pucBuffer, pucImg, pstImg->nWidth, pstImg->nHeight,
            pstImg->nPitch, m_nWidth * m_nChannels, nLeft, nTop, nRight, nBottom);
    }
    else
    {
        CopyGrayData(pstImg->pucBuffer, pucImg, pstImg->nWidth, pstImg->nHeight,
            pstImg->nPitch, nLeft, nTop, nRight, nBottom);
    }
#else
#if 0
    if (0 <= m_para.nChannel && nChannel > m_para.nChannel)
    {
        ExtractChannel(pstImg->pucBuffer, pucImg, pstImg->nWidth, pstImg->nHeight,
            pstImg->nPitch, m_para.nChannel, nLeft, nTop, nRight, nBottom);
    }
    else
    {
        CopyGrayData(pstImg->pucBuffer, pucImg, pstImg->nWidth, pstImg->nHeight,
            pstImg->nPitch, nLeft, nTop, nRight, nBottom);
    }
#endif
#endif

    return 0;
}

/**
* @method       CClearness::GetPara
* @access       public
* @brief        get current parameters
* @param        Para & para
* @return       void
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-16
* @history      initial draft
*/
void CClearness::GetPara(Para &para) const
{
    para = m_para;
}

/**
* @method       CClearness::SetPara
* @access       public
* @brief        set parameters
* @param        const Para & para
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-16
* @history      initial draft
*/
int CClearness::SetPara(const Para &para)
{
    // you could check the validation of input parameters
    m_para = para;
    return 0;
}

float CClearness::BlurMetric(const uchar *pucImg, uchar *pucH, uchar *pucV,
                             const int nWidth, const int nHeight)
{
    if (NULL == pucImg || NULL == pucH || NULL == pucV
        || 0 >= nWidth || 0 >= nHeight)
    {
        return 0.0f;
    }

    Mat img(nHeight, nWidth, CV_8UC1, (void *) pucImg, nWidth);
    Mat h(nHeight, nWidth, CV_8UC1, pucH, nWidth);
    //Mat v(nHeight, nWidth, CV_8UC1, pucV, nWidth);

    blur(img, h, Size(9, 1));
    //blur(img, v, Size(1, 9));

    int nDiffSum = 0, nGapSum = 0, nDiff = 0, nSmooth = 0;
    for (int i = 0; i < nHeight; ++i)
    {
        const uchar *pucOri = pucImg + i * nWidth;
        uchar *pucSmooth = pucH + i * nWidth;
        for (int j = 0; j < nWidth - 1; ++j)
        {
            nDiff = abs(*pucOri - *(pucOri + 1));
            nSmooth = abs(*pucSmooth - *(pucSmooth + 1));

            nDiffSum += nDiff;
            nGapSum += max(0, nDiff - nSmooth);

            pucOri++;
            pucSmooth++;
        }
    }

    float fFocus = nGapSum * 100.0f / nDiffSum;
    return fFocus;
}

float CClearness::FocusUnic(const uchar *pucImg, uchar *pucGray, uchar *pucColor,
                            const int nWidth, const int nHeight, const int nColor,
                            const bool bUseColor, const int nScale)
{
    if (NULL == pucImg || NULL == pucGray || NULL == pucColor
        || 0 >= nWidth || 0 >= nHeight || 1 > nScale
        || (COLOR_GRAY == nColor && bUseColor))
    {
        return 0.0f;
    }

    float fFocus = 0.0f;
    switch (nColor)
    {
    case COLOR_GRAY:
        fFocus = FocusUnicGray(pucImg, nWidth, nHeight, nScale);
        break;
    case COLOR_TRUE:
        if (!bUseColor)
        {
            fFocus = FocusUnicGray(pucImg, nWidth, nHeight, nScale);
        }
        else
        {
            fFocus = FocusUnicColor(pucImg, pucGray, pucColor,
                nWidth, nHeight, m_nChannels, nScale, m_fScore);
        }
        break;
    case COLOR_GRGB:
    case COLOR_GBGR:
    case COLOR_RGBG:
    case COLOR_BGRG:
       fFocus = FocusUnicBayer(pucImg, pucGray, pucColor, nWidth, nHeight, nColor, bUseColor, m_fScore);
		//fFocus = Tenengrad(pucImg, pucGray, pucColor,nWidth, nHeight, nColor, bUseColor, m_fScore);
		
        break;
    default:
        break;
    }

    return fFocus;
}

float  CClearness::FluorScoring(uchar* pfImage, int nWidth, int nHeight, int nDepth, float* pfscore1, float* pfscore2)
{
	uchar* pfImg = NULL;
	int nZoom = 1;
	if (nDepth == 3)
	{
		pfImg = (uchar*)calloc(nWidth * nHeight, sizeof(unsigned char));
		uchar* p0, *p1;
		for (int y = 0; y < nHeight; y += nZoom)
		{
			p0 = (uchar*)(pfImage + y * nWidth * nDepth);
			p1 = (uchar*)(pfImg + y * nWidth);
			for (int x = 0; x < nWidth; x += nZoom)
			{
				*(p1 + x) = (uchar)(0.114f * *(p0 + x * nDepth + 0) + 0.587f * *(p0 + x * nDepth + 1) + 0.299f * *(p0 + x * nDepth + 2));
			}
		}
	}
	else if (nDepth == 1)
		pfImg = pfImage;
	else
		return 0.0f;

	///////////////////////////////////////////
	// calc histogram
	int x, y, n;
	uchar* p;
	unsigned long sum = 1;
	unsigned long mean = 0;
	float fdev = 0.f;

	unsigned long	nHisto[256] = { 0 };
	for (y = 0; y < nHeight; y += nZoom)
	{
		p = (uchar*)(pfImg + y * nWidth);
		for (int x = 0; x < nWidth; x += nZoom)
		{
			nHisto[*(p + x)]++;
			sum++;
			mean += *(p + x);
		}
	}

	///////////////////////////////////////////
	// calc 1st level mean and dev
	mean /= sum;
	for (n = 1; n < 256; n++)
	{
		fdev += nHisto[n] * (n - mean) * (n - mean);
	}
	fdev = sqrt(fdev / sum);

	///////////////////////////////////////////
	// calc 2nd level means and devs
	// L
	unsigned long sumL = 1;
	unsigned long meanL = 0;
	float fdevL = 0.f;
	for (n = 1; n < mean; n++)
	{
		sumL += nHisto[n];
		meanL += nHisto[n] * n;
	}
	meanL /= sumL;
	for (n = 1; n < mean; n++)
	{
		fdevL += nHisto[n] * (n - meanL) * (n - meanL);
	}
	fdevL = sqrt(fdevL / sumL);

	// R
	unsigned long sumR = 1;
	unsigned long meanR = 0;
	float fdevR = 0.f;
	for (n = mean; n < 256; n++)
	{
		sumR += nHisto[n];
		meanR += nHisto[n] * n;
	}
	meanR /= sumR;
	for (n = mean; n < 256; n++)
	{
		fdevR += nHisto[n] * (n - meanR) * (n - meanR);
	}
	fdevR = sqrt(fdevR / sumR);

	///////////////////////////////////////////
	// calc 3rd level means and devs
	// LL
	unsigned long sumLL = 1;
	unsigned long meanLL = 0;
	float fdevLL = 0.f;
	for (n = 1; n < meanL; n++)
	{
		sumLL += nHisto[n];
		meanLL += nHisto[n] * n;
	}
	meanLL /= sumLL;
	for (n = 1; n < meanL; n++)
	{
		fdevLL += nHisto[n] * (n - meanLL) * (n - meanLL);
	}
	fdevLL = sqrt(fdevLL / sumLL);

	// LR
	unsigned long sumLR = 1;
	unsigned long meanLR = 0;
	float fdevLR = 0.f;
	for (n = meanL; n < mean; n++)
	{
		sumLR += nHisto[n];
		meanLR += nHisto[n] * n;
	}
	meanLR /= sumLR;
	for (n = meanL; n < mean; n++)
	{
		fdevLR += nHisto[n] * (n - meanLR) * (n - meanLR);
	}
	fdevLR = sqrt(fdevLR / sumLR);

	// RL
	unsigned long sumRL = 1;
	unsigned long meanRL = 0;
	float fdevRL = 0.f;
	for (n = mean; n < meanR; n++)
	{
		sumRL += nHisto[n];
		meanRL += nHisto[n] * n;
	}
	meanRL /= sumRL;
	for (n = mean; n < meanR; n++)
	{
		fdevRL += nHisto[n] * (n - meanRL) * (n - meanRL);
	}
	fdevRL = sqrt(fdevRL / sumRL);

	// RR
	unsigned long sumRR = 1;
	unsigned long meanRR = 0;
	float fdevRR = 0.f;
	for (n = meanR; n < 256; n++)
	{
		sumRR += nHisto[n];
		meanRR += nHisto[n] * n;
	}
	meanRR /= sumRR;
	for (n = meanR; n < 256; n++)
	{
		fdevRR += nHisto[n] * (n - meanRR) * (n - meanRR);
	}
	fdevRR = sqrt(fdevRR / sumRR);

	///////////////////////////////////////////
	// calc 3rd level means and devs
	float fdelta = 1.0f;
	if (fdevRL < 1.0f)	fdelta = 10.0f * (1.0f - fdevRL);
	else				fdelta *= 1.f;
	float s1 = fdev + fdevL + fdevR;
	float s2 = fdelta * (fdevR / meanR) * (fdevRR / meanRR) * 100.f;

	if (pfscore1 != NULL) *pfscore1 = s1;
	if (pfscore2 != NULL) *pfscore2 = s2;

	if (NULL != pfImg && nDepth == 3) free(pfImg);

	return (s1 > s2) ? s1 : s2;
}
float CClearness::FocusUnicGray(const uchar *pucImg, const int nWidth,
                                const int nHeight, const int nScale)
{
    int anHist[256] = {0};
    for (int i = 0; i < nHeight - 1; i += nScale)
    {
        const uchar *pucCur = pucImg + i * nWidth;
        const uchar *pucN = pucCur + nWidth;
        for (int j = 0; j < nWidth - 1; j += nScale)
        {
            anHist[abs(*pucCur - *(pucCur + 1))]++;
            anHist[abs(*pucCur - *pucN)]++;
            pucCur++;
            pucN++;
        }
    }

#ifdef _DEBUG
    SaveImage(pucImg, nWidth, nHeight, "d:/tmp image/gray01.bmp");
#endif

    int nMaxEdgNum = (nWidth - 1) * (nHeight - 1) / (nScale * nScale * 32);
    int nCount = 0, i = 0;
    float fSum = 0.0f;
    for (i = 255; i > 2; --i)
    {
        nCount += anHist[i];
        if (nCount > nMaxEdgNum)
        {
            break;
        }
        fSum += anHist[i] * i * i;
    }
    if (i > 2)
    {
        fSum += (anHist[i] - (nCount - nMaxEdgNum)) * i * i;
    }

    return sqrtf(fSum / nMaxEdgNum);
}

float CClearness::FocusUnicColor(const uchar *pucImg, uchar *pucGray, uchar *pucColor,
                                 const int nWidth, const int nHeight, const int nChannels,
                                 const int nScale, float &fScore)
{
    if (NULL == pucImg || NULL == pucGray || NULL == pucColor
        || 0 >= nWidth || 0 >= nHeight || 1 > nScale
        || (3 != nChannels && 4 != nChannels))
    {
        return 0.0f;
    }

    const int nW = nWidth / nScale, nH = nHeight / nScale;
    for (int i = 0; i < nHeight; i += nScale)
    {
        const uchar *pucImgRow = pucImg + i * nWidth * nChannels;
        uchar *pucGrayRow = pucGray + (i / nScale) * nW;
        uchar *pucColorRow = pucColor + (i / nScale) * nW;
        for (int j = 0; j < nWidth; j += nScale)
        {
            uchar ucB = *pucImgRow, ucG = *(pucImgRow + 1), ucR = *(pucImgRow + 2);
            *pucGrayRow = (306 * ucR + 601 * ucG + 117 * ucB + 512) >> 10;
            int nMax = max(ucB, max(ucG, ucR));
            int nMin = min(ucB, min(ucG, ucR));
            *pucColorRow = (uchar) ((nMax - nMin) * 255 / max(nMax, 30));
            pucImgRow += nScale * nChannels;
            pucGrayRow++;
            pucColorRow++;
        }
    }

#ifdef _DEBUG
    SaveImage(pucGray, nW, nH, "d:/tmp image/gray02.bmp");
    SaveImage(pucColor, nW, nH, "d:/tmp image/color.bmp");
#endif

    int anHistGray[256] = {0}, anHistColor[256] = {0};
    for (int i = 0; i < nH - 1; ++i)
    {
        uchar *pucGrayRow = pucGray + i * nW;
        uchar *pucColorRow = pucColor + i * nW;
        for (int j = 0; j < nW - 1; ++j)
        {
            anHistGray[abs(pucGrayRow[j] - pucGrayRow[j + 1])]++;
            anHistGray[abs(pucGrayRow[j] - pucGrayRow[j + nW])]++;
            anHistColor[abs(pucColorRow[j] - pucColorRow[j + 1])]++;
            anHistColor[abs(pucColorRow[j] - pucColorRow[j + nW])]++;
        }
    }

    // gray focus
    float fFocus = 0.0f;
    int nMaxEdgNum = (nW - 1) * (nH - 1) / 32;
    int nCount = 0, i = 0;
    float fSum = 0.0f;
    for (i = 255; i > 2; --i)
    {
        nCount += anHistGray[i];
        if (nCount > nMaxEdgNum)
        {
            break;
        }
        fSum += anHistGray[i] * i * i;
    }
    if (i > 2)
    {
        fSum += (anHistGray[i] - (nCount - nMaxEdgNum)) * i * i;
    }
    fFocus = sqrtf(fSum / nMaxEdgNum);

    // color score
    nCount = 0;
    fSum = 0;
    for (i = 255; i > 2; i--)
    {
        nCount += anHistColor[i];
        if (nCount > nMaxEdgNum)
        {
            break;
        }
        fSum += anHistColor[i] * i * i;
    }
    if (i > 2)
    {
        fSum += (anHistColor[i] - (nCount - nMaxEdgNum)) * i * i;
    }
    fScore = fSum / ((nW - 1) * (nH - 1));

    return fFocus;
}
float CClearness::Tenengrad(const uchar *pucImg, uchar *pucGray, uchar *pucColor,
	const int nWidth, const int nHeight, const int nColor,
	const bool bUseColor, float &fScore)
{
	if (NULL == pucImg || NULL == pucGray || NULL == pucColor
		|| 0 >= nWidth || 0 >= nHeight
		|| COLOR_GRGB > nColor || COLOR_BGRG < nColor)
	{
		return 0.0f;
	}

	int nR = 0, nG1 = 0, nG2 = 0, nB = 0;
	switch (nColor)
	{
	case COLOR_GRGB:
		nR = 1;
		nB = nWidth;
		nG1 = 0;
		nG2 = nWidth + 1;
		break;
	case COLOR_GBGR:
		nB = 1;
		nR = nWidth;
		nG1 = 0;
		nG2 = nWidth + 1;
		break;
	case COLOR_RGBG:
		nG1 = 1;
		nG2 = nWidth;
		nR = 0;
		nB = nWidth + 1;
		break;
	case COLOR_BGRG:
		nG1 = 1;
		nG2 = nWidth;
		nB = 0;
		nR = nWidth + 1;
		break;
	default:
		break;
	}
	float fFocus = 0.0f;
	const int nW = nWidth / 2, nH = nHeight / 2;
	float sum = 0;
	if (bUseColor)
	{
		for (int i = 0; i < nH; ++i)
		{
			const uchar *pucImgRow = pucImg + i * 2 * nWidth;
			uchar *pucGrayRow = pucGray + i * nW;
			for (int j = 0; j < nW; ++j)
			{
				uchar ucB = *(pucImgRow + nB);
				uchar ucG = (*(pucImgRow + nG1) + *(pucImgRow + nG2)) / 2;
				uchar ucR = *(pucImgRow + nR);
				*pucGrayRow = (306 * ucR + 601 * ucG + 117 * ucB + 512) >> 10;
				sum += *pucGrayRow;
				pucImgRow += 2;
				pucGrayRow++;
			}
		}
	}
	int aver = sum / (nW * nH);
	sum = 0;
	for (int i = 1; i < nH-1; i++)
	{
		const uchar *pucGrayRow = pucGray + i * nW + 1;
		for (int j = 1; j < nW - 1; j++)
		{
			sum = sum + (*pucGrayRow - aver)*(*pucGrayRow - aver);
			pucGrayRow++;
		}
	}
	//sum = sum / ((nH - 1)*(nW - 1));
	float Factor = 0.0;
	for (int i = 1; i < nH - 1; i++)
	{
		const uchar *pucGrayRow = pucGray + i * nW + 1;
		for (int j = 1; j < nW - 1; j++)
		{
			int Gx = pucGrayRow[j-1+nW] + 2*pucGrayRow[j+nW] + pucGrayRow[j+1+nW] - pucGrayRow[j-1-nW] - 2*pucGrayRow[j-nW] - pucGrayRow[j+1-nW];
			int Gy = pucGrayRow[j-1-nW] + 2*pucGrayRow[j- 1] + pucGrayRow[j-1+nW]- pucGrayRow[j+1-nW] - 2*pucGrayRow[j+1] - pucGrayRow[j+1+nW];
			Factor = Factor + (*pucGrayRow - aver)*(*pucGrayRow - aver)/sum * (Gx*Gx+ Gy*Gy);
			pucGrayRow++;
		}
	}

	return Factor;

}
float CClearness::FocusUnicBayer(const uchar *pucImg, uchar *pucGray, uchar *pucColor,
                                 const int nWidth, const int nHeight, const int nColor,
                                 const bool bUseColor, float &fScore)
{
    if (NULL == pucImg || NULL == pucGray || NULL == pucColor
        || 0 >= nWidth || 0 >= nHeight
        || COLOR_GRGB > nColor || COLOR_BGRG < nColor)
    {
        return 0.0f;
    }

    int nR = 0, nG1 = 0, nG2 = 0, nB = 0;
    switch (nColor)
    {
    case COLOR_GRGB:
        nR = 1;
        nB = nWidth;
        nG1 = 0;
        nG2 = nWidth + 1;
        break;
    case COLOR_GBGR:
        nB = 1;
        nR = nWidth;
        nG1 = 0;
        nG2 = nWidth + 1;
        break;
    case COLOR_RGBG:
        nG1 = 1;
        nG2 = nWidth;
        nR = 0;
        nB = nWidth + 1;
        break;
    case COLOR_BGRG:
        nG1 = 1;
        nG2 = nWidth;
        nB = 0;
        nR = nWidth + 1;
        break;
    default:
        break;
    }

    float fFocus = 0.0f;
    const int nW = nWidth / 2, nH = nHeight / 2;
    if (bUseColor)
    {
        for (int i = 0; i < nH; ++i)
        {
            const uchar *pucImgRow = pucImg + i * 2 * nWidth;
            uchar *pucGrayRow = pucGray + i * nW;
            uchar *pucColorRow = pucColor + i * nW;
            for (int j = 0; j < nW; ++j)
            {
                uchar ucB = *(pucImgRow + nB);
                uchar ucG = (*(pucImgRow + nG1) + *(pucImgRow + nG2)) / 2;
                uchar ucR = *(pucImgRow + nR);
                *pucGrayRow = (306 * ucR + 601 * ucG + 117 * ucB + 512) >> 10;
                int nMax = max(ucB, max(ucG, ucR));
                int nMin = min(ucB, min(ucG, ucR));
                *pucColorRow = (uchar) ((nMax - nMin) * 255 / max(nMax, 30));
                pucImgRow += 2;
                pucGrayRow++;
                pucColorRow++;
            }
        }
#ifdef _DEBUG
        Mat bayer(nH, nW, CV_8UC1, pucGray, nW);
        imwrite("d:/tmp image/bayer.bmp", bayer);
#endif

        int anHistGray[256] = {0}, anHistColor[256] = {0};
        for (int i = 0; i < nH - 1; ++i)
        {
            uchar *pucGrayRow = pucGray + i * nW;
            uchar *pucColorRow = pucColor + i * nW;
            for (int j = 0; j < nW - 1; ++j)
            {
                anHistGray[abs(pucGrayRow[j] - pucGrayRow[j + 1])]++;
                anHistGray[abs(pucGrayRow[j] - pucGrayRow[j + nW])]++;
                anHistColor[abs(pucColorRow[j] - pucColorRow[j + 1])]++;
                anHistColor[abs(pucColorRow[j] - pucColorRow[j + nW])]++;
            }
        }
        // gray focus
		//fFocus = FluorScoring(pucGray, nW, nH, 1);
		//fFocus = Tenengrad(pucGray, nW, nH);
		//fFocus = Brenner(pucGray, nW, nH);
		
        int nMaxEdgNum = (nW - 1) * (nH - 1) / 32;
        int nCount = 0, i = 0;
        float fSum = 0.0f;
       for (i = 255; i > 2; --i)
        {
            nCount += anHistGray[i];
            if (nCount > nMaxEdgNum)
            {
                break;
            }
            fSum += anHistGray[i] * i * i;
        }
        if (i > 2)
        {
            fSum += (anHistGray[i] - (nCount - nMaxEdgNum)) * i * i;
        }
        fFocus = sqrtf(fSum / nMaxEdgNum);



        // color score
        nCount = 0;
        fSum = 0;
        for (i = 255; i > 2; i--)
        {
            nCount += anHistColor[i];
            if (nCount > nMaxEdgNum)
            {
                break;
            }
            fSum += anHistColor[i] * i * i;
        }
        if (i > 2)
        {
            fSum += (anHistColor[i] - (nCount - nMaxEdgNum)) * i * i;
        }
        fScore = fSum / ((nW - 1) * (nH - 1));
    }
    else
    {
        for (int i = 0; i < nH; ++i)
        {
            const uchar *pucImgRow = pucImg + i * 2 * nWidth;
            uchar *pucGrayRow = pucGray + i * nW;
            for (int j = 0; j < nW; ++j)
            {
                uchar ucB = *(pucImgRow + nB);
                uchar ucG = (*(pucImgRow + nG1) + *(pucImgRow + nG2)) / 2;
                uchar ucR = *(pucImgRow + nR);
                *pucGrayRow = (306 * ucR + 601 * ucG + 117 * ucB + 512) >> 10;
                pucImgRow += 2;
                pucGrayRow++;
            }
        }

        fFocus = FocusUnicGray(pucGray, nW, nH, 1);
    }

    return fFocus;
}

float CClearness::GetFocus(void) const
{
    return m_fFocus;
}

float CClearness::GetColorScore(void) const
{
    return m_fScore;
}

float CClearness::Vollath(const uchar *pucImg, const int nWidth, const int nHeight)
{
    if (NULL == pucImg || 0 >= nWidth || 2 >= nHeight)
    {
        return 0.0f;
    }

    int nFocus = 0;
    for (int i = 0; i < nHeight - 2; ++i)
    {
        const uchar *pucCur = pucImg + i * nWidth;
        const uchar *pucN = pucCur + nWidth;
        const uchar *pucNN = pucN + nWidth;
        for (int j = 0; j < nWidth; ++j)
        {
            nFocus += (*pucCur) * (((int) *pucN) - (*pucNN));
            pucCur++;
            pucN++;
            pucNN++;
        }
    }
    {
        const uchar *pucCur = pucImg + (nHeight - 2) * nWidth;
        const uchar *pucN = pucCur + nWidth;
        for (int j = 0; j < nWidth; ++j)
        {
            nFocus += (*pucCur) * (*pucN);
            pucCur++;
            pucN++;
        }
    }

    return nFocus * 1.0f / ((nHeight - 1) * nWidth);
}

float CClearness::Tenengrad(const uchar *pucImg, const int nWidth, const int nHeight)
{
    if (NULL == pucImg || 0 >= nWidth || 1 >= nHeight)
    {
        return 0.0f;
    }

    int nFocus = 0;
    for (int i = 0; i < nHeight - 1; ++i)
    {
        const uchar *pucCur = pucImg + i * nWidth;
        const uchar *pucN = pucCur + nWidth;
        for (int j = 0; j < nWidth - 1; ++j)
        {
            nFocus += (*pucCur - *(pucCur + 1)) * (*pucCur - *(pucCur + 1))
                + (*pucCur - *pucN) * (*pucCur - *pucN);
            pucCur++;
            pucN++;
        }
    }

    return nFocus * 0.5f / ((nWidth - 1) * (nHeight - 1));
}

float CClearness::Brenner(const uchar *pucImg, const int nWidth, const int nHeight)
{
    if (NULL == pucImg || 0 >= nWidth || 2 >= nHeight)
    {
        return 0.0f;
    }

    int nFocus = 0;
    for (int i = 0; i < nHeight - 2; ++i)
    {
        const uchar *pucCur = m_pucImgSrc + i * nWidth;
        const uchar *pucNN = pucCur + nWidth * 2;
        for (int j = 0; j < nWidth; ++j)
        {
            nFocus += (*pucCur - *pucNN) * (*pucCur - *pucNN);
        }
    }

    return nFocus * 0.4f / ((nHeight - 2) * nWidth);
}

float CClearness::VollathAbs(const uchar *pucImg, uchar *pucTmp1, uchar *pucTmp2,
                             const int nWidth, const int nHeight)
{
    if (NULL == pucImg || NULL == pucTmp1 || NULL == pucTmp2 || 0 >= nWidth || 2 >= nHeight)
    {
        return 0.0f;
    }

    Mat img(nHeight, nWidth, CV_8UC1, (void *) pucImg, nWidth);
    Mat dst(nHeight, nWidth, CV_8UC1, pucTmp1, nWidth);
    blur(img, dst, Size(9, 9));

    float fFocusOri = Vollath(pucImg, nWidth, nHeight);
    float fFocusSmooth = Vollath(pucTmp1, nWidth, nHeight);
    float fFocus = (fFocusOri - fFocusSmooth) * 100.0f / max((float) M_EPS, fFocusOri);

    if (m_para.bAdjust)
    {
        int nScale = max(1, min(nWidth, nHeight) / 32);
        int nW = nWidth / nScale, nH = nHeight / nScale;
        DownSample(pucImg, pucTmp1, nWidth, nHeight, nWidth, nScale);

        Mat tmp1(nH, nW, CV_8UC1, pucTmp1, nW);
        Mat tmp2(nH, nW, CV_8UC1, pucTmp2, nW);
        Mat elem = getStructuringElement(MORPH_RECT, Size(3, 3));
        erode(tmp1, tmp2, elem);

        const int nImgSize = nH * nW;
        int nBkg = 0;
        for (int i = 0; i < nImgSize; ++i)
        {
            if (*pucTmp2++ >= m_para.ucBkgTh)
            {
                nBkg++;
            }
        }

        fFocus += fFocus * nBkg / nImgSize;
        m_fBkg = nBkg * 1.0f / nImgSize;
    }

    return fFocus;
}

bool CClearness::IsGoodForJudge() const
{
    return m_fBkg < 0.5f;
}
