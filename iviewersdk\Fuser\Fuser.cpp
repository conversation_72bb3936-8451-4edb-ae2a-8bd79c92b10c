/**
* @date         2015-08-15
* @filename     Fuser.cpp
* @purpose      class for fusing images (gray, bayer and color images)
* @version      1.0
* @history      initial draft
* <AUTHOR> UNIC, Beijing, China
* @copyright    UNIC Technologies, 2005-2015. All rights reserved.
*/

#include "./AlgoGap.h"
#include "./Fuser.h"

class CFuser::Data
{
public:
    CAlgoGap gap;
};

CFuser::CFuser()
{
    m_pD = new Data();
}

CFuser::~CFuser()
{
    delete m_pD;
    m_pD = NULL;
}

void CFuser::GetPara(Para &para) const
{
    CAlgoGap::Para paraGap;
    m_pD->gap.GetPara(paraGap);
    memcpy(&para, &paraGap, sizeof(para));
}

int CFuser::SetPara(const Para &para)
{
    CAlgoGap::Para paraGap;
    memcpy(&paraGap, &para, sizeof(para));
    return m_pD->gap.SetPara(paraGap);
}

int CFuser::SetImage(const int nIndex, const unsigned char *pucImg, const int nWidth,
                           const int nHeight, const int nPitch, const int nLeft /* = 0 */,
                           const int nTop /* = 0 */, const int nRight /* = 0 */,
                           const int nBottom /* = 0 */)
{
    UN_IMAGE_INFO_S stImg;
    stImg.pucBuffer = (uchar *) pucImg;
    stImg.nWidth = nWidth;
    stImg.nHeight = nHeight;
    stImg.nPitch = nPitch;
    stImg.nLeft = nLeft;
    stImg.nTop = nTop;
    stImg.nRight = nRight;
    stImg.nBottom = nBottom;

    return m_pD->gap.SetImage(nIndex, &stImg);
}

int CFuser::DoInspect(const unsigned char *pucImg, const int nWidth, const int nHeight,
                            const int nPitch, const int nLeft /* = 0 */, const int nTop /* = 0 */,
                            const int nRight /* = 0 */, const int nBottom /* = 0 */)
{
    UN_IMAGE_INFO_S stImg;
    stImg.pucBuffer = (uchar *) pucImg;
    stImg.nWidth = nWidth;
    stImg.nHeight = nHeight;
    stImg.nPitch = nPitch;
    stImg.nLeft = nLeft;
    stImg.nTop = nTop;
    stImg.nRight = nRight;
    stImg.nBottom = nBottom;

    return m_pD->gap.DoInspect(&stImg);
}

void CFuser::StartFuse()
{
    return m_pD->gap.StartFuse();
}

uchar *CFuser::GetResult()
{
    return m_pD->gap.GetResult();
}
