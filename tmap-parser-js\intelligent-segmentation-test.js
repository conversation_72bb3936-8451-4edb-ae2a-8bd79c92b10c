/**
 * Intelligent Segmentation Test
 * Test all three segmentation modes: fast, balanced, accurate
 */

import { Tmap6Parser } from './src/parsers/tmap6-parser.js';
import sharp from 'sharp';

async function testSegmentationModes() {
  console.log('🧠 Intelligent Segmentation Mode Test');
  console.log('═'.repeat(60));

  const filePath = 'E:\\TMAP\\Test_1.TMAP';
  const modes = ['fast', 'balanced', 'accurate'];
  const results = {};

  for (const mode of modes) {
    console.log(`\n🔧 Testing ${mode.toUpperCase()} mode`);
    console.log('─'.repeat(40));

    try {
      // Create parser with specific mode
      const parser = new Tmap6Parser({ segmentationMode: mode });
      await parser.parseFile(filePath);

      // Get configuration info
      const config = parser.getSegmentationConfig();
      console.log(`📋 Configuration: ${config.config.description}`);
      console.log(`🔧 Algorithm: ${config.config.algorithm}`);

      // Extract images and measure performance
      const startTime = Date.now();
      
      const labelData = await parser.getLabelImage(false);
      const macroData = await parser.getMacroImage(false);
      
      const endTime = Date.now();
      const extractionTime = endTime - startTime;

      if (!labelData || !macroData) {
        throw new Error('Failed to extract images');
      }

      // Get image metadata
      const labelMetadata = await sharp(labelData).metadata();
      const macroMetadata = await sharp(macroData).metadata();
      const macroLabelMetadata = await sharp(await parser.getMacroLabelImage(false)).metadata();

      // Calculate metrics
      const totalExtracted = labelMetadata.width + macroMetadata.width;
      const pixelDifference = Math.abs(totalExtracted - macroLabelMetadata.width);
      const accuracy = ((macroLabelMetadata.width - pixelDifference) / macroLabelMetadata.width) * 100;
      const labelRatio = (labelMetadata.width / macroLabelMetadata.width) * 100;
      const macroRatio = (macroMetadata.width / macroLabelMetadata.width) * 100;

      results[mode] = {
        extractionTime,
        labelSize: labelData.length,
        macroSize: macroData.length,
        labelDimensions: `${labelMetadata.width}x${labelMetadata.height}`,
        macroDimensions: `${macroMetadata.width}x${macroMetadata.height}`,
        totalExtracted,
        pixelDifference,
        accuracy,
        labelRatio,
        macroRatio,
        algorithm: config.config.algorithm
      };

      // Print results
      console.log(`⚡ Extraction time: ${extractionTime}ms`);
      console.log(`📐 Label: ${labelMetadata.width}x${labelMetadata.height} (${(labelData.length / 1024).toFixed(1)} kB)`);
      console.log(`📐 Macro: ${macroMetadata.width}x${macroMetadata.height} (${(macroData.length / 1024).toFixed(1)} kB)`);
      console.log(`🎯 Accuracy: ${accuracy.toFixed(2)}% (${pixelDifference} pixel difference)`);
      console.log(`📊 Ratios: Label ${labelRatio.toFixed(1)}%, Macro ${macroRatio.toFixed(1)}%`);

      if (accuracy >= 99.5) {
        console.log(`✅ Excellent accuracy`);
      } else if (accuracy >= 95) {
        console.log(`✅ Good accuracy`);
      } else if (accuracy >= 90) {
        console.log(`⚠️  Acceptable accuracy`);
      } else {
        console.log(`❌ Poor accuracy`);
      }

    } catch (error) {
      console.log(`❌ ${mode.toUpperCase()} mode failed: ${error.message}`);
      results[mode] = { error: error.message };
    }
  }

  // Print comparison
  console.log('\n📊 Mode Comparison');
  console.log('═'.repeat(60));

  const successfulModes = Object.keys(results).filter(mode => !results[mode].error);
  
  if (successfulModes.length === 0) {
    console.log('❌ All modes failed');
    return;
  }

  // Performance comparison
  console.log('\n⚡ Performance Comparison:');
  console.log('─'.repeat(30));
  successfulModes.forEach(mode => {
    const result = results[mode];
    console.log(`${mode.toUpperCase().padEnd(8)}: ${result.extractionTime.toString().padStart(3)}ms (${result.algorithm})`);
  });

  // Find fastest and slowest
  const times = successfulModes.map(mode => ({ mode, time: results[mode].extractionTime }));
  times.sort((a, b) => a.time - b.time);
  const fastest = times[0];
  const slowest = times[times.length - 1];
  const speedup = slowest.time / fastest.time;

  console.log(`\n🏆 Fastest: ${fastest.mode.toUpperCase()} (${fastest.time}ms)`);
  console.log(`🐌 Slowest: ${slowest.mode.toUpperCase()} (${slowest.time}ms)`);
  console.log(`📈 Speed difference: ${speedup.toFixed(1)}x`);

  // Accuracy comparison
  console.log('\n🎯 Accuracy Comparison:');
  console.log('─'.repeat(30));
  successfulModes.forEach(mode => {
    const result = results[mode];
    console.log(`${mode.toUpperCase().padEnd(8)}: ${result.accuracy.toFixed(2)}% (±${result.pixelDifference} pixels)`);
  });

  // Find most and least accurate
  const accuracies = successfulModes.map(mode => ({ mode, accuracy: results[mode].accuracy }));
  accuracies.sort((a, b) => b.accuracy - a.accuracy);
  const mostAccurate = accuracies[0];
  const leastAccurate = accuracies[accuracies.length - 1];

  console.log(`\n🎯 Most accurate: ${mostAccurate.mode.toUpperCase()} (${mostAccurate.accuracy.toFixed(2)}%)`);
  console.log(`📉 Least accurate: ${leastAccurate.mode.toUpperCase()} (${leastAccurate.accuracy.toFixed(2)}%)`);

  // Size comparison
  console.log('\n💾 File Size Comparison:');
  console.log('─'.repeat(30));
  successfulModes.forEach(mode => {
    const result = results[mode];
    const totalSize = (result.labelSize + result.macroSize) / 1024;
    console.log(`${mode.toUpperCase().padEnd(8)}: ${totalSize.toFixed(1)} kB (Label: ${(result.labelSize / 1024).toFixed(1)} kB, Macro: ${(result.macroSize / 1024).toFixed(1)} kB)`);
  });

  // Ratio analysis
  console.log('\n📊 Segmentation Ratio Analysis:');
  console.log('─'.repeat(30));
  successfulModes.forEach(mode => {
    const result = results[mode];
    console.log(`${mode.toUpperCase().padEnd(8)}: Label ${result.labelRatio.toFixed(1)}%, Macro ${result.macroRatio.toFixed(1)}%`);
  });

  // Recommendations
  console.log('\n🏆 Recommendations:');
  console.log('─'.repeat(30));

  // Find best for different use cases
  const bestSpeed = times[0].mode;
  const bestAccuracy = accuracies[0].mode;
  
  console.log(`⚡ For speed: ${bestSpeed.toUpperCase()} mode (${results[bestSpeed].extractionTime}ms)`);
  console.log(`🎯 For accuracy: ${bestAccuracy.toUpperCase()} mode (${results[bestAccuracy].accuracy.toFixed(2)}%)`);
  
  // Find balanced option
  const balancedScores = successfulModes.map(mode => {
    const result = results[mode];
    const speedScore = (slowest.time - result.extractionTime) / (slowest.time - fastest.time);
    const accuracyScore = (result.accuracy - leastAccurate.accuracy) / (mostAccurate.accuracy - leastAccurate.accuracy);
    const balancedScore = (speedScore + accuracyScore) / 2;
    return { mode, score: balancedScore };
  });
  
  balancedScores.sort((a, b) => b.score - a.score);
  const bestBalanced = balancedScores[0].mode;
  
  console.log(`⚖️  For balance: ${bestBalanced.toUpperCase()} mode (best speed/accuracy trade-off)`);

  // Overall recommendation
  console.log('\n💡 Overall Recommendation:');
  console.log('─'.repeat(30));
  
  if (results.balanced && !results.balanced.error) {
    console.log('✅ Use BALANCED mode for most applications');
    console.log('   - Intelligent algorithm selection');
    console.log('   - Good performance and accuracy balance');
    console.log('   - Automatic fallback protection');
  } else if (results.accurate && !results.accurate.error) {
    console.log('✅ Use ACCURATE mode for critical applications');
    console.log('   - Maximum accuracy and precision');
    console.log('   - Suitable for research and analysis');
  } else if (results.fast && !results.fast.error) {
    console.log('✅ Use FAST mode for batch processing');
    console.log('   - Maximum speed for large datasets');
    console.log('   - Acceptable accuracy for most use cases');
  }

  console.log('\n✨ Test completed successfully!');
}

// Run the test
runSegmentationTest().catch(console.error);

async function runSegmentationTest() {
  try {
    await testSegmentationModes();
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}
