/**
* @date         2014-07-16
* @filename     Clearness.h
* @purpose      example class for using CAlgoInterface class
* @version      1.0
* @history      initial draft
* <AUTHOR> UNIC, Beijing, China
* @copyright    UNIC Technologies, 2005-2014. All rights reserved.
*/

#ifndef __CLEARNESS_H__
#define __CLEARNESS_H__

#ifdef CLEARNESS_EXPORTS
#define CLEARNESS_API __declspec(dllexport)
class CAlgoInterface;
#else
#define CLEARNESS_API __declspec(dllimport)
#include "./AlgoInterface.h"
#endif

#pragma warning(disable:4275)
#pragma warning(disable:4251)

class CLEARNESS_API CClearness : public CAlgoInterface
{
 public:
    CClearness(void);

    virtual ~CClearness(void);

    // pack parameters for UI
    virtual int PackPara(void);

    // unpack parameters from UI
    virtual int UnpackPara(void);

    // set extra images (like template or something)
    virtual int SetImage(const int nIndex, const UN_IMAGE_INFO_S *pstImg);

    // do the main job
    virtual int DoInspect(const UN_IMAGE_INFO_S *pstImg);

    // pack results for displaying in text
    virtual int PackResult(void);

    // pack elements for display on image
    virtual int PackDisplay(void);

    enum
    {
        FOCUS_VOLLATH = 0,
        FOCUS_TENENGRAD,
        FOCUS_BRENNER,
        FOCUS_BLUR_METRIC,
        FOCUS_UNIC,

        FOCUS_VOLLATH_ABS,

        FOCUS_ALL
    };

    enum
    {
        COLOR_GRAY = 0,
        COLOR_GRGB,
        COLOR_GBGR,
        COLOR_RGBG,
        COLOR_BGRG,
        COLOR_TRUE
    };

    // parameters structure for this algorithm
    struct Para
    {
        int nFocusType;
        int nColorType; // valid for FOCUS_UNIC
        int nScale; // valid for FOCUS_UNIC
        bool bUseColor; // valid for FOCUS_UNIC
        bool bAdjust; // adjust focus, valid for FOCUS_VOLLATH_ABS
        uchar ucBkgTh; // valid for FOCUS_VOLLATH_ABS
        Para()
        {
            nFocusType = FOCUS_VOLLATH;
            nColorType = COLOR_GRAY;
            nScale = 2;
            bUseColor = false;
            bAdjust = false;
            ucBkgTh = 200;
        }
    };

    // do the main job using this input image
    int DoInspect(const unsigned char *pucImg, const int nWidth, const int nHeight,
        const int nPitch, const int nLeft = 0, const int nTop = 0,
        const int nRight = 0, const int nBottom = 0);

    void GetPara(Para &para) const;
    int SetPara(const Para &para);
    float GetFocus(void) const;
    float GetColorScore(void) const;
    bool IsGoodForJudge(void) const;

 private:
    int CheckImageInfo(const UN_IMAGE_INFO_S *pstImg) const;

    int SetImage(const UN_IMAGE_INFO_S *pstImg, const bool bTemplate); //lint !e1411

    float Vollath(const uchar *pucImg, const int nWidth, const int nHeight);

    float Tenengrad(const uchar *pucImg, const int nWidth, const int nHeight);

    float Brenner(const uchar *pucImg, const int nWidth, const int nHeight);

    float BlurMetric(const uchar *pucImg, uchar *pucH, uchar *pucV,
        const int nWidth, const int nHeight);

    float FocusUnic(const uchar *pucImg, uchar *pucGray, uchar *pucColor,
        const int nWidth, const int nHeight, const int nColor,
        const bool bUseColor, const int nScale);

	float FluorScoring(unsigned char* pfImage, int nWidth, int nHeight, int nDepth, float* pfscore1 = NULL, float* pfscore2 = NULL);

    float FocusUnicGray(const uchar *pucImg, const int nWidth,
        const int nHeight, const int nScale);

    float FocusUnicColor(const uchar *pucImg, uchar *pucGray, uchar *pucColor,
        const int nWidth, const int nHeight, const int nChannels,
        const int nScale, float &fScore);

    float FocusUnicBayer(const uchar *pucImg, uchar *pucGray, uchar *pucColor,
        const int nWidth, const int nHeight, const int nColor,
        const bool bUseColor, float &fScore);

	float Tenengrad(const uchar *pucImg, uchar *pucGray, uchar *pucColor,
		const int nWidth, const int nHeight, const int nColor,
		const bool bUseColor, float &fScore);
	
    float VollathAbs(const uchar *pucImg, uchar *pucTmp1, uchar *pucTmp2,
        const int nWidth, const int nHeight);

    // make it private to prevent copying
    CClearness(const CClearness &other); //lint !e1704

    // make it private to prevent copying
    CClearness &operator=(const CClearness &other);

    uchar *m_pucImgSrc;         // save gray image of source image
    uchar *m_pucImgH;           // h image
    uchar *m_pucImgV;           // v image
    uchar *m_pucImgTpl;         // template image
    bool m_bHasTemplate;        // has template or not

    int m_nWidth;               // image width
    int m_nHeight;              // image height
    int m_nChannels;            // image channel
    int m_nImgSize;             // size of image
    int m_nLeft;                // left of ROI
    int m_nTop;                 // top of ROI

    Para m_para;                // parameters
    float m_fFocus, m_fScore, m_fBkg;
};

#endif // __CLEARNESS_H__
