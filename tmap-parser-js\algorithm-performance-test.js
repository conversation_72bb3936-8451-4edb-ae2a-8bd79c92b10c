/**
 * Algorithm Performance Comparison Test
 * Compare Simple Algorithm vs OpenCV.js for TMAP image segmentation
 */

import { Tmap6Parser } from './src/parsers/tmap6-parser.js';
import { 
  extractLabelRegion as opencvExtractLabel, 
  extractMacroRegion as opencvExtractMacro 
} from './src/utils/opencv-segmentation.js';
import sharp from 'sharp';

class PerformanceTester {
  constructor() {
    this.results = {
      simple: { times: [], accuracy: [], memory: [] },
      opencv: { times: [], accuracy: [], memory: [] }
    };
  }

  /**
   * Simple algorithm implementation (fallback method)
   */
  async simpleExtractLabel(imageBuffer, width, height, channels) {
    const startTime = performance.now();
    const startMemory = process.memoryUsage().heapUsed;

    try {
      // Simple 2/3 split algorithm
      const labelWidth = Math.floor(width * 2 / 3);
      const labelBuffer = Buffer.alloc(labelWidth * height * channels);

      for (let y = 0; y < height; y++) {
        const srcOffset = y * width * channels;
        const dstOffset = y * labelWidth * channels;
        const copyLength = labelWidth * channels;
        imageBuffer.copy(labelBuffer, dstOffset, srcOffset, srcOffset + copyLength);
      }

      const endTime = performance.now();
      const endMemory = process.memoryUsage().heapUsed;

      return {
        buffer: labelBuffer,
        width: labelWidth,
        height: height,
        processingTime: endTime - startTime,
        memoryUsed: endMemory - startMemory
      };
    } catch (error) {
      throw new Error(`Simple extraction failed: ${error.message}`);
    }
  }

  async simpleExtractMacro(imageBuffer, width, height, channels) {
    const startTime = performance.now();
    const startMemory = process.memoryUsage().heapUsed;

    try {
      // Simple 1/3 split algorithm
      const macroLeft = Math.floor(width * 2 / 3);
      const macroWidth = width - macroLeft;
      const macroBuffer = Buffer.alloc(macroWidth * height * channels);

      for (let y = 0; y < height; y++) {
        const srcOffset = y * width * channels + macroLeft * channels;
        const dstOffset = y * macroWidth * channels;
        const copyLength = macroWidth * channels;
        imageBuffer.copy(macroBuffer, dstOffset, srcOffset, srcOffset + copyLength);
      }

      const endTime = performance.now();
      const endMemory = process.memoryUsage().heapUsed;

      return {
        buffer: macroBuffer,
        width: macroWidth,
        height: height,
        processingTime: endTime - startTime,
        memoryUsed: endMemory - startMemory
      };
    } catch (error) {
      throw new Error(`Simple extraction failed: ${error.message}`);
    }
  }

  /**
   * OpenCV algorithm wrapper
   */
  async opencvExtractLabel(imageBuffer, width, height, channels) {
    const startTime = performance.now();
    const startMemory = process.memoryUsage().heapUsed;

    try {
      const result = await opencvExtractLabel(imageBuffer, width, height, channels);
      
      const endTime = performance.now();
      const endMemory = process.memoryUsage().heapUsed;

      return {
        ...result,
        processingTime: endTime - startTime,
        memoryUsed: endMemory - startMemory
      };
    } catch (error) {
      throw new Error(`OpenCV extraction failed: ${error.message}`);
    }
  }

  async opencvExtractMacro(imageBuffer, width, height, channels) {
    const startTime = performance.now();
    const startMemory = process.memoryUsage().heapUsed;

    try {
      const result = await opencvExtractMacro(imageBuffer, width, height, channels);
      
      const endTime = performance.now();
      const endMemory = process.memoryUsage().heapUsed;

      return {
        ...result,
        processingTime: endTime - startTime,
        memoryUsed: endMemory - startMemory
      };
    } catch (error) {
      throw new Error(`OpenCV extraction failed: ${error.message}`);
    }
  }

  /**
   * Calculate accuracy metrics
   */
  calculateAccuracy(labelResult, macroResult, originalWidth) {
    const totalExtracted = labelResult.width + macroResult.width;
    const pixelDifference = Math.abs(totalExtracted - originalWidth);
    const accuracy = ((originalWidth - pixelDifference) / originalWidth) * 100;
    
    return {
      pixelDifference,
      accuracy,
      coverage: (totalExtracted / originalWidth) * 100
    };
  }

  /**
   * Run performance test
   */
  async runTest(iterations = 5) {
    console.log('🔬 Algorithm Performance Comparison Test');
    console.log('═'.repeat(60));
    console.log(`Running ${iterations} iterations for each algorithm...\n`);

    const parser = new Tmap6Parser();
    const filePath = 'E:\\TMAP\\Test_1.TMAP';

    // Parse file and get test data
    await parser.parseFile(filePath);
    const macroLabelData = await parser.getMacroLabelImage(true); // Raw bitmap
    const macroLabelMetadata = await sharp(await parser.getMacroLabelImage(false)).metadata();
    
    const width = macroLabelMetadata.width;
    const height = macroLabelMetadata.height;
    const channels = macroLabelMetadata.channels;

    console.log(`📐 Test Image: ${width}x${height}, ${channels} channels\n`);

    // Test both algorithms multiple times
    for (let i = 0; i < iterations; i++) {
      console.log(`🔄 Iteration ${i + 1}/${iterations}`);
      
      // Force garbage collection before each test
      if (global.gc) global.gc();

      // Test Simple Algorithm
      try {
        const simpleLabel = await this.simpleExtractLabel(macroLabelData, width, height, channels);
        const simpleMacro = await this.simpleExtractMacro(macroLabelData, width, height, channels);
        const simpleAccuracy = this.calculateAccuracy(simpleLabel, simpleMacro, width);
        
        this.results.simple.times.push(simpleLabel.processingTime + simpleMacro.processingTime);
        this.results.simple.accuracy.push(simpleAccuracy.accuracy);
        this.results.simple.memory.push(simpleLabel.memoryUsed + simpleMacro.memoryUsed);
        
        console.log(`  Simple: ${(simpleLabel.processingTime + simpleMacro.processingTime).toFixed(2)}ms, Accuracy: ${simpleAccuracy.accuracy.toFixed(2)}%`);
      } catch (error) {
        console.log(`  Simple: FAILED - ${error.message}`);
      }

      // Test OpenCV Algorithm
      try {
        const opencvLabel = await this.opencvExtractLabel(macroLabelData, width, height, channels);
        const opencvMacro = await this.opencvExtractMacro(macroLabelData, width, height, channels);
        const opencvAccuracy = this.calculateAccuracy(opencvLabel, opencvMacro, width);
        
        this.results.opencv.times.push(opencvLabel.processingTime + opencvMacro.processingTime);
        this.results.opencv.accuracy.push(opencvAccuracy.accuracy);
        this.results.opencv.memory.push(opencvLabel.memoryUsed + opencvMacro.memoryUsed);
        
        console.log(`  OpenCV: ${(opencvLabel.processingTime + opencvMacro.processingTime).toFixed(2)}ms, Accuracy: ${opencvAccuracy.accuracy.toFixed(2)}%`);
      } catch (error) {
        console.log(`  OpenCV: FAILED - ${error.message}`);
      }
    }

    this.printResults();
  }

  /**
   * Calculate statistics
   */
  calculateStats(values) {
    if (values.length === 0) return { avg: 0, min: 0, max: 0, std: 0 };
    
    const avg = values.reduce((a, b) => a + b, 0) / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);
    const variance = values.reduce((a, b) => a + Math.pow(b - avg, 2), 0) / values.length;
    const std = Math.sqrt(variance);
    
    return { avg, min, max, std };
  }

  /**
   * Print detailed results
   */
  printResults() {
    console.log('\n📊 Performance Analysis Results');
    console.log('═'.repeat(60));

    const simpleTimeStats = this.calculateStats(this.results.simple.times);
    const opencvTimeStats = this.calculateStats(this.results.opencv.times);
    const simpleAccuracyStats = this.calculateStats(this.results.simple.accuracy);
    const opencvAccuracyStats = this.calculateStats(this.results.opencv.accuracy);
    const simpleMemoryStats = this.calculateStats(this.results.simple.memory);
    const opencvMemoryStats = this.calculateStats(this.results.opencv.memory);

    // Processing Time Comparison
    console.log('\n⏱️  Processing Time (ms)');
    console.log('─'.repeat(40));
    console.log(`Simple Algorithm:`);
    console.log(`  Average: ${simpleTimeStats.avg.toFixed(2)}ms`);
    console.log(`  Range: ${simpleTimeStats.min.toFixed(2)} - ${simpleTimeStats.max.toFixed(2)}ms`);
    console.log(`  Std Dev: ${simpleTimeStats.std.toFixed(2)}ms`);
    
    console.log(`\nOpenCV Algorithm:`);
    console.log(`  Average: ${opencvTimeStats.avg.toFixed(2)}ms`);
    console.log(`  Range: ${opencvTimeStats.min.toFixed(2)} - ${opencvTimeStats.max.toFixed(2)}ms`);
    console.log(`  Std Dev: ${opencvTimeStats.std.toFixed(2)}ms`);

    const speedImprovement = ((simpleTimeStats.avg - opencvTimeStats.avg) / simpleTimeStats.avg) * 100;
    console.log(`\n🚀 Speed Difference: ${speedImprovement > 0 ? 'OpenCV is' : 'Simple is'} ${Math.abs(speedImprovement).toFixed(1)}% faster`);

    // Accuracy Comparison
    console.log('\n🎯 Accuracy (%)');
    console.log('─'.repeat(40));
    console.log(`Simple Algorithm: ${simpleAccuracyStats.avg.toFixed(2)}% ± ${simpleAccuracyStats.std.toFixed(2)}%`);
    console.log(`OpenCV Algorithm: ${opencvAccuracyStats.avg.toFixed(2)}% ± ${opencvAccuracyStats.std.toFixed(2)}%`);
    
    const accuracyImprovement = opencvAccuracyStats.avg - simpleAccuracyStats.avg;
    console.log(`\n🎯 Accuracy Difference: OpenCV is ${accuracyImprovement.toFixed(2)}% more accurate`);

    // Memory Usage Comparison
    console.log('\n💾 Memory Usage (bytes)');
    console.log('─'.repeat(40));
    console.log(`Simple Algorithm: ${(simpleMemoryStats.avg / 1024).toFixed(1)} KB ± ${(simpleMemoryStats.std / 1024).toFixed(1)} KB`);
    console.log(`OpenCV Algorithm: ${(opencvMemoryStats.avg / 1024).toFixed(1)} KB ± ${(opencvMemoryStats.std / 1024).toFixed(1)} KB`);

    // Overall Recommendation
    console.log('\n🏆 Overall Assessment');
    console.log('─'.repeat(40));
    
    if (opencvAccuracyStats.avg > simpleAccuracyStats.avg + 1) {
      console.log('✅ OpenCV provides significantly better accuracy');
    } else {
      console.log('⚖️  Both algorithms have similar accuracy');
    }
    
    if (Math.abs(speedImprovement) < 10) {
      console.log('⚖️  Performance difference is minimal');
    } else if (speedImprovement > 0) {
      console.log('🚀 OpenCV is significantly faster');
    } else {
      console.log('🐌 Simple algorithm is faster');
    }

    console.log('\n📋 Recommendation:');
    if (opencvAccuracyStats.avg > simpleAccuracyStats.avg + 1) {
      console.log('🎯 Use OpenCV for better accuracy');
    } else if (speedImprovement < -20) {
      console.log('⚡ Use Simple algorithm for better performance');
    } else {
      console.log('🔄 Both algorithms are viable, OpenCV recommended for consistency');
    }
  }
}

// Run the test
async function runPerformanceTest() {
  const tester = new PerformanceTester();
  
  try {
    await tester.runTest(5); // Run 5 iterations
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Enable garbage collection for memory testing
if (typeof global.gc === 'undefined') {
  console.log('💡 Tip: Run with --expose-gc flag for better memory analysis');
}

runPerformanceTest().catch(console.error);
