/**
 * OpenCV.js based image segmentation for TMAP label/macro separation
 * This module provides more accurate boundary detection using OpenCV algorithms
 */

import cv from 'opencv.js';

/**
 * Initialize OpenCV.js
 * @returns {Promise<boolean>} True if initialization successful
 */
export async function initializeOpenCV() {
  try {
    // OpenCV.js should be loaded already, just check if it's available
    if (typeof cv !== 'undefined' && cv.Mat) {
      console.log('OpenCV.js initialized successfully');
      return true;
    } else {
      console.error('OpenCV.js not available');
      return false;
    }
  } catch (error) {
    console.error('Failed to initialize OpenCV.js:', error.message);
    return false;
  }
}

/**
 * Convert Buffer to OpenCV Mat
 * @param {Buffer} buffer - Image buffer
 * @param {number} width - Image width
 * @param {number} height - Image height
 * @param {number} channels - Number of channels (1 or 3)
 * @returns {cv.Mat} OpenCV Mat object
 */
function bufferToMat(buffer, width, height, channels) {
  const mat = new cv.Mat(height, width, channels === 3 ? cv.CV_8UC3 : cv.CV_8UC1);
  mat.data.set(buffer);
  return mat;
}

/**
 * Convert OpenCV Mat to Buffer
 * @param {cv.Mat} mat - OpenCV Mat object
 * @returns {Buffer} Image buffer
 */
function matToBuffer(mat) {
  return Buffer.from(mat.data);
}

/**
 * Find boundary between label and macro regions using OpenCV
 * @param {Buffer} imageBuffer - Raw image data
 * @param {number} width - Image width
 * @param {number} height - Image height
 * @param {number} channels - Number of channels
 * @returns {Promise<object>} Boundary information
 */
export async function findLabelMacroBoundary(imageBuffer, width, height, channels) {
  try {
    // Convert buffer to OpenCV Mat
    const src = bufferToMat(imageBuffer, width, height, channels);
    
    // Convert to grayscale if needed
    let gray;
    if (channels === 3) {
      gray = new cv.Mat();
      cv.cvtColor(src, gray, cv.COLOR_RGB2GRAY);
    } else {
      gray = src.clone();
    }
    
    // Downsample for analysis (scale factor 4, like iViewerSDK)
    const scale = 4;
    const subWidth = Math.floor(width / scale);
    const subHeight = Math.floor(height / scale);
    
    const resized = new cv.Mat();
    cv.resize(gray, resized, new cv.Size(subWidth, subHeight), 0, 0, cv.INTER_AREA);
    
    // Calculate horizontal gradients using Sobel operator
    const gradX = new cv.Mat();
    cv.Sobel(resized, gradX, cv.CV_32F, 1, 0, 3);
    
    // Convert to absolute values
    const absGradX = new cv.Mat();
    cv.convertScaleAbs(gradX, absGradX);
    
    // Calculate column projections
    const colProjections = new Array(subWidth).fill(0);
    const startCol = Math.max(8, Math.floor(subWidth / 8));
    const endCol = subWidth - startCol;
    const startRow = Math.floor(subHeight / 8);
    const endRow = subHeight - startRow;
    
    // Sum gradients in each column (middle region only)
    for (let x = startCol; x < endCol; x++) {
      for (let y = startRow; y < endRow; y++) {
        const value = absGradX.ucharPtr(y, x)[0];
        colProjections[x] += value;
      }
    }
    
    // Apply smoothing to projections (simple moving average)
    const smoothedProjections = smoothArray(colProjections, 3);
    
    // Find the maximum gradient (boundary)
    let maxGradient = 0;
    let boundaryIndex = Math.floor(subWidth / 2);
    
    for (let x = startCol; x < endCol; x++) {
      if (smoothedProjections[x] > maxGradient) {
        maxGradient = smoothedProjections[x];
        boundaryIndex = x;
      }
    }
    
    // Determine if label is on left or right
    let leftSum = 0, rightSum = 0;
    const thirdWidth = Math.floor(subWidth / 3);
    
    for (let i = 0; i < thirdWidth; i++) {
      leftSum += smoothedProjections[i] || 0;
      rightSum += smoothedProjections[subWidth - i - 1] || 0;
    }
    
    const labelOnLeft = leftSum > rightSum;
    
    // Calculate boundary in original scale
    const boundaryPixel = boundaryIndex * scale;
    
    // Clean up OpenCV objects
    src.delete();
    if (gray !== src) gray.delete();
    resized.delete();
    gradX.delete();
    absGradX.delete();
    
    return {
      boundaryPixel,
      labelOnLeft,
      confidence: maxGradient / (width * height / 1000), // Normalized confidence
      projections: smoothedProjections
    };
    
  } catch (error) {
    console.error('OpenCV boundary detection failed:', error.message);
    throw error;
  }
}

/**
 * Extract label region using OpenCV-detected boundary
 * @param {Buffer} imageBuffer - Raw image data
 * @param {number} width - Image width
 * @param {number} height - Image height
 * @param {number} channels - Number of channels
 * @returns {Promise<object>} Label region data
 */
export async function extractLabelRegion(imageBuffer, width, height, channels) {
  try {
    const boundary = await findLabelMacroBoundary(imageBuffer, width, height, channels);
    
    let labelLeft, labelRight;
    
    if (boundary.labelOnLeft) {
      // Label is on left side
      labelLeft = 0;
      labelRight = boundary.boundaryPixel;
    } else {
      // Label is on right side
      labelLeft = boundary.boundaryPixel;
      labelRight = width;
    }
    
    const labelWidth = labelRight - labelLeft;
    
    if (labelWidth <= 0) {
      throw new Error('Invalid label region detected');
    }
    
    // Extract the region
    const labelBuffer = Buffer.alloc(labelWidth * height * channels);
    
    for (let y = 0; y < height; y++) {
      const srcOffset = y * width * channels + labelLeft * channels;
      const dstOffset = y * labelWidth * channels;
      const copyLength = labelWidth * channels;
      
      imageBuffer.copy(labelBuffer, dstOffset, srcOffset, srcOffset + copyLength);
    }
    
    return {
      buffer: labelBuffer,
      width: labelWidth,
      height: height,
      boundary: boundary.boundaryPixel,
      confidence: boundary.confidence
    };
    
  } catch (error) {
    console.error('Label extraction failed:', error.message);
    throw error;
  }
}

/**
 * Extract macro region using OpenCV-detected boundary
 * @param {Buffer} imageBuffer - Raw image data
 * @param {number} width - Image width
 * @param {number} height - Image height
 * @param {number} channels - Number of channels
 * @returns {Promise<object>} Macro region data
 */
export async function extractMacroRegion(imageBuffer, width, height, channels) {
  try {
    const boundary = await findLabelMacroBoundary(imageBuffer, width, height, channels);
    
    let macroLeft, macroRight;
    
    if (boundary.labelOnLeft) {
      // Label is on left, macro is on right
      macroLeft = boundary.boundaryPixel;
      macroRight = width;
    } else {
      // Label is on right, macro is on left
      macroLeft = 0;
      macroRight = boundary.boundaryPixel;
    }
    
    const macroWidth = macroRight - macroLeft;
    
    if (macroWidth <= 0) {
      throw new Error('Invalid macro region detected');
    }
    
    // Extract the region
    const macroBuffer = Buffer.alloc(macroWidth * height * channels);
    
    for (let y = 0; y < height; y++) {
      const srcOffset = y * width * channels + macroLeft * channels;
      const dstOffset = y * macroWidth * channels;
      const copyLength = macroWidth * channels;
      
      imageBuffer.copy(macroBuffer, dstOffset, srcOffset, srcOffset + copyLength);
    }
    
    return {
      buffer: macroBuffer,
      width: macroWidth,
      height: height,
      boundary: boundary.boundaryPixel,
      confidence: boundary.confidence
    };
    
  } catch (error) {
    console.error('Macro extraction failed:', error.message);
    throw error;
  }
}

/**
 * Smooth array using moving average
 * @param {number[]} array - Input array
 * @param {number} windowSize - Window size for smoothing
 * @returns {number[]} Smoothed array
 */
function smoothArray(array, windowSize = 3) {
  const smoothed = new Array(array.length);
  const halfWindow = Math.floor(windowSize / 2);
  
  for (let i = 0; i < array.length; i++) {
    let sum = 0;
    let count = 0;
    
    for (let j = Math.max(0, i - halfWindow); j <= Math.min(array.length - 1, i + halfWindow); j++) {
      sum += array[j] || 0;
      count++;
    }
    
    smoothed[i] = count > 0 ? sum / count : 0;
  }
  
  return smoothed;
}
