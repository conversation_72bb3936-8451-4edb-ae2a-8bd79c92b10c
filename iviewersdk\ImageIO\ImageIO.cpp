/**
* @date         2012-05-28
* @filename     ImageIO.cpp
* @purpose      class for loading and saving image
* @version      2.0
* @history      initial draft
* <AUTHOR> UNIC Tech, Beijing, China
* @copyright    UNIC Technologies, 2005-2012. All rights reserved.
*/

#include <opencv2/opencv.hpp>
#include <algorithm>
#include <string>
#include <stdio.h>
#include <direct.h>
#include <io.h>

#include "./ImageIO.h"
//#include "../License/License.h"

using namespace cv;
using namespace std;

static string g_strExt[CImageIO::ALGO_ALL] =
{
    ".JPG",
    ".JP2"
};

int MakeDir(const char *pcPath)
{
    if (NULL == pcPath || 0 >= strlen(pcPath))
    {
        return 0;
    }

    char *pcDir = _strdup(pcPath);
    const int nLen = strlen(pcDir);

    // make internal directories
    for (int i = 0; i < nLen; i++)
    {
        if ('\\' == pcDir[i] || '/' == pcDir[i])
        {
            pcDir[i] = '\0';

            // create if not exist
            int nRv = _access(pcDir, 0);
            if (0 != nRv)
            {
                nRv = _mkdir(pcDir);
                if (0 != nRv)
                {
                    return -1;
                }
            }

            // change all \\ to /
            pcDir[i] = '/';
        }
    }

    free(pcDir);
    return 0;
}

/**
* @method       CImageIO::CImageIO
* @access       public
* @brief        construct function
* @param        void
* @return
* <AUTHOR> Lei, <EMAIL>
* @date         2012-05-28
* @history      initial draft
*/
CImageIO::CImageIO(void)
{
    m_pImg = NULL;
}

/**
* @method       CImageIO::~CImageIO
* @access       public
* @brief        destruct function
* @param        void
* @return
* <AUTHOR> Lei, <EMAIL>
* @date         2012-05-28
* @history      initial draft
*/
CImageIO::~CImageIO(void)
{
    if (m_pImg != NULL)
    {
        IplImage *pImg = (IplImage *) m_pImg;
        cvReleaseImage(&pImg);
        m_pImg = NULL;
    }
}

/**
* @method       CImageIO::ReadImage
* @access       public
* @brief        read image
* @param        const char * sImagePath
* @param        bool bLoadAsGray
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2012-05-28
* @history      initial draft
*/
bool CImageIO::ReadImage(const char *sImagePath, bool bLoadAsGray /* = false */)
{
#if 0
    // check time
    if (IsExpired(2015, 6, 6))
    {
        return false;
    }
#endif

    if (sImagePath == NULL)
    {
        return false;
    }

    IplImage *pImg = (IplImage *) m_pImg;
    // release memory first
    if (m_pImg != NULL)
    {
        cvReleaseImage(&pImg);
        m_pImg = NULL;
    }

    if (bLoadAsGray)
    {
        pImg = cvLoadImage(sImagePath, CV_LOAD_IMAGE_GRAYSCALE);
    }
    else
    {
        pImg = cvLoadImage(sImagePath, CV_LOAD_IMAGE_UNCHANGED);
    }
    m_pImg = pImg;

    return (pImg != NULL);
}

/**
* @method       CImageIO::SaveImage
* @access       public static
* @brief        save image
* @param        const char * pcImagePath
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2012-05-28
* @history      initial draft
*/
bool CImageIO::SaveImage(const char *pcImagePath)
{
#if 0
    // check time
    if (IsExpired(2015, 6, 6))
    {
        return false;
    }
#endif

    if (pcImagePath == NULL)
    {
        return false;
    }
    int nRv = MakeDir(pcImagePath);
    if (0 != nRv)
    {
        return false;
    }

    IplImage *pImg = (IplImage *) m_pImg;
    if (m_pImg == NULL)
    {
        return false;
    }

    int rv = cvSaveImage(pcImagePath, pImg);

    return (rv == 0);
}

/**
* @method       CImageIO::SaveImage
* @access       public
* @brief        save image
* @param        const char * pcImagePath
* @param        const uchar * pucBuffer
* @param        const int nWidth
* @param        const int nHeight
* @param        const int nPitch
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2012-05-28
* @history      initial draft
*/
bool CImageIO::SaveImage(const char *pcImagePath, const uchar *pucBuffer,
                         const int nWidth, const int nHeight, const int nPitch)
{
#if 0
    // check time
    if (IsExpired(2015, 6, 6))
    {
        return false;
    }
#endif

    const int nChannel = nPitch / max(1, nWidth);
    if (pcImagePath == NULL || pucBuffer == NULL
        || nChannel < 1 || nChannel > 4)
    {
        return false;
    }
    int nRv = MakeDir(pcImagePath);
    if (0 != nRv)
    {
        return false;
    }

    IplImage *iplImg = cvCreateImage(cvSize(nWidth, nHeight), 8, nChannel);
    if (iplImg == NULL)
    {
        return false;
    }
    uchar *pImage = (uchar *) iplImg->imageData;
    for (int i = 0; i < iplImg->height; i++)
    {
        memcpy(pImage + i * iplImg->widthStep, pucBuffer + i * nPitch,
            sizeof(uchar) * (uint) (nPitch));
    }
    int rv = cvSaveImage(pcImagePath, iplImg);
    cvReleaseImage(&iplImg);

    return (rv != 0);
}

/**
* @method       CImageIO::GetBuffer
* @access       public
* @brief        get image buffer
* @param        void
* @return       uchar *
* <AUTHOR> Lei, <EMAIL>
* @date         2012-05-28
* @history      initial draft
*/
uchar *CImageIO::GetBuffer(void)
{
    IplImage *pImg = (IplImage *) m_pImg;
    if (pImg == NULL)
    {
        return NULL;
    }

    return (uchar *) pImg->imageData;
}

/**
* @method       CImageIO::GetWidth
* @access       public
* @brief        get image width
* @param        void
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-05-28
* @history      initial draft
*/
int CImageIO::GetWidth(void) const
{
    IplImage *pImg = (IplImage *) m_pImg;
    if (pImg == NULL)
    {
        return 0;
    }

    return pImg->width;
}

/**
* @method       CImageIO::GetHeight
* @access       public
* @brief        get image height
* @param        void
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-05-28
* @history      initial draft
*/
int CImageIO::GetHeight(void) const
{
    IplImage *pImg = (IplImage *) m_pImg;
    if (pImg == NULL)
    {
        return 0;
    }

    return pImg->height;
}

/**
* @method       CImageIO::GetPitch
* @access       public
* @brief        get image width step
* @param        void
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-05-28
* @history      initial draft
*/
int CImageIO::GetPitch(void) const
{
    IplImage *pImg = (IplImage *) m_pImg;
    if (pImg == NULL)
    {
        return 0;
    }

    return pImg->widthStep;
}

/**
* @method       CImageIO::GetBpp
* @access       public
* @brief        get bpp
* @param        void
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-05-28
* @history      initial draft
*/
int CImageIO::GetBpp(void) const
{
    IplImage *pImg = (IplImage *) m_pImg;
    if (pImg == NULL)
    {
        return 0;
    }

    return pImg->nChannels * 8;
}

/**
* @method       CImageIO::Resize
* @access       public
* @brief        resize image
* @param        const int nWidth
* @param        const int nHeight
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2012-05-28
* @history      initial draft
*/
bool CImageIO::Resize(const int nWidth, const int nHeight, const INTER_E eType/* = INTER_LINEAR*/)
{
    IplImage *pImg = (IplImage *) m_pImg;
    if (pImg == NULL)
    {
        return false;
    }
    if (nWidth <= 0 || nHeight <= 0)
    {
        return false;
    }
    if (nWidth == pImg->width && nHeight == pImg->height)
    {
        return true;
    }
    IplImage *pResize = cvCreateImage(cvSize(nWidth, nHeight), pImg->depth, pImg->nChannels);
    if (pResize == NULL)
    {
        return false;
    }
    cvResize(pImg, pResize, eType % INTER_ALL);
    cvReleaseImage(&pImg);
    m_pImg = pResize;

    return true;
}

/**
* @method       CImageIO::Display
* @access       public static
* @brief        display image
* @param        void
* @return       void
* <AUTHOR> Lei, <EMAIL>
* @date         2012-05-28
* @history      initial draft
*/
void CImageIO::Display(void) const
{
    IplImage *pImg = (IplImage *) m_pImg;
    if (pImg == NULL)
    {
        return;
    }
    (void) cvNamedWindow("Image", CV_WINDOW_AUTOSIZE);
    cvShowImage("Image", pImg);
    (void) cvWaitKey(0);
    cvDestroyWindow("Image");
}

/**
* @method       CImageIO::Display
* @access       public
* @brief        display image
* @param        const uchar * pucImg
* @param        const int nWidth
* @param        const int nHeight
* @param        const int nPitch
* @return       void
* <AUTHOR> Lei, <EMAIL>
* @date         2012-05-28
* @history      initial draft
*/
void CImageIO::Display(const uchar *pucImg, const int nWidth,
                       const int nHeight, const int nPitch)
{
    const int nChannel = nPitch / max(1, nWidth);
    if (pucImg == NULL || nChannel < 1 || nChannel > 4)
    {
        return;
    }
    IplImage *iplImg = cvCreateImage(cvSize(nWidth, nHeight), 8, nChannel);
    if (iplImg == NULL)
    {
        return;
    }
    uchar *pImage = (uchar *) iplImg->imageData;
    for (int i = 0; i < iplImg->height; i++)
    {
        memcpy(pImage + i * iplImg->widthStep, pucImg + i * nPitch,
            sizeof(uchar) * (uint) (nPitch));
    }
    (void) cvNamedWindow("Image", CV_WINDOW_AUTOSIZE);
    cvShowImage("Image", iplImg);
    (void) cvWaitKey(0);
    cvDestroyWindow("Image");
    cvReleaseImage(&iplImg);
}

bool CImageIO::Decode(const uchar *pucJpeg, const uint unLength,
                      uchar *pucData, const int nWidth, const int nHeight, const int nPitch)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucJpeg || NULL == pucData || 0 >= unLength
        || (1 != nChannels && 3 != nChannels))
    {
        return false;
    }

    Mat img_decode(nHeight, nWidth, CV_8UC(nChannels), pucData, nPitch);
    Mat buf(1, unLength, CV_8UC1, (void *) pucJpeg, unLength);
	

  // imdecode(buf, CV_LOAD_IMAGE_COLOR, &dst);

	vector<uchar> data;
	for (int i = 0; i < unLength; ++i) {
		data.push_back(pucJpeg[i]);
	}
	img_decode = cv::imdecode(data, CV_LOAD_IMAGE_COLOR);
	cv::flip(img_decode, img_decode, -1);

    return true;
}

bool CImageIO::Encode(const uchar *pucData, const int nWidth, const int nHeight,
                      const int nPitch, uchar *pucJpeg, int &nDataLen,
                      const int nQuality /* = 70 */, const ALGO_E eAlgo/* = ALGO_JPEG*/)
{
    const int nChannels = nPitch / max(1, nWidth);
    if (NULL == pucData || NULL == pucJpeg
        || (1 != nChannels && 3 != nChannels))
    {
        return false;
    }

    Mat img(nHeight, nWidth, CV_8UC(nChannels), (void *) pucData, nPitch);
    vector<uchar> buf;
    vector<int> para;
    para.push_back(CV_IMWRITE_JPEG_QUALITY);
    para.push_back(nQuality);
    const string ext = g_strExt[eAlgo];
    bool bOk = imencode(ext, img, buf, para);
    if (!bOk)
    {
        buf.clear();
        return bOk;
    }

    Mat dst(buf);
    memcpy(pucJpeg, dst.data, sizeof(pucJpeg[0]) * buf.size());
    nDataLen = buf.size();
    buf.clear();

    return true;
}

bool CImageIO::Resize(const uchar *pucImgIn, uchar *pucImgOut, const int nWidth, const int nHeight,
                      const int nPitch, const int nWidthDst, const int nHeightDst, const int nPitchDst,
                      const INTER_E eType)
{
    const int nChannels = nPitch / max(1, nWidth);
    const int nChannelsDst = nPitchDst / max(1, nWidthDst);
    if (NULL == pucImgIn || NULL == pucImgOut
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || (1 != nChannelsDst && 3 != nChannelsDst && 4 != nChannelsDst)
        || nChannels != nChannelsDst
        || 0 == nWidth * nHeight
        || 0 == nWidthDst * nHeightDst)
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC(nChannels), (void *) pucImgIn, nPitch);
    Mat dst(nHeightDst, nWidthDst, CV_8UC(nChannelsDst), (void *) pucImgOut, nPitchDst);
    resize(src, dst, Size(nWidthDst, nHeightDst), 0.0, 0.0, eType % INTER_ALL);

    return true;
}

bool CImageIO::Resize(const uchar *pucImgIn, uchar *pucImgOut,
            const int nWidth, const int nHeight, const int nChannels, const int nPitch,
            const int nWidthDst, const int nHeightDst, const int nChannelsDst, const int nPitchDst,
            const INTER_E eType/* = INTER_LINEAR*/)
{
    if (NULL == pucImgIn || NULL == pucImgOut
        || (1 != nChannels && 3 != nChannels && 4 != nChannels)
        || (1 != nChannelsDst && 3 != nChannelsDst && 4 != nChannelsDst)
        || nChannels != nChannelsDst
        || 0 == nWidth * nHeight
        || 0 == nWidthDst * nHeightDst)
    {
        return false;
    }

    Mat src(nHeight, nWidth, CV_8UC(nChannels), (void *) pucImgIn, nPitch);
    Mat dst(nHeightDst, nWidthDst, CV_8UC(nChannelsDst), (void *) pucImgOut, nPitchDst);
    resize(src, dst, Size(nWidthDst, nHeightDst), 0.0, 0.0, eType % INTER_ALL);

    return true;
}
