{"_from": "opencv.js", "_id": "opencv.js@1.2.0", "_inBundle": false, "_integrity": "sha512-x8dmscvA47Nv7j6IPGfZv+M8tEKbgNdm2l0IlMkj+SunFPNvScw14dYTHfvoFQ6vIqimWtyCjdidfcxhJvVpjQ==", "_location": "/opencv.js", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "opencv.js", "name": "opencv.js", "escapedName": "opencv.js", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/opencv.js/-/opencv.js-1.2.0.tgz", "_shasum": "6cbe34f93604d3ada1505b3a546cca7f5db1da8d", "_spec": "opencv.js", "_where": "/home/<USER>", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/huningxin/opencv/issues"}, "bundleDependencies": false, "deprecated": false, "description": "OpenCV for JavaScript", "devDependencies": {"qunit": "^1.0.0", "test": ">=0.0.5"}, "files": ["opencv.js", "tests/", "README.md", "LICENSE", "test.js", "pacakge.json"], "homepage": "https://github.com/huningxin/opencv#readme", "keywords": ["Computer vision", "OpenCV"], "license": "BSD-3-<PERSON><PERSON>", "main": "opencv.js", "name": "opencv.js", "repository": {"type": "git", "url": "git+https://github.com/huningxin/opencv.git"}, "scripts": {"test": "node test.js"}, "version": "1.2.1"}