# TMAP Image Extraction Guide

This guide explains how to use the new `extract-images` command to batch extract all supported images from TMAP files with detailed performance analysis.

## Overview

The `extract-images` command extracts all supported image types from TMAP6 files:
- ✅ **Thumbnail** - Small preview image (≤ 256×256)
- ✅ **Navigate** - Navigation image from air image data
- ✅ **Macro** - Tissue overview image
- ✅ **Label** - Label image extracted from macro image
- ✅ **Macro-Label** - Combined tissue and label image

## Command Syntax

```bash
node src/cli.js extract-images <file> [options]
```

### Options

- `-o, --output-dir <dir>` - Output directory (default: `./extracted_images`)
- `-p, --performance` - Show detailed performance metrics
- `--prefix <prefix>` - Filename prefix (default: `image`)

## Usage Examples

### Basic Extraction
```bash
# Extract all images to default directory
node src/cli.js extract-images "path/to/file.tmap"

# Extract to custom directory with custom prefix
node src/cli.js extract-images "path/to/file.tmap" --output-dir "./my_images" --prefix "sample1"
```

### With Performance Analysis
```bash
# Extract with detailed performance metrics
node src/cli.js extract-images "path/to/file.tmap" --performance
```

## Output Format

### File Naming
- `{prefix}_thumbnail.jpg` - Thumbnail image
- `{prefix}_macro.jpg` - Macro image
- `{prefix}_macro-label.jpg` - Macro-label image
- `{prefix}_navigate.jpg` - Navigate image (if available)
- `{prefix}_label.jpg` - Label image (if available)

### Data Format
All extracted images are in **JPEG format** (compressed), not raw bitmap data. This ensures:
- Smaller file sizes for easy sharing
- Standard format compatible with all image viewers
- Maintains original quality from TMAP storage

## Performance Metrics

When using `--performance`, the command provides detailed analysis:

### Extraction Results Table
Shows status, file size, extraction time, and output path for each image type.

### Performance Analysis
- **Total Time** - Complete operation duration
- **Parse Time** - Time spent parsing the TMAP file
- **Extraction Time** - Time spent extracting images
- **Total Data Size** - Combined size of all extracted images
- **Average Speed** - Data extraction rate (MB/s)
- **Success Rate** - Percentage of successfully extracted images

### Individual Timing Breakdown
Shows per-image extraction times and their percentage of total extraction time.

### Memory & Efficiency Metrics
- Peak memory usage during operation
- Total number of operations performed
- Average operation time

## Example Output

```
📷 Image Extraction Results
══════════════════════════════════════════════════
┌─────────────┬───────────┬─────────┬───────────┬────────────────────────────────────────┐
│ Image Type  │ Status    │ Size    │ Time (ms) │ File Path                              │
├─────────────┼───────────┼─────────┼───────────┼────────────────────────────────────────┤
│ thumbnail   │ ✓ Success │ 4.85 kB │ 0         │ extracted_images\image_thumbnail.jpg   │
│ navigate    │ ✓ Success │ 96.2 kB │ 8         │ extracted_images\image_navigate.jpg    │
│ macro       │ ✓ Success │ 107 kB  │ 0         │ extracted_images\image_macro.jpg       │
│ label       │ ✓ Success │ 35.4 kB │ 15        │ extracted_images\image_label.jpg       │
│ macro-label │ ✓ Success │ 107 kB  │ 0         │ extracted_images\image_macro-label.jpg │
└─────────────┴───────────┴─────────┴───────────┴────────────────────────────────────────┘

⚡ Performance Analysis
══════════════════════════════════════════════════
┌─────────────────┬───────────┬─────────────────────────┐
│ Metric          │ Value     │ Details                 │
├─────────────────┼───────────┼─────────────────────────┤
│ Total Time      │ 203ms     │ Complete operation time │
│ Parse Time      │ 174ms     │ 85.7% of total          │
│ Extraction Time │ 29ms      │ 14.3% of total          │
│ Total Data Size │ 350 kB    │ 5 images                │
│ Average Speed   │ 1.65 MB/s │ Data extraction rate    │
│ Success Rate    │ 100.0%    │ 5/5 images              │
└─────────────────┴───────────┴─────────────────────────┘
```

## Technical Implementation

### TMAP6 Extension Data Processing
1. **Header Parsing** - Reads TMAP6 file header and extension information
2. **Data Location** - Locates image data in extension data sections
3. **JPEG Extraction** - Extracts JPEG data from TMAP6 extension format:
   - Skips 8-byte header + 24-byte IS_IMAGE_INFO structure
   - Searches for JPEG signature (0xFF 0xD8)
   - Extracts pure JPEG data for output

### Performance Optimization
- **Single Parse** - File is parsed once, then all images are extracted
- **Efficient Memory** - Uses streaming and minimal memory footprint
- **Error Handling** - Graceful handling of missing or corrupted image data
- **Progress Feedback** - Real-time progress indicators during extraction

## Troubleshooting

### Common Issues

**"No image data found"**
- Some TMAP files may not contain all image types
- Check if air image data is available for Navigate images
- Macro image must be present for Label extraction

**"Failed to extract images"**
- Check file permissions and disk space
- Ensure TMAP file is not corrupted
- Verify file is TMAP6 format

### Performance Tips

1. **Use SSD storage** for faster file I/O
2. **Sufficient RAM** for large TMAP files
3. **Close other applications** for accurate performance metrics

## Comparison with Individual Extraction

| Method | Command | Use Case |
|--------|---------|----------|
| Individual | `image <file> <type>` | Extract specific image type |
| Batch | `extract-images <file>` | Extract all images at once |

### Advantages of Batch Extraction
- **Efficiency** - Single file parse for all images
- **Performance Analysis** - Detailed timing and metrics
- **Convenience** - One command for all images
- **Consistency** - Same extraction parameters for all images

## Integration with Workflows

### Automated Processing
```bash
# Process multiple files
for file in *.tmap; do
    node src/cli.js extract-images "$file" --output-dir "./processed/$file" --performance
done
```

### Quality Assurance
Use performance metrics to:
- Monitor extraction success rates
- Identify problematic files
- Optimize processing workflows
- Track performance over time

## Technical Details

### Navigate Image Implementation
- **Source**: Air image data stored at `airImgOffset` in TMAP file
- **Dimensions**: Uses `airImgWidth` and `airImgHeight` from header
- **Processing**: Direct read from file offset, converted to JPEG if needed

### Label Image Implementation
- **Source**: Extracted from macro image using image processing
- **Algorithm**: Simplified version of iViewerSDK's ExtractLabel function
- **Steps**:
  1. Downsample macro image to ≤256 pixels
  2. Convert to grayscale
  3. Apply threshold to identify bright label regions
  4. Generate binary mask for label areas

### Performance Characteristics
- **Navigate**: Fast (direct file read)
- **Label**: Slower (requires image processing)
- **Thumbnail/Macro**: Medium (JPEG extraction from extension data)

## Future Enhancements

- **Advanced Label Detection** - Implement full iViewerSDK algorithm
- **Parallel Processing** - Multi-threaded extraction
- **Format Options** - Support for PNG, TIFF output formats
- **Metadata Export** - Include image metadata in output
- **Tile Reconstruction** - Alternative navigate image generation from tiles
