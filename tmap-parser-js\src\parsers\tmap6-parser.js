/**
 * TMAP6 File Parser
 * Parses TMAP version 6.0 files and extracts metadata
 */

import fs from 'fs/promises';
import { BufferReader } from '../utils/buffer-reader.js';
import { PerformanceMonitor, timeOperation } from '../utils/performance.js';
import {
  TmapHeader,
  TmapExtInfo,
  ImageInfo5,
  ShrinkTileInfo,
  TileInfo,
  ImageType,
  ExtensionType,
  CompressionAlgo,
  SlideType,
  MAX_TILE_NUM
} from '../structures/tmap6-structures.js';

export class Tmap6Parser {
  constructor() {
    this.performanceMonitor = new PerformanceMonitor();
    this.filePath = null;
    this.fileSize = 0;
    this.header = null;
    this.extInfo = null;
    this.imageInfos = [];
    this.shrinkTileInfos = [];
    this.extensionData = new Map();
  }

  /**
   * Parse TMAP6 file
   * @param {string} filePath - Path to TMAP file
   * @returns {Promise<object>} Parsed file information
   */
  async parseFile(filePath) {
    return await timeOperation('parseFile', async () => {
      this.filePath = filePath;
      
      // Read file stats
      const stats = await fs.stat(filePath);
      this.fileSize = stats.size;
      
      // Read file header first to validate
      const headerBuffer = await this.readFileChunk(0, 1024); // Read first 1KB
      const reader = new BufferReader(headerBuffer);
      
      // Parse and validate header
      await this.parseHeader(reader);
      
      // Read full file if header is valid
      const fullBuffer = await fs.readFile(filePath);
      const fullReader = new BufferReader(fullBuffer);
      
      // Re-parse with full buffer
      await this.parseHeader(fullReader);
      await this.parseExtensionInfo(fullReader);
      await this.parseImageInfos(fullReader);
      await this.parseShrinkTileInfos(fullReader);
      await this.parseExtensionData(fullReader);
      
      return this.getFileInfo();
    });
  }

  /**
   * Read a chunk of the file
   * @param {number} offset - File offset
   * @param {number} length - Chunk length
   * @returns {Promise<Buffer>} File chunk
   */
  async readFileChunk(offset, length) {
    return await timeOperation('readFileChunk', async () => {
      const fileHandle = await fs.open(this.filePath, 'r');
      try {
        const buffer = Buffer.alloc(length);
        const { bytesRead } = await fileHandle.read(buffer, 0, length, offset);
        return buffer.subarray(0, bytesRead);
      } finally {
        await fileHandle.close();
      }
    });
  }

  /**
   * Parse TMAP header
   * @param {BufferReader} reader - Buffer reader
   */
  async parseHeader(reader) {
    await timeOperation('parseHeader', async () => {
      // Use struct-compile to parse the header with proper alignment
      const headerBuffer = reader.readBytes(TmapHeader.prototype.length);
      const headerStruct = new TmapHeader(headerBuffer);

      // Validate magic number
      const magic = Buffer.from(headerStruct.header).toString('ascii');
      if (magic !== 'TMAP') {
        throw new Error(`Invalid TMAP file: expected 'TMAP', got '${magic}'`);
      }

      // Convert version from ASCII
      const minorVersion = headerStruct.mainVersion[0] - 48;
      const majorVersion = headerStruct.mainVersion[1] - 48;
      const version = majorVersion + minorVersion / 10;

      if (version < 5.0 || version >= 7.0) {
        throw new Error(`Unsupported TMAP version: ${version}. This parser supports version 6.x only.`);
      }

      // Create header object with parsed data
      this.header = {
        magic,
        mainVersion: headerStruct.mainVersion,
        version,
        maxFocusNum: headerStruct.maxFocusNum,
        imageFormat: headerStruct.imageFormat,
        fileNum: headerStruct.fileNum,
        layerSize: headerStruct.layerSize,
        imgColor: headerStruct.imgColor,
        checkSum: headerStruct.checkSum,
        ratioStep: headerStruct.ratioStep,
        maxLaySize: headerStruct.maxLaySize,
        slideType: headerStruct.slideType,
        bkColor: headerStruct.bkColor,
        pixelSize: headerStruct.pixelSize,
        totalImgNum: headerStruct.totalImgNum,
        maxZoomRate: headerStruct.maxZoomRate,
        imgCol: headerStruct.imgCol,
        imgRow: headerStruct.imgRow,
        imgWidth: headerStruct.imgWidth,
        imgHeight: headerStruct.imgHeight,
        tileWidth: headerStruct.tileWidth,
        tileHeight: headerStruct.tileHeight,
        airImgWidth: headerStruct.airImgWidth,
        airImgHeight: headerStruct.airImgHeight,
        shrinkTileNum: headerStruct.shrinkTileNum,
        totalImgWidth: headerStruct.totalImgWidth,
        totalImgHeight: headerStruct.totalImgHeight,
        airImgOffset: headerStruct.airImgOffset
      };
    });
  }

  /**
   * Parse extension information
   * @param {BufferReader} reader - Buffer reader
   */
  async parseExtensionInfo(reader) {
    await timeOperation('parseExtensionInfo', async () => {
      if (this.header.version >= 5.0) {
        const extInfo = {};
        
        extInfo.maxExtDataLen = reader.readUInt32();
        extInfo.sumExtDataLen = reader.readUInt32();
        extInfo.tmapDataEndPos = reader.readUInt32();
        
        extInfo.dataType = reader.readArray(8, () => reader.readUInt32());
        extInfo.dataOffset = reader.readArray(8, () => reader.readUInt32());
        extInfo.dataLength = reader.readArray(8, () => reader.readUInt32());
        extInfo.reserveByte = reader.readArray(24, () => reader.readUInt8());
        
        this.extInfo = extInfo;
      }
    });
  }

  /**
   * Parse image information array
   * @param {BufferReader} reader - Buffer reader
   */
  async parseImageInfos(reader) {
    await timeOperation('parseImageInfos', async () => {
      const imageCount = this.header.totalImgNum;
      this.imageInfos = [];
      
      for (let i = 0; i < imageCount; i++) {
        const imageInfo = {};
        
        imageInfo.fileID = reader.readUInt8();
        imageInfo.layer = reader.readInt8();
        imageInfo.reversed = reader.readArray(2, () => reader.readUInt8());
        imageInfo.topDx = reader.readInt8();
        imageInfo.topDy = reader.readInt8();
        imageInfo.leftDx = reader.readInt8();
        imageInfo.leftDy = reader.readInt8();
        imageInfo.imgCol = reader.readInt16();
        imageInfo.imgRow = reader.readInt16();
        imageInfo.x = reader.readInt32();
        imageInfo.y = reader.readInt32();
        
        // Read tile information
        imageInfo.tiles = [];
        for (let j = 0; j < MAX_TILE_NUM; j++) {
          const tile = {};
          tile.layerNo = reader.readUInt8();
          tile.tileCol = reader.readUInt8();
          tile.tileRow = reader.readUInt8();
          tile.fileOffset = reader.readInt32();
          tile.length = reader.readUInt32();
          imageInfo.tiles.push(tile);
        }
        
        this.imageInfos.push(imageInfo);
      }
    });
  }

  /**
   * Parse shrink tile information array
   * @param {BufferReader} reader - Buffer reader
   */
  async parseShrinkTileInfos(reader) {
    await timeOperation('parseShrinkTileInfos', async () => {
      const tileCount = this.header.shrinkTileNum;
      this.shrinkTileInfos = [];
      
      for (let i = 0; i < tileCount; i++) {
        const tileInfo = {};
        
        tileInfo.fileID = reader.readUInt8();
        tileInfo.layerNo = reader.readUInt8();
        tileInfo.x = reader.readInt32();
        tileInfo.y = reader.readInt32();
        tileInfo.fileOffset = reader.readInt32();
        tileInfo.length = reader.readUInt32();
        
        this.shrinkTileInfos.push(tileInfo);
      }
    });
  }

  /**
   * Parse extension data
   * @param {BufferReader} reader - Buffer reader
   */
  async parseExtensionData(reader) {
    await timeOperation('parseExtensionData', async () => {
      if (!this.extInfo) return;
      
      for (let i = 0; i < 8; i++) {
        const dataType = this.extInfo.dataType[i];
        const dataOffset = this.extInfo.dataOffset[i];
        const dataLength = this.extInfo.dataLength[i];
        
        if (dataType !== ExtensionType.NONE && dataLength > 0) {
          reader.setOffset(dataOffset);
          const data = reader.readBytes(dataLength);
          
          this.extensionData.set(dataType, {
            type: dataType,
            offset: dataOffset,
            length: dataLength,
            data: data
          });
        }
      }
    });
  }

  /**
   * Get comprehensive file information
   * @returns {object} File information
   */
  getFileInfo() {
    const info = {
      // File metadata
      filePath: this.filePath,
      fileSize: this.fileSize,

      // Version information
      version: this.header.version,
      versionString: `${String.fromCharCode(this.header.mainVersion[1])}.${String.fromCharCode(this.header.mainVersion[0])}`,

      // Image dimensions
      totalWidth: this.header.totalImgWidth,
      totalHeight: this.header.totalImgHeight,
      imageWidth: this.header.imgWidth,
      imageHeight: this.header.imgHeight,

      // Tile information
      tileWidth: this.header.tileWidth,
      tileHeight: this.header.tileHeight,
      imageColumns: this.header.imgCol,
      imageRows: this.header.imgRow,
      totalImages: this.header.totalImgNum,
      shrinkTileCount: this.header.shrinkTileNum,

      // Scanning parameters
      scanMagnification: this.header.maxZoomRate,
      pixelSize: this.header.pixelSize,
      focusLayers: this.header.maxFocusNum,

      // Image properties
      colorDepth: this.header.imgColor,
      backgroundColor: this.header.bkColor,
      slideType: this.getSlideTypeName(this.header.slideType),

      // File structure
      fileCount: this.header.fileNum,
      layerCount: this.header.layerSize,
      ratioStep: this.header.ratioStep,

      // Extension data
      hasExtensionData: this.extInfo !== null,
      extensionTypes: Array.from(this.extensionData.keys()),

      // Performance metrics
      performanceMetrics: this.performanceMonitor.getSummary()
    };

    return info;
  }

  /**
   * Get slide type name from numeric value
   * @param {number} slideType - Numeric slide type
   * @returns {string} Slide type name
   */
  getSlideTypeName(slideType) {
    const types = {
      [SlideType.HE]: 'H&E',
      [SlideType.IHC]: 'IHC',
      [SlideType.FISH]: 'FISH',
      [SlideType.OTHER]: 'Other'
    };
    return types[slideType] || 'Unknown';
  }

  /**
   * Get extension data by type
   * @param {number} type - Extension type
   * @returns {object|null} Extension data or null
   */
  getExtensionData(type) {
    return this.extensionData.get(type) || null;
  }

  /**
   * Get slide type name from numeric value
   * @param {number} slideType - Numeric slide type
   * @returns {string} Slide type name
   */
  getSlideTypeName(slideType) {
    const types = {
      [SlideType.HE]: 'H&E',
      [SlideType.IHC]: 'IHC',
      [SlideType.FISH]: 'FISH',
      [SlideType.OTHER]: 'Other'
    };
    return types[slideType] || 'Unknown';
  }

  /**
   * Get extension data by type
   * @param {number} type - Extension type
   * @returns {object|null} Extension data or null
   */
  getExtensionData(type) {
    return this.extensionData.get(type) || null;
  }

  /**
   * Get all performance metrics
   * @returns {Map} Performance metrics
   */
  getPerformanceMetrics() {
    return this.performanceMonitor.getAllMetrics();
  }
}
