/**
 * TMAP6 File Parser
 * Parses TMAP version 6.0 files and extracts metadata
 */

import fs from 'fs/promises';
import { BufferReader } from '../utils/buffer-reader.js';
import { PerformanceMonitor, timeOperation } from '../utils/performance.js';
import {
  TmapHeaderStruct,
  TmapExtInfoStruct,
  ImageInfo5Struct,
  ShrinkTileInfoStruct,
  ImageType,
  ExtensionType,
  CompressionAlgo,
  SlideType,
  MAX_TILE_NUM
} from '../structures/tmap6-structures.js';

export class Tmap6Parser {
  constructor() {
    this.performanceMonitor = new PerformanceMonitor();
    this.filePath = null;
    this.fileSize = 0;
    this.header = null;
    this.extInfo = null;
    this.imageInfos = [];
    this.shrinkTileInfos = [];
    this.extensionData = new Map();
  }

  /**
   * Parse TMAP6 file
   * @param {string} filePath - Path to TMAP file
   * @returns {Promise<object>} Parsed file information
   */
  async parseFile(filePath) {
    return await timeOperation('parseFile', async () => {
      this.filePath = filePath;
      
      // Read file stats
      const stats = await fs.stat(filePath);
      this.fileSize = stats.size;
      
      // Read file header first to validate
      const headerBuffer = await this.readFileChunk(0, 1024); // Read first 1KB
      const reader = new BufferReader(headerBuffer);
      
      // Parse and validate header
      await this.parseHeader(reader);
      
      // Read full file if header is valid
      const fullBuffer = await fs.readFile(filePath);
      const fullReader = new BufferReader(fullBuffer);
      
      // Re-parse with full buffer
      await this.parseHeader(fullReader);
      await this.parseExtensionInfo(fullReader);
      await this.parseImageInfos(fullReader);
      await this.parseShrinkTileInfos(fullReader);
      await this.parseExtensionData(fullReader);
      
      return this.getFileInfo();
    });
  }

  /**
   * Read a chunk of the file
   * @param {number} offset - File offset
   * @param {number} length - Chunk length
   * @returns {Promise<Buffer>} File chunk
   */
  async readFileChunk(offset, length) {
    return await timeOperation('readFileChunk', async () => {
      const fileHandle = await fs.open(this.filePath, 'r');
      try {
        const buffer = Buffer.alloc(length);
        const { bytesRead } = await fileHandle.read(buffer, 0, length, offset);
        return buffer.subarray(0, bytesRead);
      } finally {
        await fileHandle.close();
      }
    });
  }

  /**
   * Parse TMAP header
   * @param {BufferReader} reader - Buffer reader
   */
  async parseHeader(reader) {
    await timeOperation('parseHeader', async () => {
      const startOffset = reader.getOffset();
      
      // Read header fields manually for better control
      const header = {};
      
      // Read magic number
      header.magic = reader.readString(4, 'ascii');
      if (header.magic !== 'TMAP') {
        throw new Error(`Invalid TMAP file: expected 'TMAP', got '${header.magic}'`);
      }
      
      // Read version
      header.mainVersion = reader.readArray(2, () => reader.readUInt8());
      header.version = header.mainVersion[0] + header.mainVersion[1] / 10;
      
      if (header.version < 5.0 || header.version >= 7.0) {
        throw new Error(`Unsupported TMAP version: ${header.version}. This parser supports version 6.x only.`);
      }
      
      // Read remaining header fields
      header.maxFocusNum = reader.readUInt8();
      header.imageFormat = reader.readUInt8();
      header.fileNum = reader.readUInt8();
      header.layerSize = reader.readUInt8();
      header.imgColor = reader.readUInt8();
      header.checkSum = reader.readUInt8();
      header.ratioStep = reader.readUInt8();
      header.maxLaySize = reader.readUInt8();
      header.slideType = reader.readUInt8();
      header.bkColor = reader.readUInt8();
      header.pixelSize = reader.readFloat32();
      header.totalImgNum = reader.readInt32();
      
      // Image data info
      header.maxZoomRate = reader.readInt16();
      header.imgCol = reader.readInt16();
      header.imgRow = reader.readInt16();
      header.imgWidth = reader.readInt16();
      header.imgHeight = reader.readInt16();
      header.tileWidth = reader.readInt16();
      header.tileHeight = reader.readInt16();
      header.airImgWidth = reader.readInt16();
      header.airImgHeight = reader.readInt16();
      header.shrinkTileNum = reader.readInt32();
      header.totalImgWidth = reader.readInt32();
      header.totalImgHeight = reader.readInt32();
      header.airImgOffset = reader.readInt32();
      
      this.header = header;
      
      // Log header parsing performance
      this.performanceMonitor.logMetrics('parseHeader');
    });
  }

  /**
   * Parse extension information
   * @param {BufferReader} reader - Buffer reader
   */
  async parseExtensionInfo(reader) {
    await timeOperation('parseExtensionInfo', async () => {
      if (this.header.version >= 5.0) {
        const extInfo = {};
        
        extInfo.maxExtDataLen = reader.readUInt32();
        extInfo.sumExtDataLen = reader.readUInt32();
        extInfo.tmapDataEndPos = reader.readUInt32();
        
        extInfo.dataType = reader.readArray(8, () => reader.readUInt32());
        extInfo.dataOffset = reader.readArray(8, () => reader.readUInt32());
        extInfo.dataLength = reader.readArray(8, () => reader.readUInt32());
        extInfo.reserveByte = reader.readArray(24, () => reader.readUInt8());
        
        this.extInfo = extInfo;
      }
    });
  }

  /**
   * Parse image information array
   * @param {BufferReader} reader - Buffer reader
   */
  async parseImageInfos(reader) {
    await timeOperation('parseImageInfos', async () => {
      const imageCount = this.header.totalImgNum;
      this.imageInfos = [];
      
      for (let i = 0; i < imageCount; i++) {
        const imageInfo = {};
        
        imageInfo.fileID = reader.readUInt8();
        imageInfo.layer = reader.readInt8();
        imageInfo.reversed = reader.readArray(2, () => reader.readUInt8());
        imageInfo.topDx = reader.readInt8();
        imageInfo.topDy = reader.readInt8();
        imageInfo.leftDx = reader.readInt8();
        imageInfo.leftDy = reader.readInt8();
        imageInfo.imgCol = reader.readInt16();
        imageInfo.imgRow = reader.readInt16();
        imageInfo.x = reader.readInt32();
        imageInfo.y = reader.readInt32();
        
        // Read tile information
        imageInfo.tiles = [];
        for (let j = 0; j < MAX_TILE_NUM; j++) {
          const tile = {};
          tile.layerNo = reader.readUInt8();
          tile.tileCol = reader.readUInt8();
          tile.tileRow = reader.readUInt8();
          tile.fileOffset = reader.readInt32();
          tile.length = reader.readUInt32();
          imageInfo.tiles.push(tile);
        }
        
        this.imageInfos.push(imageInfo);
      }
    });
  }

  /**
   * Parse shrink tile information array
   * @param {BufferReader} reader - Buffer reader
   */
  async parseShrinkTileInfos(reader) {
    await timeOperation('parseShrinkTileInfos', async () => {
      const tileCount = this.header.shrinkTileNum;
      this.shrinkTileInfos = [];
      
      for (let i = 0; i < tileCount; i++) {
        const tileInfo = {};
        
        tileInfo.fileID = reader.readUInt8();
        tileInfo.layerNo = reader.readUInt8();
        tileInfo.x = reader.readInt32();
        tileInfo.y = reader.readInt32();
        tileInfo.fileOffset = reader.readInt32();
        tileInfo.length = reader.readUInt32();
        
        this.shrinkTileInfos.push(tileInfo);
      }
    });
  }

  /**
   * Parse extension data
   * @param {BufferReader} reader - Buffer reader
   */
  async parseExtensionData(reader) {
    await timeOperation('parseExtensionData', async () => {
      if (!this.extInfo) return;
      
      for (let i = 0; i < 8; i++) {
        const dataType = this.extInfo.dataType[i];
        const dataOffset = this.extInfo.dataOffset[i];
        const dataLength = this.extInfo.dataLength[i];
        
        if (dataType !== ExtensionType.NONE && dataLength > 0) {
          reader.setOffset(dataOffset);
          const data = reader.readBytes(dataLength);
          
          this.extensionData.set(dataType, {
            type: dataType,
            offset: dataOffset,
            length: dataLength,
            data: data
          });
        }
      }
    });
  }

  /**
   * Get comprehensive file information
   * @returns {object} File information
   */
  getFileInfo() {
    const info = {
      // File metadata
      filePath: this.filePath,
      fileSize: this.fileSize,
      
      // Version information
      version: this.header.version,
      versionString: `${this.header.mainVersion[0]}.${this.header.mainVersion[1]}`,
      
      // Image dimensions
      totalWidth: this.header.totalImgWidth,
      totalHeight: this.header.totalImgHeight,
      imageWidth: this.header.imgWidth,
      imageHeight: this.header.imgHeight,
      
      // Tile information
      tileWidth: this.header.tileWidth,
      tileHeight: this.header.tileHeight,
      imageColumns: this.header.imgCol,
      imageRows: this.header.imgRow,
      totalImages: this.header.totalImgNum,
      shrinkTileCount: this.header.shrinkTileNum,
      
      // Scanning parameters
      scanMagnification: this.header.maxZoomRate,
      pixelSize: this.header.pixelSize,
      focusLayers: this.header.maxFocusNum,
      
      // Image properties
      colorDepth: this.header.imgColor,
      backgroundColor: this.header.bkColor,
      slideType: this.getSlideTypeName(this.header.slideType),
      
      // File structure
      fileCount: this.header.fileNum,
      layerCount: this.header.layerSize,
      ratioStep: this.header.ratioStep,
      
      // Extension data
      hasExtensionData: this.extInfo !== null,
      extensionTypes: Array.from(this.extensionData.keys()),
      
      // Performance metrics
      performanceMetrics: this.performanceMonitor.getSummary()
    };
    
    return info;
  }

  /**
   * Get slide type name from numeric value
   * @param {number} slideType - Numeric slide type
   * @returns {string} Slide type name
   */
  getSlideTypeName(slideType) {
    const types = {
      [SlideType.HE]: 'H&E',
      [SlideType.IHC]: 'IHC',
      [SlideType.FISH]: 'FISH',
      [SlideType.OTHER]: 'Other'
    };
    return types[slideType] || 'Unknown';
  }

  /**
   * Get extension data by type
   * @param {number} type - Extension type
   * @returns {object|null} Extension data or null
   */
  getExtensionData(type) {
    return this.extensionData.get(type) || null;
  }

  /**
   * Get all performance metrics
   * @returns {Map} Performance metrics
   */
  getPerformanceMetrics() {
    return this.performanceMonitor.getAllMetrics();
  }
}
