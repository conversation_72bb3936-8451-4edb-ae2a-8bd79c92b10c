/**
 * TMAP6 File Parser
 * Parses TMAP version 6.0 files and extracts metadata
 */

import fs from 'fs/promises';
import { BufferReader } from '../utils/buffer-reader.js';
import { PerformanceMonitor, timeOperation } from '../utils/performance.js';
import sharp from 'sharp';
import {
  TmapHeader,
  ExtensionType,
  SlideType,
  ImageType,
  MAX_TILE_NUM
} from '../structures/tmap6-structures.js';
import {
  extractLabel as iViewerExtractLabel,
  extractMacro as iViewerExtractMacro
} from '../utils/iviewer-segmentation.js';

export class Tmap6Parser {
  constructor() {
    this.performanceMonitor = new PerformanceMonitor();
    this.filePath = null;
    this.fileSize = 0;
    this.header = null;
    this.extInfo = null;
    this.imageInfos = [];
    this.shrinkTileInfos = [];
    this.extensionData = new Map();
  }

  /**
   * Parse TMAP6 file
   * @param {string} filePath - Path to TMAP file
   * @returns {Promise<object>} Parsed file information
   */
  async parseFile(filePath) {
    return await timeOperation('parseFile', async () => {
      this.filePath = filePath;
      
      // Read file stats
      const stats = await fs.stat(filePath);
      this.fileSize = stats.size;
      
      // Read file header first to validate
      const headerBuffer = await this.readFileChunk(0, 1024); // Read first 1KB
      const reader = new BufferReader(headerBuffer);
      
      // Parse and validate header
      await this.parseHeader(reader);
      
      // Read full file if header is valid
      const fullBuffer = await fs.readFile(filePath);
      const fullReader = new BufferReader(fullBuffer);
      
      // Re-parse with full buffer
      await this.parseHeader(fullReader);
      await this.parseExtensionInfo(fullReader);
      await this.parseImageInfos(fullReader);
      await this.parseShrinkTileInfos(fullReader);
      await this.parseExtensionData(fullReader);
      
      return this.getFileInfo();
    });
  }

  /**
   * Read a chunk of the file
   * @param {number} offset - File offset
   * @param {number} length - Chunk length
   * @returns {Promise<Buffer>} File chunk
   */
  async readFileChunk(offset, length) {
    return await timeOperation('readFileChunk', async () => {
      const fileHandle = await fs.open(this.filePath, 'r');
      try {
        const buffer = Buffer.alloc(length);
        const { bytesRead } = await fileHandle.read(buffer, 0, length, offset);
        return buffer.subarray(0, bytesRead);
      } finally {
        await fileHandle.close();
      }
    });
  }

  /**
   * Parse TMAP header
   * @param {BufferReader} reader - Buffer reader
   */
  async parseHeader(reader) {
    await timeOperation('parseHeader', async () => {
      // Use struct-compile to parse the header with proper alignment
      const tempHeader = new TmapHeader();
      const headerBuffer = reader.readBytes(tempHeader.length);
      const headerStruct = new TmapHeader(headerBuffer);

      // Validate magic number
      const magic = Buffer.from(headerStruct.header).toString('ascii');
      if (magic !== 'TMAP') {
        throw new Error(`Invalid TMAP file: expected 'TMAP', got '${magic}'`);
      }

      // Convert version from ASCII - based on iViewerSDK code
      // chMainVersion[0] is minor version (e.g., '0'), chMainVersion[1] is major version (e.g., '6')
      const minorVersion = headerStruct.mainVersion[0] - 48; // '0' -> 0
      const majorVersion = headerStruct.mainVersion[1] - 48; // '6' -> 6
      const version = majorVersion + minorVersion / 10; // 6.0

      if (version < 5.0 || version >= 7.0) {
        throw new Error(`Unsupported TMAP version: ${version}. This parser supports version 6.x only.`);
      }

      // Create header object with parsed data
      this.header = {
        magic,
        mainVersion: headerStruct.mainVersion,
        version,
        maxFocusNum: headerStruct.maxFocusNum,
        imageFormat: headerStruct.imageFormat,
        fileNum: headerStruct.fileNum,
        layerSize: headerStruct.layerSize,
        imgColor: headerStruct.imgColor,
        checkSum: headerStruct.checkSum,
        ratioStep: headerStruct.ratioStep,
        maxLaySize: headerStruct.maxLaySize,
        slideType: headerStruct.slideType,
        bkColor: headerStruct.bkColor,
        pixelSize: headerStruct.pixelSize,
        totalImgNum: headerStruct.totalImgNum,
        maxZoomRate: headerStruct.maxZoomRate,
        imgCol: headerStruct.imgCol,
        imgRow: headerStruct.imgRow,
        imgWidth: headerStruct.imgWidth,
        imgHeight: headerStruct.imgHeight,
        tileWidth: headerStruct.tileWidth,
        tileHeight: headerStruct.tileHeight,
        airImgWidth: headerStruct.airImgWidth,
        airImgHeight: headerStruct.airImgHeight,
        shrinkTileNum: headerStruct.shrinkTileNum,
        totalImgWidth: headerStruct.totalImgWidth,
        totalImgHeight: headerStruct.totalImgHeight,
        airImgOffset: headerStruct.airImgOffset
      };
    });
  }

  /**
   * Parse extension information
   * @param {BufferReader} reader - Buffer reader
   */
  async parseExtensionInfo(reader) {
    await timeOperation('parseExtensionInfo', async () => {
      if (this.header.version >= 5.0) {
        // Manual parsing for arrays to avoid struct-compile issues
        const extInfo = {};

        extInfo.maxExtDataLen = reader.readUInt32();
        extInfo.sumExtDataLen = reader.readUInt32();
        extInfo.tmapDataEndPos = reader.readUInt32();

        extInfo.dataType = reader.readArray(8, () => reader.readUInt32());
        extInfo.dataOffset = reader.readArray(8, () => reader.readUInt32());
        extInfo.dataLength = reader.readArray(8, () => reader.readUInt32());
        extInfo.reserveByte = reader.readArray(24, () => reader.readUInt8());

        this.extInfo = extInfo;
      }
    });
  }

  /**
   * Parse image information array
   * @param {BufferReader} reader - Buffer reader
   */
  async parseImageInfos(reader) {
    await timeOperation('parseImageInfos', async () => {
      const imageCount = this.header.totalImgNum;
      this.imageInfos = [];
      
      for (let i = 0; i < imageCount; i++) {
        const imageInfo = {};
        
        imageInfo.fileID = reader.readUInt8();
        imageInfo.layer = reader.readInt8();
        imageInfo.reversed = reader.readArray(2, () => reader.readUInt8());
        imageInfo.topDx = reader.readInt8();
        imageInfo.topDy = reader.readInt8();
        imageInfo.leftDx = reader.readInt8();
        imageInfo.leftDy = reader.readInt8();
        imageInfo.imgCol = reader.readInt16();
        imageInfo.imgRow = reader.readInt16();
        imageInfo.x = reader.readInt32();
        imageInfo.y = reader.readInt32();
        
        // Read tile information
        imageInfo.tiles = [];
        for (let j = 0; j < MAX_TILE_NUM; j++) {
          const tile = {};
          tile.layerNo = reader.readUInt8();
          tile.tileCol = reader.readUInt8();
          tile.tileRow = reader.readUInt8();
          tile.fileOffset = reader.readInt32();
          tile.length = reader.readUInt32();
          imageInfo.tiles.push(tile);
        }
        
        this.imageInfos.push(imageInfo);
      }
    });
  }

  /**
   * Parse shrink tile information array
   * @param {BufferReader} reader - Buffer reader
   */
  async parseShrinkTileInfos(reader) {
    await timeOperation('parseShrinkTileInfos', async () => {
      const tileCount = this.header.shrinkTileNum;
      this.shrinkTileInfos = [];
      
      for (let i = 0; i < tileCount; i++) {
        const tileInfo = {};
        
        tileInfo.fileID = reader.readUInt8();
        tileInfo.layerNo = reader.readUInt8();
        tileInfo.x = reader.readInt32();
        tileInfo.y = reader.readInt32();
        tileInfo.fileOffset = reader.readInt32();
        tileInfo.length = reader.readUInt32();
        
        this.shrinkTileInfos.push(tileInfo);
      }
    });
  }

  /**
   * Parse extension data
   * @param {BufferReader} reader - Buffer reader
   */
  async parseExtensionData(reader) {
    await timeOperation('parseExtensionData', async () => {
      if (!this.extInfo) return;
      
      for (let i = 0; i < 8; i++) {
        const dataType = this.extInfo.dataType[i];
        const dataOffset = this.extInfo.dataOffset[i];
        const dataLength = this.extInfo.dataLength[i];
        
        if (dataType !== ExtensionType.NONE && dataLength > 0) {
          reader.setOffset(dataOffset);
          const data = reader.readBytes(dataLength);
          
          this.extensionData.set(dataType, {
            type: dataType,
            offset: dataOffset,
            length: dataLength,
            data: data
          });
        }
      }
    });
  }

  /**
   * Get comprehensive file information
   * @returns {object} File information
   */
  getFileInfo() {
    const info = {
      // File metadata
      filePath: this.filePath,
      fileSize: this.fileSize,

      // Version information
      version: this.header.version,
      versionString: `${String.fromCharCode(this.header.mainVersion[1])}.${String.fromCharCode(this.header.mainVersion[0])}`,

      // Image dimensions
      totalWidth: this.header.totalImgWidth,
      totalHeight: this.header.totalImgHeight,
      imageWidth: this.header.imgWidth,
      imageHeight: this.header.imgHeight,

      // Tile information
      tileWidth: this.header.tileWidth,
      tileHeight: this.header.tileHeight,
      imageColumns: this.header.imgCol,
      imageRows: this.header.imgRow,
      totalImages: this.header.totalImgNum,
      shrinkTileCount: this.header.shrinkTileNum,

      // Scanning parameters
      scanMagnification: this.header.maxZoomRate,
      pixelSize: this.header.pixelSize,
      focusLayers: this.header.maxFocusNum,

      // Image properties
      colorDepth: this.header.imgColor,
      backgroundColor: this.header.bkColor,
      slideType: this.getSlideTypeName(this.header.slideType),

      // File structure
      fileCount: this.header.fileNum,
      layerCount: this.header.layerSize,
      ratioStep: this.header.ratioStep,

      // Extension data
      hasExtensionData: this.extInfo !== null,
      extensionTypes: Array.from(this.extensionData.keys()),

      // Performance metrics
      performanceMetrics: this.performanceMonitor.getSummary()
    };

    return info;
  }

  /**
   * Get slide type name from numeric value
   * @param {number} slideType - Numeric slide type
   * @returns {string} Slide type name
   */
  getSlideTypeName(slideType) {
    const types = {
      [SlideType.HE]: 'H&E',
      [SlideType.IHC]: 'IHC',
      [SlideType.FISH]: 'FISH',
      [SlideType.OTHER]: 'Other'
    };
    return types[slideType] || 'Unknown';
  }

  /**
   * Get extension data by type
   * @param {number} type - Extension type
   * @returns {object|null} Extension data or null
   */
  getExtensionData(type) {
    return this.extensionData.get(type) || null;
  }

  /**
   * Get all performance metrics
   * @returns {Map} Performance metrics
   */
  getPerformanceMetrics() {
    return this.performanceMonitor.getAllMetrics();
  }

  /**
   * Get image data for specified image type
   * @param {number} imageType - Image type (ImageType enum)
   * @param {boolean} raw - If true, return raw bitmap data; if false, return compressed data
   * @returns {Promise<Buffer|null>} Image data buffer or null if not found
   */
  async getImageData(imageType, raw = true) {
    return await timeOperation('getImageData', async () => {
      // Validate image type (exclude TILE, WHOLE, ALL as per original SDK)
      if (imageType === ImageType.TILE || imageType === ImageType.WHOLE || imageType === ImageType.ALL) {
        throw new Error('Image type TILE, WHOLE, and ALL are not supported for direct image extraction');
      }

      switch (imageType) {
        case ImageType.THUMBNAIL:
          return await this.getThumbnailImage(raw);

        case ImageType.NAVIGATE:
          return await this.getNavigateImage(raw);

        case ImageType.MACRO:
          return await this.getMacroImage(raw);

        case ImageType.LABEL:
          return await this.getLabelImage(raw);

        case ImageType.MACRO_LABEL:
          return await this.getMacroLabelImage(raw);

        default:
          throw new Error(`Unsupported image type: ${imageType}`);
      }
    });
  }

  /**
   * Get thumbnail image from extension data
   * @param {boolean} raw - If true, return raw bitmap data; if false, return compressed data
   * @returns {Promise<Buffer|null>} Thumbnail image data
   */
  async getThumbnailImage(raw = true) {
    const extData = this.getExtensionData(ExtensionType.THUMB_IMAGE);
    if (extData && extData.data) {
      // Extract JPEG data from TMAP6 extension format
      const jpegData = this.extractJpegFromExtensionData(extData.data);

      if (raw && jpegData) {
        return await this.decompressImageData(jpegData, ImageType.THUMBNAIL);
      } else if (jpegData) {
        return jpegData;
      } else {
        // Fallback to original data if JPEG extraction fails
        return extData.data;
      }
    }
    return null;
  }

  /**
   * Get navigate image (air image data)
   * @param {boolean} raw - If true, return raw bitmap data; if false, return compressed data
   * @returns {Promise<Buffer|null>} Navigate image data
   */
  async getNavigateImage(raw = true) {
    return await timeOperation('getNavigateImage', async () => {
      try {
        // In TMAP6, navigate image is stored as compressed JPEG data at airImgOffset
        if (!this.header.airImgOffset || this.header.airImgOffset <= 0) {
          return null;
        }

        const width = this.header.airImgWidth;
        const height = this.header.airImgHeight;
        const depth = this.header.imgColor;

        if (width <= 0 || height <= 0) {
          return null;
        }

        // Read compressed air image data from file
        // The data size is not fixed, so we need to read a reasonable chunk and find JPEG boundaries
        const fs = await import('fs/promises');
        const fileHandle = await fs.open(this.filePath, 'r');

        try {
          // Read a large chunk to find the JPEG data
          const maxChunkSize = Math.min(width * height, 1024 * 1024); // Max 1MB
          const airBuffer = Buffer.alloc(maxChunkSize);
          const { bytesRead } = await fileHandle.read(airBuffer, 0, maxChunkSize, this.header.airImgOffset);

          // Find JPEG start and end markers
          const jpegData = this.extractJpegFromBuffer(airBuffer.subarray(0, bytesRead));

          if (!jpegData) {
            console.warn('No valid JPEG data found in air image');
            return null;
          }

          if (raw) {
            // Decompress JPEG to raw bitmap data
            return await this.decompressImageData(jpegData, ImageType.NAVIGATE);
          } else {
            // Return JPEG data directly
            return jpegData;
          }
        } finally {
          await fileHandle.close();
        }
      } catch (error) {
        console.error('Failed to get navigate image:', error.message);
        return null;
      }
    });
  }

  /**
   * Get macro image (tissue region extracted from macro-label image using iViewerSDK algorithm)
   * @param {boolean} raw - If true, return raw bitmap data; if false, return compressed data
   * @returns {Promise<Buffer|null>} Macro image data
   */
  async getMacroImage(raw = true) {
    return await timeOperation('getMacroImage', async () => {
      try {
        // Get the macro-label image as raw bitmap data
        const macroLabelRawData = await this.getMacroLabelImage(true);
        if (!macroLabelRawData) {
          return null;
        }

        // Get macro-label image dimensions
        const macroExtData = this.getExtensionData(ExtensionType.MACRO_IMAGE);
        if (!macroExtData) {
          console.warn('No macro image extension data found');
          return null;
        }

        const macroJpeg = this.extractJpegFromExtensionData(macroExtData.data);
        if (!macroJpeg) {
          console.warn('No valid JPEG data found in macro extension');
          return null;
        }

        const macroMetadata = await sharp(macroJpeg).metadata();
        const macroWidth = macroMetadata.width;
        const macroHeight = macroMetadata.height;
        const channels = macroMetadata.channels;

        if (macroWidth <= 0 || macroHeight <= 0 || channels < 1) {
          return null;
        }

        // Use iViewerSDK ExtractMacro algorithm
        const macroData = await iViewerExtractMacro(
          macroLabelRawData, macroWidth, macroHeight, channels
        );

        if (!macroData) {
          return null;
        }

        if (raw) {
          return macroData.buffer;
        } else {
          // Convert to JPEG format
          const depth = channels * 8;
          return await this.convertToJpeg(
            macroData.buffer, macroData.width, macroData.height, depth
          );
        }
      } catch (error) {
        console.error('Failed to extract macro image:', error.message);
        return null;
      }
    });
  }

  /**
   * Get label image (extracted from macro image)
   * @param {boolean} raw - If true, return raw bitmap data; if false, return compressed data
   * @returns {Promise<Buffer|null>} Label image data
   */
  async getLabelImage(raw = true) {
    return await timeOperation('getLabelImage', async () => {
      try {
        // First get the macro-label image as raw bitmap data
        const macroLabelRawData = await this.getMacroLabelImage(true);
        if (!macroLabelRawData) {
          return null;
        }

        // Get macro-label image dimensions from extension data
        const macroExtData = this.getExtensionData(ExtensionType.MACRO_IMAGE);
        if (!macroExtData) {
          console.warn('No macro image extension data found for label extraction');
          return null;
        }

        // Get macro-label image metadata from sharp
        const macroJpeg = this.extractJpegFromExtensionData(macroExtData.data);
        if (!macroJpeg) {
          console.warn('No valid JPEG data found in macro extension');
          return null;
        }

        const macroMetadata = await sharp(macroJpeg).metadata();
        const macroWidth = macroMetadata.width;
        const macroHeight = macroMetadata.height;
        const channels = macroMetadata.channels;

        if (macroWidth <= 0 || macroHeight <= 0 || channels < 1) {
          return null;
        }

        // Use iViewerSDK ExtractLabel algorithm
        const labelData = await iViewerExtractLabel(
          macroLabelRawData, macroWidth, macroHeight, channels
        );

        if (!labelData) {
          return null;
        }

        if (raw) {
          return labelData.buffer;
        } else {
          // Convert to JPEG format
          const depth = channels * 8; // Convert channels back to depth
          return await this.convertToJpeg(
            labelData.buffer, labelData.width, labelData.height, depth
          );
        }
      } catch (error) {
        console.error('Failed to extract label image:', error.message);
        return null;
      }
    });
  }

  /**
   * Get macro+label combined image (original extension data)
   * @param {boolean} raw - If true, return raw bitmap data; if false, return compressed data
   * @returns {Promise<Buffer|null>} Combined macro and label image data
   */
  async getMacroLabelImage(raw = true) {
    // This is the original macro image from extension data (contains both macro and label)
    const extData = this.getExtensionData(ExtensionType.MACRO_IMAGE);
    if (extData && extData.data) {
      // Extract JPEG data from TMAP6 extension format
      const jpegData = this.extractJpegFromExtensionData(extData.data);

      if (raw && jpegData) {
        return await this.decompressImageData(jpegData, ImageType.MACRO_LABEL);
      } else if (jpegData) {
        return jpegData;
      } else {
        // Fallback to original data if JPEG extraction fails
        return extData.data;
      }
    }
    return null;
  }









  /**
   * Extract JPEG data from TMAP6 extension data format
   * @param {Buffer} extensionData - Raw extension data
   * @returns {Buffer|null} JPEG data or null if not found
   */
  extractJpegFromExtensionData(extensionData) {
    try {
      // Based on iViewerSDK code, the structure is:
      // - First 8 bytes: some header info
      // - Next 20-24 bytes: IS_IMAGE_INFO structure
      // - Remaining data: JPEG image data

      // Skip the header and IS_IMAGE_INFO structure
      const headerSize = 8;
      const imageInfoSize = 24; // 20 bytes for x86, but we use 24 to be safe
      const jpegStartOffset = headerSize + imageInfoSize;

      if (extensionData.length <= jpegStartOffset) {
        return null;
      }

      // Extract JPEG data
      const jpegData = Buffer.from(extensionData.subarray(jpegStartOffset));

      // Verify it's actually JPEG data (starts with 0xFF 0xD8)
      if (jpegData.length >= 2 && jpegData[0] === 0xFF && jpegData[1] === 0xD8) {
        return jpegData;
      }

      // If not JPEG, try different offsets
      for (let offset = 0; offset < Math.min(extensionData.length - 2, 64); offset++) {
        if (extensionData[offset] === 0xFF && extensionData[offset + 1] === 0xD8) {
          return Buffer.from(extensionData.subarray(offset));
        }
      }

      return null;
    } catch (error) {
      console.error('Failed to extract JPEG from extension data:', error.message);
      return null;
    }
  }

  /**
   * Extract JPEG data from buffer (for air image data)
   * @param {Buffer} buffer - Raw buffer data
   * @returns {Buffer|null} JPEG data or null if not found
   */
  extractJpegFromBuffer(buffer) {
    try {
      // Look for JPEG start marker (0xFF 0xD8)
      for (let i = 0; i < buffer.length - 1; i++) {
        if (buffer[i] === 0xFF && buffer[i + 1] === 0xD8) {
          // Found JPEG start, now find the end marker (0xFF 0xD9)
          for (let j = i + 2; j < buffer.length - 1; j++) {
            if (buffer[j] === 0xFF && buffer[j + 1] === 0xD9) {
              // Found JPEG end, extract the complete JPEG data
              return Buffer.from(buffer.subarray(i, j + 2));
            }
          }
          // If no end marker found, return from start to end of buffer
          return Buffer.from(buffer.subarray(i));
        }
      }
      return null;
    } catch (error) {
      console.error('Failed to extract JPEG from buffer:', error.message);
      return null;
    }
  }

  /**
   * Decompress image data from JPEG to raw bitmap
   * @param {Buffer} compressedData - Compressed image data (JPEG)
   * @param {number} imageType - Image type for getting dimensions
   * @returns {Promise<Buffer>} Raw bitmap data
   */
  async decompressImageData(compressedData, imageType) {
    return await timeOperation('decompressImageData', async () => {
      try {
        // Use sharp to decode JPEG and get raw pixel data
        const image = sharp(compressedData);

        // Get raw pixel data as RGB buffer
        const rawBuffer = await image
          .raw()
          .toBuffer();

        return rawBuffer;
      } catch (error) {
        console.error('Failed to decompress image data:', error.message);
        // If decompression fails, return the original compressed data
        return compressedData;
      }
    });
  }

  /**
   * Convert raw bitmap data to JPEG format
   * @param {Buffer} rawData - Raw bitmap data
   * @param {number} width - Image width
   * @param {number} height - Image height
   * @param {number} depth - Color depth (bits per pixel)
   * @returns {Promise<Buffer>} JPEG data
   */
  async convertToJpeg(rawData, width, height, depth) {
    return await timeOperation('convertToJpeg', async () => {
      try {
        const channels = depth / 8;
        let image;

        if (channels === 3) {
          // RGB data
          image = sharp(rawData, {
            raw: {
              width,
              height,
              channels: 3
            }
          });
        } else if (channels === 1) {
          // Grayscale data
          image = sharp(rawData, {
            raw: {
              width,
              height,
              channels: 1
            }
          });
        } else {
          throw new Error(`Unsupported channel count: ${channels}`);
        }

        return await image.jpeg({ quality: 85 }).toBuffer();
      } catch (error) {
        console.error('Failed to convert to JPEG:', error.message);
        throw error;
      }
    });
  }

  /**
   * Extract macro (tissue) region from macro-label image using iViewerSDK algorithm
   * @param {Buffer} macroLabelData - Raw macro-label image data
   * @param {number} width - Image width
   * @param {number} height - Image height
   * @param {number} channels - Number of channels (1 or 3)
   * @returns {Promise<object|null>} Macro data with buffer, width, height
   */
  async extractMacroFromMacroLabel(macroLabelData, width, height, channels) {
    return await timeOperation('extractMacroFromMacroLabel', async () => {
      try {
        // Based on the iViewerSDK ExtractMacro function
        // This algorithm finds the tissue region (opposite of label region)

        // Calculate scale for analysis
        const scale = 4; // Fixed scale as in iViewerSDK
        const subWidth = Math.floor(width / scale);
        const subHeight = Math.floor(height / scale);

        // Create downsampled grayscale image for analysis
        const grayData = Buffer.alloc(subWidth * subHeight);

        // Downsample and convert to grayscale
        for (let y = 0; y < subHeight; y++) {
          for (let x = 0; x < subWidth; x++) {
            const srcX = Math.floor(x * scale);
            const srcY = Math.floor(y * scale);
            const srcIndex = (srcY * width + srcX) * channels;

            let gray;
            if (channels === 3) {
              // RGB to grayscale conversion
              const r = macroLabelData[srcIndex];
              const g = macroLabelData[srcIndex + 1];
              const b = macroLabelData[srcIndex + 2];
              gray = Math.floor(0.299 * r + 0.587 * g + 0.114 * b);
            } else {
              gray = macroLabelData[srcIndex];
            }

            grayData[y * subWidth + x] = gray;
          }
        }

        // Calculate column projections to find boundaries
        const colProjections = new Array(subWidth).fill(0);
        for (let x = 0; x < subWidth; x++) {
          for (let y = 0; y < subHeight; y++) {
            colProjections[x] += grayData[y * subWidth + x];
          }
        }

        // Find the boundary between label and macro regions
        // Based on iViewerSDK ExtractMacro algorithm

        // Calculate gradient-based column projections (like iViewerSDK)
        const gradientProjections = new Array(subWidth).fill(0);
        const startCol = Math.max(8, Math.floor(subWidth / 8));
        const endCol = subWidth - startCol;

        // Calculate horizontal gradients in the middle region
        for (let y = Math.floor(subHeight / 8); y < subHeight - Math.floor(subHeight / 8); y++) {
          for (let x = startCol; x < endCol; x++) {
            const current = grayData[y * subWidth + x];
            const next = grayData[y * subWidth + x + 1];
            gradientProjections[x] += Math.abs(current - next);
          }
        }

        // Find the maximum gradient (boundary)
        let maxGradient = 0;
        let boundaryIndex = Math.floor(subWidth / 2); // Default to middle
        for (let x = startCol; x < endCol; x++) {
          if (gradientProjections[x] > maxGradient) {
            maxGradient = gradientProjections[x];
            boundaryIndex = x;
          }
        }

        // Determine if label is on left or right by comparing left and right sums
        let leftSum = 0, rightSum = 0;
        const thirdWidth = Math.floor(subWidth / 3);
        for (let i = 0; i < thirdWidth; i++) {
          leftSum += gradientProjections[i] || 0;
          rightSum += gradientProjections[subWidth - i - 1] || 0;
        }
        const labelOnLeft = leftSum > rightSum;

        // Extract macro region (opposite of label)
        let macroLeft, macroRight;
        if (labelOnLeft) {
          // Label is on left, macro is on right
          macroLeft = (boundaryIndex + 1) * scale;
          macroRight = width;
        } else {
          // Label is on right, macro is on left
          macroLeft = 0;
          macroRight = (boundaryIndex - 1) * scale;
        }

        const macroWidth = macroRight - macroLeft;

        if (macroWidth <= 0) {
          // No clear boundary found, return right 2/3 of the image as fallback
          const fallbackWidth = Math.floor(width * 2 / 3);
          const fallbackLeft = width - fallbackWidth;

          const macroData = Buffer.alloc(fallbackWidth * height * channels);
          for (let y = 0; y < height; y++) {
            const srcOffset = y * width * channels + fallbackLeft * channels;
            const dstOffset = y * fallbackWidth * channels;
            macroLabelData.copy(macroData, dstOffset, srcOffset, srcOffset + fallbackWidth * channels);
          }

          return {
            buffer: macroData,
            width: fallbackWidth,
            height: height
          };
        }

        // Extract the macro region with original colors
        const macroData = Buffer.alloc(macroWidth * height * channels);
        for (let y = 0; y < height; y++) {
          const srcOffset = y * width * channels + macroLeft * channels;
          const dstOffset = y * macroWidth * channels;
          macroLabelData.copy(macroData, dstOffset, srcOffset, srcOffset + macroWidth * channels);
        }

        return {
          buffer: macroData,
          width: macroWidth,
          height: height
        };
      } catch (error) {
        console.error('Failed to extract macro from macro-label:', error.message);
        return null;
      }
    });
  }

  /**
   * Extract label region from macro image using iViewerSDK algorithm
   * @param {Buffer} macroData - Raw macro image data
   * @param {number} width - Image width
   * @param {number} height - Image height
   * @param {number} channels - Number of channels (1 or 3)
   * @returns {Promise<object|null>} Label data with buffer, width, height
   */
  async extractLabelFromMacro(macroData, width, height, channels) {
    return await timeOperation('extractLabelFromMacro', async () => {
      try {
        // Based on the iViewerSDK ExtractLabel function
        // This algorithm finds label boundaries by analyzing column projections

        // Calculate scale to keep analysis manageable
        let scale = 1;
        let tempHeight = height;
        while (tempHeight >= 256) {
          tempHeight = height / scale;
          scale++;
        }
        if (scale > 1) {
          scale--;
        }

        const subWidth = Math.floor(width / scale);
        const subHeight = Math.floor(height / scale);

        // Create downsampled grayscale image for analysis
        const grayData = Buffer.alloc(subWidth * subHeight);

        // Downsample and convert to grayscale
        for (let y = 0; y < subHeight; y++) {
          for (let x = 0; x < subWidth; x++) {
            const srcX = Math.floor(x * scale);
            const srcY = Math.floor(y * scale);
            const srcIndex = (srcY * width + srcX) * channels;

            let gray;
            if (channels === 3) {
              // RGB to grayscale conversion
              const r = macroData[srcIndex];
              const g = macroData[srcIndex + 1];
              const b = macroData[srcIndex + 2];
              gray = Math.floor(0.299 * r + 0.587 * g + 0.114 * b);
            } else {
              gray = macroData[srcIndex];
            }

            grayData[y * subWidth + x] = gray;
          }
        }

        // Calculate column projections to find label boundaries
        const colProjections = new Array(subWidth).fill(0);
        for (let x = 0; x < subWidth; x++) {
          for (let y = 0; y < subHeight; y++) {
            colProjections[x] += grayData[y * subWidth + x];
          }
        }

        // Find the label region boundaries
        // Based on iViewerSDK ExtractLabel algorithm

        // Calculate gradient-based column projections (like iViewerSDK)
        const gradientProjections = new Array(subWidth).fill(0);
        const startCol = Math.max(8, Math.floor(subWidth / 8));
        const endCol = subWidth - startCol;

        // Calculate horizontal gradients in the middle region
        for (let y = Math.floor(subHeight / 8); y < subHeight - Math.floor(subHeight / 8); y++) {
          for (let x = startCol; x < endCol; x++) {
            const current = grayData[y * subWidth + x];
            const next = grayData[y * subWidth + x + 1];
            gradientProjections[x] += Math.abs(current - next);
          }
        }

        // Find the maximum gradient (boundary)
        let maxGradient = 0;
        let boundaryIndex = Math.floor(subWidth / 2); // Default to middle
        for (let x = startCol; x < endCol; x++) {
          if (gradientProjections[x] > maxGradient) {
            maxGradient = gradientProjections[x];
            boundaryIndex = x;
          }
        }

        // Determine if label is on left or right by comparing left and right sums
        let leftSum = 0, rightSum = 0;
        const thirdWidth = Math.floor(subWidth / 3);
        for (let i = 0; i < thirdWidth; i++) {
          leftSum += gradientProjections[i] || 0;
          rightSum += gradientProjections[subWidth - i - 1] || 0;
        }
        const labelOnLeft = leftSum > rightSum;

        // Extract label region based on boundary detection
        let labelLeft, labelRight;
        if (labelOnLeft) {
          // Label is on left side
          labelLeft = 0;
          labelRight = (boundaryIndex - 1) * scale;
        } else {
          // Label is on right side
          labelLeft = (boundaryIndex + 1) * scale;
          labelRight = width;
        }

        const labelWidth = labelRight - labelLeft;

        if (labelWidth <= 0) {
          // No label region found, return left 1/3 of the image as fallback
          const fallbackWidth = Math.min(width / 3, 400);
          const fallbackLeft = 0; // Always take from left side for label

          const labelData = Buffer.alloc(fallbackWidth * height * channels);
          for (let y = 0; y < height; y++) {
            const srcOffset = y * width * channels + fallbackLeft * channels;
            const dstOffset = y * fallbackWidth * channels;
            macroData.copy(labelData, dstOffset, srcOffset, srcOffset + fallbackWidth * channels);
          }

          return {
            buffer: labelData,
            width: fallbackWidth,
            height: height
          };
        }

        // Extract the label region with original colors
        const labelData = Buffer.alloc(labelWidth * height * channels);
        for (let y = 0; y < height; y++) {
          const srcOffset = y * width * channels + labelLeft * channels;
          const dstOffset = y * labelWidth * channels;
          const copyLength = Math.min(labelWidth * channels, macroData.length - srcOffset);
          if (copyLength > 0 && srcOffset + copyLength <= macroData.length) {
            macroData.copy(labelData, dstOffset, srcOffset, srcOffset + copyLength);
          }
        }

        return {
          buffer: labelData,
          width: labelWidth,
          height: height
        };
      } catch (error) {
        console.error('Failed to extract label from macro:', error.message);
        return null;
      }
    });
  }

  /**
   * Get image information for specified type
   * @param {number} imageType - Image type
   * @returns {object|null} Image information (width, height, depth)
   */
  getImageInfo(imageType) {
    switch (imageType) {
      case ImageType.THUMBNAIL:
        // Thumbnail is typically <= 256x256
        return { width: 256, height: 256, depth: this.header.imgColor };

      case ImageType.NAVIGATE:
        // Navigate image dimensions from header
        return {
          width: this.header.airImgWidth,
          height: this.header.airImgHeight,
          depth: this.header.imgColor
        };

      case ImageType.MACRO:
      case ImageType.MACRO_LABEL:
        // Macro image dimensions from header
        return {
          width: this.header.airImgWidth,
          height: this.header.airImgHeight,
          depth: this.header.imgColor
        };

      case ImageType.LABEL:
        // Label is extracted and scaled down
        const macroWidth = this.header.airImgWidth;
        const macroHeight = this.header.airImgHeight;
        let scale = 1;
        let tempHeight = macroHeight;
        while (tempHeight >= 256) {
          tempHeight = macroHeight / scale;
          scale++;
        }
        if (scale > 1) scale--;

        return {
          width: Math.floor(macroWidth / scale),
          height: Math.floor(macroHeight / scale),
          depth: this.header.imgColor
        };

      default:
        return null;
    }
  }
}
