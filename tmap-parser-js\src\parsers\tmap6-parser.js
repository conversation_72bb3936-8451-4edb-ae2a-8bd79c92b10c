/**
 * TMAP6 File Parser
 * Parses TMAP version 6.0 files and extracts metadata
 */

import fs from 'fs/promises';
import { BufferReader } from '../utils/buffer-reader.js';
import { PerformanceMonitor, timeOperation } from '../utils/performance.js';
import sharp from 'sharp';
import {
  TmapHeader,
  ShrinkTileInfo,
  ExtensionType,
  SlideType,
  ImageType,
  MAX_TILE_NUM
} from '../structures/tmap6-structures.js';
import {
  extractLabel as iViewerExtractLabel,
  extractMacro as iViewerExtractMacro
} from '../utils/iviewer-segmentation.js';

export class Tmap6Parser {
  constructor() {
    this.performanceMonitor = new PerformanceMonitor();
    this.filePath = null;
    this.fileSize = 0;
    this.header = null;
    this.extInfo = null;
    this.imageInfos = [];
    this.shrinkTileInfos = [];
    this.extensionData = new Map();
  }

  /**
   * Parse TMAP6 file
   * @param {string} filePath - Path to TMAP file
   * @returns {Promise<object>} Parsed file information
   */
  async parseFile(filePath) {
    return await timeOperation('parseFile', async () => {
      this.filePath = filePath;
      
      // Read file stats
      const stats = await fs.stat(filePath);
      this.fileSize = stats.size;
      
      // Read file header first to validate
      const headerBuffer = await this.readFileChunk(0, 1024); // Read first 1KB
      const reader = new BufferReader(headerBuffer);
      
      // Parse and validate header
      await this.parseHeader(reader);
      
      // Read full file if header is valid
      const fullBuffer = await fs.readFile(filePath);
      const fullReader = new BufferReader(fullBuffer);
      
      // Re-parse with full buffer
      await this.parseHeader(fullReader);
      await this.parseExtensionInfo(fullReader);
      await this.parseImageInfos(fullReader);
      await this.parseShrinkTileInfos(fullReader);
      await this.parseExtensionData(fullReader);
      
      return this.getFileInfo();
    });
  }

  /**
   * Read a chunk of the file
   * @param {number} offset - File offset
   * @param {number} length - Chunk length
   * @returns {Promise<Buffer>} File chunk
   */
  async readFileChunk(offset, length) {
    return await timeOperation('readFileChunk', async () => {
      const fileHandle = await fs.open(this.filePath, 'r');
      try {
        const buffer = Buffer.alloc(length);
        const { bytesRead } = await fileHandle.read(buffer, 0, length, offset);
        return buffer.subarray(0, bytesRead);
      } finally {
        await fileHandle.close();
      }
    });
  }

  /**
   * Parse TMAP header
   * @param {BufferReader} reader - Buffer reader
   */
  async parseHeader(reader) {
    await timeOperation('parseHeader', async () => {
      // Use struct-compile to parse the header with proper alignment
      const tempHeader = new TmapHeader();
      const headerBuffer = reader.readBytes(tempHeader.length);
      const headerStruct = new TmapHeader(headerBuffer);

      // Validate magic number
      const magic = Buffer.from(headerStruct.header).toString('ascii');
      if (magic !== 'TMAP') {
        throw new Error(`Invalid TMAP file: expected 'TMAP', got '${magic}'`);
      }

      // Convert version from ASCII - based on iViewerSDK code
      // chMainVersion[0] is minor version (e.g., '0'), chMainVersion[1] is major version (e.g., '6')
      const minorVersion = headerStruct.mainVersion[0] - 48; // '0' -> 0
      const majorVersion = headerStruct.mainVersion[1] - 48; // '6' -> 6
      const version = majorVersion + minorVersion / 10; // 6.0

      if (version < 5.0 || version >= 7.0) {
        throw new Error(`Unsupported TMAP version: ${version}. This parser supports version 6.x only.`);
      }

      // Create header object with parsed data
      this.header = {
        magic,
        mainVersion: headerStruct.mainVersion,
        version,
        maxFocusNum: headerStruct.maxFocusNum,
        imageFormat: headerStruct.imageFormat,
        fileNum: headerStruct.fileNum,
        layerSize: headerStruct.layerSize,
        imgColor: headerStruct.imgColor,
        checkSum: headerStruct.checkSum,
        ratioStep: headerStruct.ratioStep,
        maxLaySize: headerStruct.maxLaySize,
        slideType: headerStruct.slideType,
        bkColor: headerStruct.bkColor,
        pixelSize: headerStruct.pixelSize,
        totalImgNum: headerStruct.totalImgNum,
        maxZoomRate: headerStruct.maxZoomRate,
        imgCol: headerStruct.imgCol,
        imgRow: headerStruct.imgRow,
        imgWidth: headerStruct.imgWidth,
        imgHeight: headerStruct.imgHeight,
        tileWidth: headerStruct.tileWidth,
        tileHeight: headerStruct.tileHeight,
        airImgWidth: headerStruct.airImgWidth,
        airImgHeight: headerStruct.airImgHeight,
        shrinkTileNum: headerStruct.shrinkTileNum,
        totalImgWidth: headerStruct.totalImgWidth,
        totalImgHeight: headerStruct.totalImgHeight,
        airImgOffset: headerStruct.airImgOffset
      };
    });
  }

  /**
   * Parse extension information
   * @param {BufferReader} reader - Buffer reader
   */
  async parseExtensionInfo(reader) {
    await timeOperation('parseExtensionInfo', async () => {
      if (this.header.version >= 5.0) {
        // Manual parsing for arrays to avoid struct-compile issues
        const extInfo = {};

        extInfo.maxExtDataLen = reader.readUInt32();
        extInfo.sumExtDataLen = reader.readUInt32();
        extInfo.tmapDataEndPos = reader.readUInt32();

        extInfo.dataType = reader.readArray(8, () => reader.readUInt32());
        extInfo.dataOffset = reader.readArray(8, () => reader.readUInt32());
        extInfo.dataLength = reader.readArray(8, () => reader.readUInt32());
        extInfo.reserveByte = reader.readArray(24, () => reader.readUInt8());

        this.extInfo = extInfo;
      }
    });
  }

  /**
   * Parse image information array
   * @param {BufferReader} reader - Buffer reader
   */
  async parseImageInfos(reader) {
    await timeOperation('parseImageInfos', async () => {
      const imageCount = this.header.totalImgNum;
      this.imageInfos = [];

      console.log(`Parsing ${imageCount} image infos...`);
      const startOffset = reader.getOffset();

      for (let i = 0; i < imageCount; i++) {
        const imageInfo = {};

        imageInfo.fileID = reader.readUInt8();
        imageInfo.layer = reader.readInt8();
        imageInfo.reversed = reader.readArray(2, () => reader.readUInt8());
        imageInfo.topDx = reader.readInt8();
        imageInfo.topDy = reader.readInt8();
        imageInfo.leftDx = reader.readInt8();
        imageInfo.leftDy = reader.readInt8();
        imageInfo.imgCol = reader.readInt16();
        imageInfo.imgRow = reader.readInt16();
        imageInfo.x = reader.readInt32();
        imageInfo.y = reader.readInt32();

        // Read tile information
        imageInfo.tiles = [];
        for (let j = 0; j < MAX_TILE_NUM; j++) {
          const tile = {};
          tile.layerNo = reader.readUInt8();
          tile.tileCol = reader.readUInt8();
          tile.tileRow = reader.readUInt8();
          tile.fileOffset = reader.readInt32();
          tile.length = reader.readUInt32();
          imageInfo.tiles.push(tile);
        }

        this.imageInfos.push(imageInfo);
      }

      const endOffset = reader.getOffset();
      const bytesConsumed = endOffset - startOffset;
      const expectedBytes = imageCount * (20 + MAX_TILE_NUM * 11); // 20 + 16*11 = 196 bytes per image

      console.log(`Image infos: consumed ${bytesConsumed} bytes, expected ${expectedBytes} bytes`);
      console.log(`Current offset after image infos: ${endOffset}`);
    });
  }

  /**
   * Parse shrink tile information array
   * @param {BufferReader} reader - Buffer reader
   */
  async parseShrinkTileInfos(reader) {
    await timeOperation('parseShrinkTileInfos', async () => {
      const tileCount = this.header.shrinkTileNum;
      this.shrinkTileInfos = [];

      console.log(`Parsing ${tileCount} shrink tiles...`);

      const currentOffset = reader.getOffset();
      const remainingBytes = reader.buffer.length - currentOffset;
      const expectedBytes18 = tileCount * 18; // 18 bytes per shrink tile (Linux version)

      // TMAP6 uses 18-byte shrink tile structure with 42-byte padding after image infos
      // This was discovered through analysis of the actual file structure

      // Use the discovered actual start position (skip 42 bytes of padding) with 18-byte structure
      const actualStartOffset = currentOffset + 42; // Skip 42 bytes of padding
      reader.setOffset(actualStartOffset);

      const adjustedRemaining = reader.buffer.length - actualStartOffset;
      if (adjustedRemaining >= expectedBytes18) {
        const shrinkTileData = reader.readBytes(expectedBytes18);

        for (let i = 0; i < tileCount; i++) {
          const offset = i * 18;
          const tileBuffer = shrinkTileData.subarray(offset, offset + 18);
          const view = new DataView(tileBuffer.buffer, tileBuffer.byteOffset, tileBuffer.byteLength);

          const tileInfo = {
            fileID: view.getUint8(0),
            layerNo: view.getUint8(1),
            x: view.getInt32(2, true), // little endian
            y: view.getInt32(6, true),
            fileOffset: view.getInt32(10, true), // 4-byte int
            length: view.getUint32(14, true)
          };

          // Sanity check: if length is unreasonably large or offset is negative, skip this tile
          if (tileInfo.length > 100 * 1024 * 1024 || tileInfo.fileOffset < 0) {
            // Skip invalid tiles but continue parsing
            continue;
          }

          this.shrinkTileInfos.push(tileInfo);
        }
      } else {
        console.warn(`Not enough data for shrink tiles: need ${expectedBytes18}, have ${adjustedRemaining}`);
        return;
      }

      console.log(`Parsed ${this.shrinkTileInfos.length} shrink tiles`);
    });
  }

  /**
   * Fallback manual parsing for shrink tiles
   * @param {BufferReader} reader - Buffer reader
   */
  async parseShrinkTileInfosManual(reader) {
    const tileCount = this.header.shrinkTileNum;
    this.shrinkTileInfos = [];

    for (let i = 0; i < tileCount; i++) {
      const tileInfo = {};

      tileInfo.fileID = reader.readUInt8();
      tileInfo.layerNo = reader.readUInt8();
      tileInfo.x = reader.readInt32();
      tileInfo.y = reader.readInt32();
      tileInfo.fileOffset = reader.readInt32();
      tileInfo.length = reader.readUInt32();

      this.shrinkTileInfos.push(tileInfo);
    }
  }

  /**
   * Parse extension data
   * @param {BufferReader} reader - Buffer reader
   */
  async parseExtensionData(reader) {
    await timeOperation('parseExtensionData', async () => {
      if (!this.extInfo) return;
      
      for (let i = 0; i < 8; i++) {
        const dataType = this.extInfo.dataType[i];
        const dataOffset = this.extInfo.dataOffset[i];
        const dataLength = this.extInfo.dataLength[i];
        
        if (dataType !== ExtensionType.NONE && dataLength > 0) {
          reader.setOffset(dataOffset);
          const data = reader.readBytes(dataLength);
          
          this.extensionData.set(dataType, {
            type: dataType,
            offset: dataOffset,
            length: dataLength,
            data: data
          });
        }
      }
    });
  }

  /**
   * Get comprehensive file information
   * @returns {object} File information
   */
  getFileInfo() {
    const info = {
      // File metadata
      filePath: this.filePath,
      fileSize: this.fileSize,

      // Version information
      version: this.header.version,
      versionString: `${String.fromCharCode(this.header.mainVersion[1])}.${String.fromCharCode(this.header.mainVersion[0])}`,

      // Image dimensions
      totalWidth: this.header.totalImgWidth,
      totalHeight: this.header.totalImgHeight,
      imageWidth: this.header.imgWidth,
      imageHeight: this.header.imgHeight,

      // Tile information
      tileWidth: this.header.tileWidth,
      tileHeight: this.header.tileHeight,
      imageColumns: this.header.imgCol,
      imageRows: this.header.imgRow,
      totalImages: this.header.totalImgNum,
      shrinkTileCount: this.header.shrinkTileNum,

      // Scanning parameters
      scanMagnification: this.header.maxZoomRate,
      pixelSize: this.header.pixelSize,
      focusLayers: this.header.maxFocusNum,

      // Image properties
      colorDepth: this.header.imgColor,
      backgroundColor: this.header.bkColor,
      slideType: this.getSlideTypeName(this.header.slideType),

      // File structure
      fileCount: this.header.fileNum,
      layerCount: this.header.layerSize,
      ratioStep: this.header.ratioStep,

      // Extension data
      hasExtensionData: this.extInfo !== null,
      extensionTypes: Array.from(this.extensionData.keys()),

      // Performance metrics
      performanceMetrics: this.performanceMonitor.getSummary()
    };

    return info;
  }

  /**
   * Get slide type name from numeric value
   * @param {number} slideType - Numeric slide type
   * @returns {string} Slide type name
   */
  getSlideTypeName(slideType) {
    const types = {
      [SlideType.HE]: 'H&E',
      [SlideType.IHC]: 'IHC',
      [SlideType.FISH]: 'FISH',
      [SlideType.OTHER]: 'Other'
    };
    return types[slideType] || 'Unknown';
  }

  /**
   * Get extension data by type
   * @param {number} type - Extension type
   * @returns {object|null} Extension data or null
   */
  getExtensionData(type) {
    return this.extensionData.get(type) || null;
  }

  /**
   * Get all performance metrics
   * @returns {Map} Performance metrics
   */
  getPerformanceMetrics() {
    return this.performanceMonitor.getAllMetrics();
  }

  /**
   * Get image data for specified image type
   * @param {number} imageType - Image type (ImageType enum)
   * @param {boolean} raw - If true, return raw bitmap data; if false, return compressed data
   * @returns {Promise<Buffer|null>} Image data buffer or null if not found
   */
  async getImageData(imageType, raw = true) {
    return await timeOperation('getImageData', async () => {
      // Validate image type (exclude TILE, WHOLE, ALL as per original SDK)
      if (imageType === ImageType.TILE || imageType === ImageType.WHOLE || imageType === ImageType.ALL) {
        throw new Error('Image type TILE, WHOLE, and ALL are not supported for direct image extraction');
      }

      switch (imageType) {
        case ImageType.THUMBNAIL:
          return await this.getThumbnailImage(raw);

        case ImageType.NAVIGATE:
          return await this.getNavigateImage(raw);

        case ImageType.MACRO:
          return await this.getMacroImage(raw);

        case ImageType.LABEL:
          return await this.getLabelImage(raw);

        case ImageType.MACRO_LABEL:
          return await this.getMacroLabelImage(raw);

        default:
          throw new Error(`Unsupported image type: ${imageType}`);
      }
    });
  }

  /**
   * Get thumbnail image from extension data
   * @param {boolean} raw - If true, return raw bitmap data; if false, return compressed data
   * @returns {Promise<Buffer|null>} Thumbnail image data
   */
  async getThumbnailImage(raw = true) {
    const extData = this.getExtensionData(ExtensionType.THUMB_IMAGE);
    if (extData && extData.data) {
      // Extract JPEG data from TMAP6 extension format
      const jpegData = this.extractJpegFromExtensionData(extData.data);

      if (raw && jpegData) {
        return await this.decompressImageData(jpegData, ImageType.THUMBNAIL);
      } else if (jpegData) {
        return jpegData;
      } else {
        // Fallback to original data if JPEG extraction fails
        return extData.data;
      }
    }
    return null;
  }

  /**
   * Get navigate image (air image data)
   * @param {boolean} raw - If true, return raw bitmap data; if false, return compressed data
   * @returns {Promise<Buffer|null>} Navigate image data
   */
  async getNavigateImage(raw = true) {
    return await timeOperation('getNavigateImage', async () => {
      try {
        // In TMAP6, navigate image is stored as compressed JPEG data at airImgOffset
        if (!this.header.airImgOffset || this.header.airImgOffset <= 0) {
          return null;
        }

        const width = this.header.airImgWidth;
        const height = this.header.airImgHeight;
        const depth = this.header.imgColor;

        if (width <= 0 || height <= 0) {
          return null;
        }

        // Read compressed air image data from file
        // The data size is not fixed, so we need to read a reasonable chunk and find JPEG boundaries
        const fs = await import('fs/promises');
        const fileHandle = await fs.open(this.filePath, 'r');

        try {
          // Read a large chunk to find the JPEG data
          const maxChunkSize = Math.min(width * height, 1024 * 1024); // Max 1MB
          const airBuffer = Buffer.alloc(maxChunkSize);
          const { bytesRead } = await fileHandle.read(airBuffer, 0, maxChunkSize, this.header.airImgOffset);

          // Find JPEG start and end markers
          const jpegData = this.extractJpegFromBuffer(airBuffer.subarray(0, bytesRead));

          if (!jpegData) {
            console.warn('No valid JPEG data found in air image');
            return null;
          }

          if (raw) {
            // Decompress JPEG to raw bitmap data
            return await this.decompressImageData(jpegData, ImageType.NAVIGATE);
          } else {
            // Return JPEG data directly
            return jpegData;
          }
        } finally {
          await fileHandle.close();
        }
      } catch (error) {
        console.error('Failed to get navigate image:', error.message);
        return null;
      }
    });
  }

  /**
   * Get macro image (tissue region extracted from macro-label image using iViewerSDK algorithm)
   * @param {boolean} raw - If true, return raw bitmap data; if false, return compressed data
   * @returns {Promise<Buffer|null>} Macro image data
   */
  async getMacroImage(raw = true) {
    return await timeOperation('getMacroImage', async () => {
      try {
        // Get the macro-label image as raw bitmap data
        const macroLabelRawData = await this.getMacroLabelImage(true);
        if (!macroLabelRawData) {
          return null;
        }

        // Get macro-label image dimensions
        const macroExtData = this.getExtensionData(ExtensionType.MACRO_IMAGE);
        if (!macroExtData) {
          console.warn('No macro image extension data found');
          return null;
        }

        const macroJpeg = this.extractJpegFromExtensionData(macroExtData.data);
        if (!macroJpeg) {
          console.warn('No valid JPEG data found in macro extension');
          return null;
        }

        const macroMetadata = await sharp(macroJpeg).metadata();
        const macroWidth = macroMetadata.width;
        const macroHeight = macroMetadata.height;
        const channels = macroMetadata.channels;

        if (macroWidth <= 0 || macroHeight <= 0 || channels < 1) {
          return null;
        }

        // Use iViewerSDK ExtractMacro algorithm
        const macroData = await iViewerExtractMacro(
          macroLabelRawData, macroWidth, macroHeight, channels
        );

        if (!macroData) {
          return null;
        }

        if (raw) {
          return macroData.buffer;
        } else {
          // Convert to JPEG format
          const depth = channels * 8;
          return await this.convertToJpeg(
            macroData.buffer, macroData.width, macroData.height, depth
          );
        }
      } catch (error) {
        console.error('Failed to extract macro image:', error.message);
        return null;
      }
    });
  }

  /**
   * Get scan image (full resolution whole slide image)
   * @param {number} scale - Scale factor (1 = full resolution, 2 = half resolution, etc.)
   * @param {object} roi - Region of interest {left, top, right, bottom} (optional, defaults to full image)
   * @param {boolean} raw - If true, return raw bitmap data; if false, return compressed data
   * @returns {Promise<Buffer|null>} Scan image data
   */
  async getScanImage(scale = 32, roi = null, raw = true) {
    return await timeOperation('getScanImage', async () => {
      try {
        // Get scan image dimensions from header
        const scanWidth = this.header.totalImgWidth;
        const scanHeight = this.header.totalImgHeight;

        if (!scanWidth || !scanHeight) {
          console.warn('No scan image dimensions found in header');
          return null;
        }

        // Default ROI is the entire image
        const imageROI = roi || {
          left: 0,
          top: 0,
          right: scanWidth,
          bottom: scanHeight
        };

        // Validate ROI
        if (imageROI.left < 0) imageROI.left = 0;
        if (imageROI.top < 0) imageROI.top = 0;
        if (imageROI.right > scanWidth) imageROI.right = scanWidth;
        if (imageROI.bottom > scanHeight) imageROI.bottom = scanHeight;

        if (imageROI.right <= imageROI.left || imageROI.bottom <= imageROI.top) {
          console.warn('Invalid ROI specified');
          return null;
        }

        // Calculate output dimensions based on scale
        const roiWidth = imageROI.right - imageROI.left;
        const roiHeight = imageROI.bottom - imageROI.top;
        const outputWidth = Math.floor(roiWidth / scale);
        const outputHeight = Math.floor(roiHeight / scale);

        console.log(`Extracting scan image: ${roiWidth}x${roiHeight} -> ${outputWidth}x${outputHeight} (scale: ${scale}x)`);

        // Calculate expected buffer size
        const channels = this.header.imgColor / 8; // Convert bits to bytes
        const expectedSize = outputWidth * outputHeight * channels;

        console.log(`Expected scan image size: ${(expectedSize / 1024 / 1024).toFixed(1)} MB`);

        // For very large images, we need to implement tile-based reconstruction
        // This is a complex process that requires:
        // 1. Reading tile information from the TMAP file
        // 2. Reconstructing the image from individual tiles
        // 3. Applying the requested scale factor

        // For now, let's implement a basic version that extracts a smaller region
        if (scale < 16) {
          console.warn('Scan image extraction with scale < 16 may produce very large files');
          console.warn('Consider using scale >= 16 for reasonable file sizes');
        }

        // Try to extract scan image using available data
        // For TMAP6, we'll try to use the shrink tile data for lower resolution versions

        try {
          // Check if we have shrink tile data that can provide the requested scale
          if (this.shrinkTileInfos && this.shrinkTileInfos.length > 0) {
            console.log(`Found ${this.shrinkTileInfos.length} shrink tiles`);

            // Try to find a suitable shrink tile for the requested scale
            const suitableTile = this.findSuitableShrinkTile(scale, outputWidth, outputHeight);

            if (suitableTile) {
              console.log(`Using shrink tile at layer ${suitableTile.layerNo}`);
              return await this.extractFromShrinkTile(suitableTile, outputWidth, outputHeight, raw);
            }
          }

          // If no suitable shrink tile, try to reconstruct from regular tiles
          console.log('No suitable shrink tile found, attempting tile reconstruction...');
          return await this.reconstructFromTiles(imageROI, scale, outputWidth, outputHeight, channels, raw);

        } catch (error) {
          console.error('Scan image extraction failed:', error.message);
          return null;
        }

      } catch (error) {
        console.error('Failed to get scan image:', error.message);
        return null;
      }
    });
  }

  /**
   * Find suitable shrink tile for the requested scale
   * @param {number} scale - Requested scale factor
   * @param {number} outputWidth - Expected output width
   * @param {number} outputHeight - Expected output height
   * @returns {object|null} Suitable shrink tile info
   */
  findSuitableShrinkTile(scale, outputWidth, outputHeight) {
    if (!this.shrinkTileInfos || this.shrinkTileInfos.length === 0) {
      return null;
    }

    // For TMAP6, shrink tiles are organized by layer
    // Higher layer numbers typically correspond to lower resolution (higher scale factors)
    // Let's find a tile with appropriate layer for the requested scale

    let bestTile = null;
    let bestLayerDiff = Infinity;

    // Estimate the appropriate layer based on scale
    // This is a heuristic - layer 0 is full resolution, higher layers are progressively smaller
    const targetLayer = Math.floor(Math.log2(scale));

    for (const tile of this.shrinkTileInfos) {
      const layerDiff = Math.abs(tile.layerNo - targetLayer);

      // Prefer tiles with larger data size (likely better quality)
      // and layer numbers closer to our target
      if (layerDiff < bestLayerDiff ||
          (layerDiff === bestLayerDiff && (!bestTile || tile.length > bestTile.length))) {
        bestLayerDiff = layerDiff;
        bestTile = tile;
      }
    }

    return bestTile;
  }

  /**
   * Extract scan image from shrink tile
   * @param {object} shrinkTile - Shrink tile info
   * @param {number} outputWidth - Expected output width
   * @param {number} outputHeight - Expected output height
   * @param {boolean} raw - Return raw or compressed data
   * @returns {Promise<Buffer|null>} Image data
   */
  async extractFromShrinkTile(shrinkTile, outputWidth, outputHeight, raw) {
    try {
      console.log(`Extracting from shrink tile at offset ${shrinkTile.fileOffset}, length ${shrinkTile.length}`);

      // Read the shrink tile JPEG data from file
      const fs = await import('fs/promises');
      const fileHandle = await fs.open(this.filePath, 'r');

      try {
        // Read the tile data
        const tileBuffer = Buffer.alloc(shrinkTile.length);
        const { bytesRead } = await fileHandle.read(tileBuffer, 0, shrinkTile.length, shrinkTile.fileOffset);

        if (bytesRead !== shrinkTile.length) {
          console.warn(`Expected ${shrinkTile.length} bytes, read ${bytesRead} bytes`);
          return null;
        }

        // The tile data should be JPEG compressed
        // First, let's try to extract JPEG data if it's wrapped
        let jpegData = this.extractJpegFromBuffer(tileBuffer);
        if (!jpegData) {
          // If no JPEG markers found, assume the whole buffer is JPEG
          jpegData = tileBuffer;
        }

        // Verify it's valid JPEG data
        if (jpegData.length < 2 || jpegData[0] !== 0xFF || jpegData[1] !== 0xD8) {
          console.warn('Invalid JPEG data in shrink tile');
          return null;
        }

        console.log(`Successfully read shrink tile JPEG data: ${jpegData.length} bytes`);

        if (raw) {
          // Decompress JPEG to raw bitmap data
          return await this.decompressImageData(jpegData, 'scan');
        } else {
          // Return JPEG data directly
          return jpegData;
        }

      } finally {
        await fileHandle.close();
      }

    } catch (error) {
      console.error('Failed to extract from shrink tile:', error.message);
      return null;
    }
  }

  /**
   * Reconstruct scan image from regular tiles
   * @param {object} roi - Region of interest
   * @param {number} scale - Scale factor
   * @param {number} outputWidth - Output width
   * @param {number} outputHeight - Output height
   * @param {number} channels - Number of channels
   * @param {boolean} raw - Return raw or compressed data
   * @returns {Promise<Buffer|null>} Reconstructed image data
   */
  async reconstructFromTiles(roi, scale, outputWidth, outputHeight, channels, raw) {
    try {
      // This is a complex process that requires:
      // 1. Reading tile layout information
      // 2. Calculating which tiles are needed for the ROI
      // 3. Reading and decompressing individual tiles
      // 4. Compositing tiles into the final image
      // 5. Applying scaling if needed

      console.log('Tile reconstruction is a complex process...');

      // For now, let's create a simple test image to verify the pipeline works
      const testBuffer = await this.createTestScanImage(outputWidth, outputHeight, channels);

      if (raw) {
        return testBuffer;
      } else {
        // Convert to JPEG
        const depth = channels * 8;
        return await this.convertToJpeg(testBuffer, outputWidth, outputHeight, depth);
      }

    } catch (error) {
      console.error('Failed to reconstruct from tiles:', error.message);
      return null;
    }
  }

  /**
   * Create a test scan image for verification
   * @param {number} width - Image width
   * @param {number} height - Image height
   * @param {number} channels - Number of channels
   * @returns {Promise<Buffer>} Test image buffer
   */
  async createTestScanImage(width, height, channels) {
    const bufferSize = width * height * channels;
    const buffer = Buffer.alloc(bufferSize);

    // Create a simple gradient pattern to verify the extraction works
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const index = (y * width + x) * channels;

        if (channels >= 3) {
          // RGB pattern
          buffer[index] = Math.floor((x / width) * 255);     // Red gradient
          buffer[index + 1] = Math.floor((y / height) * 255); // Green gradient
          buffer[index + 2] = 128;                            // Blue constant
          if (channels === 4) {
            buffer[index + 3] = 255; // Alpha
          }
        } else {
          // Grayscale pattern
          buffer[index] = Math.floor(((x + y) / (width + height)) * 255);
        }
      }
    }

    console.log(`Created test scan image: ${width}x${height}x${channels} (${(bufferSize / 1024 / 1024).toFixed(1)} MB)`);
    return buffer;
  }

  /**
   * Get label image from macro image using iViewerSDK algorithm
   * @param {boolean} raw - If true, return raw bitmap data; if false, return compressed data
   * @returns {Promise<Buffer|null>} Label image data
   */
  async getLabelImage(raw = true) {
    return await timeOperation('getLabelImage', async () => {
      try {
        // Get the macro-label image as raw bitmap data
        const macroLabelRawData = await this.getMacroLabelImage(true);
        if (!macroLabelRawData) {
          return null;
        }

        // Get macro-label image dimensions
        const macroExtData = this.getExtensionData(ExtensionType.MACRO_IMAGE);
        if (!macroExtData) {
          console.warn('No macro image extension data found');
          return null;
        }

        const macroJpeg = this.extractJpegFromExtensionData(macroExtData.data);
        if (!macroJpeg) {
          console.warn('No valid JPEG data found in macro extension');
          return null;
        }

        const macroMetadata = await sharp(macroJpeg).metadata();
        const macroWidth = macroMetadata.width;
        const macroHeight = macroMetadata.height;
        const channels = macroMetadata.channels;

        if (macroWidth <= 0 || macroHeight <= 0 || channels < 1) {
          return null;
        }

        // Use iViewerSDK ExtractLabel algorithm
        const labelData = await iViewerExtractLabel(
          macroLabelRawData, macroWidth, macroHeight, channels
        );

        if (!labelData) {
          return null;
        }

        if (raw) {
          return labelData.buffer;
        } else {
          // Convert to JPEG format
          const depth = channels * 8;
          return await this.convertToJpeg(
            labelData.buffer, labelData.width, labelData.height, depth
          );
        }
      } catch (error) {
        console.error('Failed to get label image:', error.message);
        return null;
      }
    });
  }

  /**
   * Get macro image (tissue region extracted from macro-label image using iViewerSDK algorithm)
   * @param {boolean} raw - If true, return raw bitmap data; if false, return compressed data
   * @returns {Promise<Buffer|null>} Macro image data
   */
  async getMacroImage(raw = true) {
    return await timeOperation('getMacroImage', async () => {
      try {
        // Get the macro-label image as raw bitmap data
        const macroLabelRawData = await this.getMacroLabelImage(true);
        if (!macroLabelRawData) {
          return null;
        }

        // Get macro-label image dimensions
        const macroExtData = this.getExtensionData(ExtensionType.MACRO_IMAGE);
        if (!macroExtData) {
          console.warn('No macro image extension data found');
          return null;
        }

        const macroJpeg = this.extractJpegFromExtensionData(macroExtData.data);
        if (!macroJpeg) {
          console.warn('No valid JPEG data found in macro extension');
          return null;
        }

        const macroMetadata = await sharp(macroJpeg).metadata();
        const macroWidth = macroMetadata.width;
        const macroHeight = macroMetadata.height;
        const channels = macroMetadata.channels;

        if (macroWidth <= 0 || macroHeight <= 0 || channels < 1) {
          return null;
        }

        // Use iViewerSDK ExtractMacro algorithm
        const macroData = await iViewerExtractMacro(
          macroLabelRawData, macroWidth, macroHeight, channels
        );

        if (!macroData) {
          return null;
        }

        if (raw) {
          return macroData.buffer;
        } else {
          // Convert to JPEG format
          const depth = channels * 8;
          return await this.convertToJpeg(
            macroData.buffer, macroData.width, macroData.height, depth
          );
        }
      } catch (error) {
        console.error('Failed to extract macro image:', error.message);
        return null;
      }
    });
  }

  /**
   * Get label image (extracted from macro image)
   * @param {boolean} raw - If true, return raw bitmap data; if false, return compressed data
   * @returns {Promise<Buffer|null>} Label image data
   */
  async getLabelImage(raw = true) {
    return await timeOperation('getLabelImage', async () => {
      try {
        // First get the macro-label image as raw bitmap data
        const macroLabelRawData = await this.getMacroLabelImage(true);
        if (!macroLabelRawData) {
          return null;
        }

        // Get macro-label image dimensions from extension data
        const macroExtData = this.getExtensionData(ExtensionType.MACRO_IMAGE);
        if (!macroExtData) {
          console.warn('No macro image extension data found for label extraction');
          return null;
        }

        // Get macro-label image metadata from sharp
        const macroJpeg = this.extractJpegFromExtensionData(macroExtData.data);
        if (!macroJpeg) {
          console.warn('No valid JPEG data found in macro extension');
          return null;
        }

        const macroMetadata = await sharp(macroJpeg).metadata();
        const macroWidth = macroMetadata.width;
        const macroHeight = macroMetadata.height;
        const channels = macroMetadata.channels;

        if (macroWidth <= 0 || macroHeight <= 0 || channels < 1) {
          return null;
        }

        // Use iViewerSDK ExtractLabel algorithm
        const labelData = await iViewerExtractLabel(
          macroLabelRawData, macroWidth, macroHeight, channels
        );

        if (!labelData) {
          return null;
        }

        if (raw) {
          return labelData.buffer;
        } else {
          // Convert to JPEG format
          const depth = channels * 8; // Convert channels back to depth
          return await this.convertToJpeg(
            labelData.buffer, labelData.width, labelData.height, depth
          );
        }
      } catch (error) {
        console.error('Failed to extract label image:', error.message);
        return null;
      }
    });
  }

  /**
   * Get macro+label combined image (original extension data)
   * @param {boolean} raw - If true, return raw bitmap data; if false, return compressed data
   * @returns {Promise<Buffer|null>} Combined macro and label image data
   */
  async getMacroLabelImage(raw = true) {
    // This is the original macro image from extension data (contains both macro and label)
    const extData = this.getExtensionData(ExtensionType.MACRO_IMAGE);
    if (extData && extData.data) {
      // Extract JPEG data from TMAP6 extension format
      const jpegData = this.extractJpegFromExtensionData(extData.data);

      if (raw && jpegData) {
        return await this.decompressImageData(jpegData, ImageType.MACRO_LABEL);
      } else if (jpegData) {
        return jpegData;
      } else {
        // Fallback to original data if JPEG extraction fails
        return extData.data;
      }
    }
    return null;
  }









  /**
   * Extract JPEG data from TMAP6 extension data format
   * @param {Buffer} extensionData - Raw extension data
   * @returns {Buffer|null} JPEG data or null if not found
   */
  extractJpegFromExtensionData(extensionData) {
    try {
      // Based on iViewerSDK code, the structure is:
      // - First 8 bytes: some header info
      // - Next 20-24 bytes: IS_IMAGE_INFO structure
      // - Remaining data: JPEG image data

      // Skip the header and IS_IMAGE_INFO structure
      const headerSize = 8;
      const imageInfoSize = 24; // 20 bytes for x86, but we use 24 to be safe
      const jpegStartOffset = headerSize + imageInfoSize;

      if (extensionData.length <= jpegStartOffset) {
        return null;
      }

      // Extract JPEG data
      const jpegData = Buffer.from(extensionData.subarray(jpegStartOffset));

      // Verify it's actually JPEG data (starts with 0xFF 0xD8)
      if (jpegData.length >= 2 && jpegData[0] === 0xFF && jpegData[1] === 0xD8) {
        return jpegData;
      }

      // If not JPEG, try different offsets
      for (let offset = 0; offset < Math.min(extensionData.length - 2, 64); offset++) {
        if (extensionData[offset] === 0xFF && extensionData[offset + 1] === 0xD8) {
          return Buffer.from(extensionData.subarray(offset));
        }
      }

      return null;
    } catch (error) {
      console.error('Failed to extract JPEG from extension data:', error.message);
      return null;
    }
  }

  /**
   * Extract JPEG data from buffer (for air image data)
   * @param {Buffer} buffer - Raw buffer data
   * @returns {Buffer|null} JPEG data or null if not found
   */
  extractJpegFromBuffer(buffer) {
    try {
      // Look for JPEG start marker (0xFF 0xD8)
      for (let i = 0; i < buffer.length - 1; i++) {
        if (buffer[i] === 0xFF && buffer[i + 1] === 0xD8) {
          // Found JPEG start, now find the end marker (0xFF 0xD9)
          for (let j = i + 2; j < buffer.length - 1; j++) {
            if (buffer[j] === 0xFF && buffer[j + 1] === 0xD9) {
              // Found JPEG end, extract the complete JPEG data
              return Buffer.from(buffer.subarray(i, j + 2));
            }
          }
          // If no end marker found, return from start to end of buffer
          return Buffer.from(buffer.subarray(i));
        }
      }
      return null;
    } catch (error) {
      console.error('Failed to extract JPEG from buffer:', error.message);
      return null;
    }
  }

  /**
   * Decompress image data from JPEG to raw bitmap
   * @param {Buffer} compressedData - Compressed image data (JPEG)
   * @param {number} imageType - Image type for getting dimensions
   * @returns {Promise<Buffer>} Raw bitmap data
   */
  async decompressImageData(compressedData, imageType) {
    return await timeOperation('decompressImageData', async () => {
      try {
        // Use sharp to decode JPEG and get raw pixel data
        const image = sharp(compressedData);

        // Get raw pixel data as RGB buffer
        const rawBuffer = await image
          .raw()
          .toBuffer();

        return rawBuffer;
      } catch (error) {
        console.error('Failed to decompress image data:', error.message);
        // If decompression fails, return the original compressed data
        return compressedData;
      }
    });
  }

  /**
   * Convert raw bitmap data to JPEG format
   * @param {Buffer} rawData - Raw bitmap data
   * @param {number} width - Image width
   * @param {number} height - Image height
   * @param {number} depth - Color depth (bits per pixel)
   * @returns {Promise<Buffer>} JPEG data
   */
  async convertToJpeg(rawData, width, height, depth) {
    return await timeOperation('convertToJpeg', async () => {
      try {
        const channels = depth / 8;
        let image;

        if (channels === 3) {
          // RGB data
          image = sharp(rawData, {
            raw: {
              width,
              height,
              channels: 3
            }
          });
        } else if (channels === 1) {
          // Grayscale data
          image = sharp(rawData, {
            raw: {
              width,
              height,
              channels: 1
            }
          });
        } else {
          throw new Error(`Unsupported channel count: ${channels}`);
        }

        return await image.jpeg({ quality: 85 }).toBuffer();
      } catch (error) {
        console.error('Failed to convert to JPEG:', error.message);
        throw error;
      }
    });
  }

  /**
   * Get image information for specified type
   * @param {number} imageType - Image type
   * @returns {object|null} Image information (width, height, depth)
   */
  getImageInfo(imageType) {
    switch (imageType) {
      case ImageType.THUMBNAIL:
        // Thumbnail is typically <= 256x256
        return { width: 256, height: 256, depth: this.header.imgColor };

      case ImageType.NAVIGATE:
        // Navigate image dimensions from header
        return {
          width: this.header.airImgWidth,
          height: this.header.airImgHeight,
          depth: this.header.imgColor
        };

      case ImageType.MACRO:
      case ImageType.MACRO_LABEL:
        // Macro image dimensions from header
        return {
          width: this.header.airImgWidth,
          height: this.header.airImgHeight,
          depth: this.header.imgColor
        };

      case ImageType.LABEL:
        // Label is extracted and scaled down
        const macroWidth = this.header.airImgWidth;
        const macroHeight = this.header.airImgHeight;
        let scale = 1;
        let tempHeight = macroHeight;
        while (tempHeight >= 256) {
          tempHeight = macroHeight / scale;
          scale++;
        }
        if (scale > 1) scale--;

        return {
          width: Math.floor(macroWidth / scale),
          height: Math.floor(macroHeight / scale),
          depth: this.header.imgColor
        };

      default:
        return null;
    }
  }
}
