#include "./CommonEx.h"
#include "./Templates.h"

bool RemoveAbnormal(int *pnData, const int nLen, const int nFactor)
{
    if (NULL == pnData || 0 >= nLen || 1 > nFactor)
    {
        return false;
    }

    int nPosSum = 0, nPosCnt = 0;
    int nNegSum = 0, nNegCnt = 0;
    for (int i = 0; i < nLen; ++i)
    {
        const int nValue = pnData[i];
        if (0 < nValue)
        {
            nPosSum += nValue;
            nPosCnt++;
        }
        else if (0 > nValue)
        {
            nNegSum += nValue;
            nNegCnt++;
        }
    }

    nPosSum /= max(1, nPosCnt);
    nNegSum /= max(1, nNegCnt);

    const int nPosTh = nPosSum * nFactor;
    const int nNegTh = nNegSum * nFactor;
    for (int i = 0; i < nLen; ++i)
    {
        if (nPosTh < pnData[i] || nNegTh > pnData[i])
        {
            pnData[i] = 0;
        }
    }

    return true;
}

bool RemoveAbnormal(int *pnData, bool *pbFlag, const int nLen, const int nFactor)
{
    if (NULL == pnData || 0 >= nLen || 1 > nFactor)
    {
        return false;
    }

    int nPosSum = 0, nPosCnt = 0;
    int nNegSum = 0, nNegCnt = 0;
    for (int i = 0; i < nLen; ++i)
    {
        const int nValue = pnData[i];
        if (pbFlag[i] && 0 < nValue)
        {
            nPosSum += nValue;
            nPosCnt++;
        }
        else if (pbFlag[i] && 0 > nValue)
        {
            nNegSum += nValue;
            nNegCnt++;
        }
    }

    nPosSum /= max(1, nPosCnt);
    nNegSum /= max(1, nNegCnt);

    bool bChanged = false;
    const int nPosThW = nPosSum / nFactor;
    const int nNegThW = nNegSum / nFactor;
    for (int i = 0; i < nLen; ++i)
    {
        if (pbFlag[i] && ((nPosThW > pnData[i] && 0 < pnData[i])
            || (nNegThW < pnData[i] && 0 > pnData[i])))
        {
            pnData[i] = 0;
            pbFlag[i] = false;
            bChanged = true;
        }
    }

    if (bChanged)
    {
        const int nBakPos = nPosSum * nFactor / 2;
        const int nBakNeg = nNegSum * nFactor / 2;
        nPosSum = 0;
        nPosCnt = 0;
        nNegSum = 0;
        nNegCnt = 0;
        for (int i = 0; i < nLen; ++i)
        {
            const int nValue = pnData[i];
            if (pbFlag[i] && 0 < nValue && nBakPos >= nValue)
            {
                nPosSum += nValue;
                nPosCnt++;
            }
            else if (pbFlag[i] && 0 > nValue && nBakNeg <= nValue)
            {
                nNegSum += nValue;
                nNegCnt++;
            }
        }

        nPosSum /= max(1, nPosCnt);
        nNegSum /= max(1, nNegCnt);
    }

    const int nPosTh = nPosSum * nFactor;
    const int nNegTh = nNegSum * nFactor;
    for (int i = 0; i < nLen; ++i)
    {
        if (pbFlag[i] && (nPosTh < pnData[i] || nNegTh > pnData[i]))
        {
            pnData[i] = 0;
            pbFlag[i] = false;
        }
    }

    return true;
}

bool RemoveWeak(int *pnData, const int nLen, const int nFactor)
{
    if (NULL == pnData || 0 >= nLen || 1 > nFactor)
    {
        return false;
    }

    RemoveAbnormal(pnData, nLen, nFactor);
    const int nMax = pnData[FindMaxIndex(pnData, nLen)];
    const int nMin = pnData[FindMinIndex(pnData, nLen)];
    const int nTh = max(nMax, -nMin) / nFactor;
    for (int i = 0; i < nLen; ++i)
    {
        if (-nTh < pnData[i] && nTh > pnData[i])
        {
            pnData[i] = 0;
        }
    }

    return true;
}

bool RemoveWeak(int *pnData, bool *pbFlag, const int nLen, const int nFactor)
{
    if (NULL == pnData || NULL == pbFlag || 0 >= nLen || 1 > nFactor)
    {
        return false;
    }

    RemoveAbnormal(pnData, pbFlag, nLen, nFactor);

    int nMaxIndex = 0, nMinIndex = 0;
    FindMaxIndex(pnData, nLen, &nMaxIndex, 1, pbFlag);
    const int nMax = pnData[nMaxIndex];
    FindMinIndex(pnData, nLen, &nMinIndex, 1, pbFlag);
    const int nMin = pnData[nMinIndex];
    const int nThPos = max(0, nMax / nFactor);
    const int nThNeg = min(0, nMin / nFactor);
    for (int i = 0; i < nLen; ++i)
    {
        if (pbFlag[i] && nThNeg <= pnData[i] && nThPos >= pnData[i])
        {
            pbFlag[i] = false;
        }
    }

    return true;
}
