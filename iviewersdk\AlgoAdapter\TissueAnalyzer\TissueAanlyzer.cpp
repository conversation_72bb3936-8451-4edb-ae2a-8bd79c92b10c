#include "TissueAnalyzer.h"
#include "TissueReport.h"
#include "pathology_param/PathologyParamMgr.h"
#include "log/Logger4cpp.h"
#include "reports.h"
#include "interface/ITFAdapter.h"

std::shared_ptr<TissueAnalyzer> TissueAnalyzer::m_ins;
std::mutex TissueAnalyzer::m_mutex;


std::shared_ptr<TissueAnalyzer> TissueAnalyzer::instance()
{
	if (m_ins == nullptr)
	{
		std::unique_lock<std::mutex> lock(m_mutex);
		if (m_ins == nullptr)
		{
			m_ins.reset(new TissueAnalyzer());


		}
	}

	return m_ins;
}



TissueAnalyzer::TissueAnalyzer()
{
	LOG_INFO("Tissue version 2.0");
}

TissueAnalyzer::~TissueAnalyzer()
{
}


bool TissueAnalyzer::run(const cv::Mat &imgOrigin, const float fStepX, const float fStepY, cv::Rect &rect,
	cv::Mat &memMap, cv::Mat &memColor, int &nWidth, int &nHeight, int &nType, std::string fileName)
{
	if (imgOrigin.empty())
	{
		return false;
	}

	
	std::shared_ptr<TFAdapter>  adapter_ = TFAdapter::create(NetId::NI_TISSUE, 2);

	int rows = imgOrigin.rows;
	int cols = imgOrigin.cols;
	std::cout << "width:" << cols << " height:" << rows << std::endl;
	PathologyImageItemPtr imgItem(new PathologyImageItem(cols, rows, 0, 0, cols*rows / 8, 0));
	
	auto report = make_shared<TissueReport>();
	report->getTissueInfo().setInfo(fStepX, fStepY, rect.tl().x,rect.tl().y,rect.width,rect.height);
	memcpy(imgItem->rawBuffer, imgOrigin.data, rows*cols * 3);
	
	if (imgItem == nullptr)
	{
		LOG_INFO("get macro tissue error.");
		return false;
	}
	LOG_INFO("get macro tissue start.");

	adapter_->run(imgItem, report);
	
	report->getTissueInfo().getMap(memMap);
	report->getTissueInfo().getColor(memColor);

	report->getTissueInfo().getMapSize(nWidth, nHeight);
	nType = report->getSlideType();

	if (fileName != "")
	{
		cv::Mat mask(imgOrigin.size(), CV_8UC1, cv::Scalar(0, 0, 0));
		std::vector<std::vector<cv::Point>> _contours = report->get_contours();
		cv::drawContours(mask, _contours, -1, cv::Scalar(255, 255, 255), -1);

		cv::Mat temp = imgOrigin.clone();

		cv::Mat img_masked;
		temp.copyTo(img_masked, mask);

		std::stringstream ss;
		ss << "./log/" << fileName.c_str();
		cv::imwrite(ss.str(), img_masked);

	}

	return true;
}


