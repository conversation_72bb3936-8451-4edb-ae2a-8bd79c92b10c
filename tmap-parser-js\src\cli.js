#!/usr/bin/env node

/**
 * TMAP Parser CLI Tool
 * Command line interface for parsing TMAP files
 */

import { Command } from 'commander';
import chalk from 'chalk';
import ora from 'ora';
import { table } from 'table';
import fs from 'fs/promises';
import path from 'path';
import prettyBytes from 'pretty-bytes';
import { TmapParser, getTmapFileInfo, getTmapImageData, getTmapImageInfo, globalPerformanceMonitor } from './index.js';
import { ImageType } from './structures/tmap6-structures.js';

const program = new Command();

// CLI Configuration
program
  .name('tmap-parser')
  .description('TMAP file parser for medical imaging')
  .version('1.0.0');

/**
 * Parse command - Full file parsing
 */
program
  .command('parse <file>')
  .description('Parse a TMAP file and display detailed information')
  .option('-o, --output <file>', 'Output file for JSON results')
  .option('-p, --performance', 'Show performance metrics')
  .option('-v, --verbose', 'Verbose output')
  .action(async (file, options) => {
    const spinner = ora('Parsing TMAP file...').start();
    
    try {
      // Check if file exists
      await fs.access(file);
      
      const parser = new TmapParser();
      const result = await parser.parseFile(file);
      
      spinner.succeed('TMAP file parsed successfully');
      
      // Display results
      displayFileInfo(result, options.verbose);
      
      // Show performance metrics if requested
      if (options.performance) {
        displayPerformanceMetrics();
      }
      
      // Save to output file if specified
      if (options.output) {
        await saveResults(result, options.output);
        console.log(chalk.green(`Results saved to: ${options.output}`));
      }
      
    } catch (error) {
      spinner.fail('Failed to parse TMAP file');
      console.error(chalk.red('Error:'), error.message);
      if (options.verbose) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  });

/**
 * Info command - Quick file information
 */
program
  .command('info <file>')
  .description('Get basic information about a TMAP file (header only)')
  .option('-j, --json', 'Output as JSON')
  .action(async (file, options) => {
    const spinner = ora('Reading TMAP file header...').start();
    
    try {
      await fs.access(file);
      
      const info = await getTmapFileInfo(file);
      
      spinner.succeed('TMAP file header read successfully');
      
      if (options.json) {
        console.log(JSON.stringify(info, null, 2));
      } else {
        displayBasicInfo(info);
      }
      
    } catch (error) {
      spinner.fail('Failed to read TMAP file');
      console.error(chalk.red('Error:'), error.message);
      process.exit(1);
    }
  });

/**
 * Validate command - Check if file is valid TMAP
 */
program
  .command('validate <file>')
  .description('Validate if a file is a valid TMAP file')
  .action(async (file) => {
    const spinner = ora('Validating TMAP file...').start();
    
    try {
      await fs.access(file);
      
      const info = await getTmapFileInfo(file);
      
      spinner.succeed('File is a valid TMAP file');
      console.log(chalk.green('✓ Valid TMAP file'));
      console.log(`  Version: ${info.versionString}`);
      console.log(`  Dimensions: ${info.totalWidth} × ${info.totalHeight}`);
      console.log(`  Magnification: ${info.scanMagnification}×`);
      
    } catch (error) {
      spinner.fail('File is not a valid TMAP file');
      console.error(chalk.red('✗ Invalid TMAP file:'), error.message);
      process.exit(1);
    }
  });

/**
 * Image command - Extract images from TMAP file
 */
program
  .command('image <file> <type>')
  .description('Extract image from TMAP file (thumbnail, navigate, macro, label, macro-label)')
  .option('-o, --output <file>', 'Output image file path')
  .option('-i, --info', 'Show image information only')
  .action(async (file, type, options) => {
    const spinner = ora('Processing image request...').start();

    try {
      await fs.access(file);

      // Map string type to enum
      const typeMap = {
        'thumbnail': ImageType.THUMBNAIL,
        'navigate': ImageType.NAVIGATE,
        'macro': ImageType.MACRO,
        'label': ImageType.LABEL,
        'macro-label': ImageType.MACRO_LABEL
      };

      const imageType = typeMap[type.toLowerCase()];
      if (imageType === undefined) {
        throw new Error(`Invalid image type: ${type}. Valid types: thumbnail, navigate, macro, label, macro-label`);
      }

      if (options.info) {
        // Show image information only
        const imageInfo = await getTmapImageInfo(file, imageType);
        spinner.succeed('Image information retrieved');

        if (imageInfo) {
          console.log(chalk.blue.bold('\n📷 Image Information'));
          console.log(chalk.blue('═'.repeat(30)));
          console.log(`${chalk.cyan('Type:')} ${type}`);
          console.log(`${chalk.cyan('Width:')} ${imageInfo.width} pixels`);
          console.log(`${chalk.cyan('Height:')} ${imageInfo.height} pixels`);
          console.log(`${chalk.cyan('Color Depth:')} ${imageInfo.depth} bits`);
        } else {
          console.log(chalk.yellow('No image information available for this type'));
        }
      } else {
        // Extract image data
        const imageData = await getTmapImageData(file, imageType);

        if (imageData) {
          spinner.succeed('Image extracted successfully');

          if (options.output) {
            await fs.writeFile(options.output, imageData);
            console.log(chalk.green(`Image saved to: ${options.output}`));
            console.log(`${chalk.cyan('Size:')} ${prettyBytes(imageData.length)}`);
          } else {
            console.log(chalk.blue.bold('\n📷 Image Data'));
            console.log(chalk.blue('═'.repeat(30)));
            console.log(`${chalk.cyan('Type:')} ${type}`);
            console.log(`${chalk.cyan('Size:')} ${prettyBytes(imageData.length)}`);
            console.log(chalk.yellow('Use --output option to save the image to a file'));
          }
        } else {
          spinner.warn('No image data found for this type');
          console.log(chalk.yellow(`No ${type} image found in the TMAP file`));
        }
      }

    } catch (error) {
      spinner.fail('Failed to process image request');
      console.error(chalk.red('Error:'), error.message);
      process.exit(1);
    }
  });

/**
 * Batch command - Process multiple files
 */
program
  .command('batch <directory>')
  .description('Process all TMAP files in a directory')
  .option('-r, --recursive', 'Process subdirectories recursively')
  .option('-o, --output <file>', 'Output file for batch results')
  .action(async (directory, options) => {
    const spinner = ora('Scanning for TMAP files...').start();
    
    try {
      const tmapFiles = await findTmapFiles(directory, options.recursive);
      
      if (tmapFiles.length === 0) {
        spinner.warn('No TMAP files found');
        return;
      }
      
      spinner.text = `Processing ${tmapFiles.length} TMAP files...`;
      
      const results = [];

      for (let i = 0; i < tmapFiles.length; i++) {
        const file = tmapFiles[i];
        spinner.text = `Processing ${i + 1}/${tmapFiles.length}: ${path.basename(file)}`;
        
        try {
          const info = await getTmapFileInfo(file);
          results.push({ file, status: 'success', info });
        } catch (error) {
          results.push({ file, status: 'error', error: error.message });
        }
      }
      
      spinner.succeed(`Processed ${tmapFiles.length} files`);
      
      // Display summary
      displayBatchResults(results);
      
      // Save results if requested
      if (options.output) {
        await saveResults(results, options.output);
        console.log(chalk.green(`Batch results saved to: ${options.output}`));
      }
      
    } catch (error) {
      spinner.fail('Batch processing failed');
      console.error(chalk.red('Error:'), error.message);
      process.exit(1);
    }
  });

/**
 * Display detailed file information
 */
function displayFileInfo(info, verbose = false) {
  console.log(chalk.blue.bold('\n📄 TMAP File Information'));
  console.log(chalk.blue('═'.repeat(50)));
  
  const data = [
    ['File Path', info.filePath],
    ['File Size', prettyBytes(info.fileSize)],
    ['TMAP Version', info.versionString],
    ['Image Dimensions', `${info.totalWidth} × ${info.totalHeight} pixels`],
    ['Tile Size', `${info.tileWidth} × ${info.tileHeight} pixels`],
    ['Scan Magnification', `${info.scanMagnification}×`],
    ['Pixel Size', `${info.pixelSize} μm/pixel`],
    ['Color Depth', `${info.colorDepth} bits`],
    ['Slide Type', info.slideType],
    ['Total Images', info.totalImages.toLocaleString()],
    ['Shrink Tiles', info.shrinkTileCount.toLocaleString()],
    ['Focus Layers', info.focusLayers]
  ];
  
  if (verbose) {
    data.push(
      ['Image Columns', info.imageColumns],
      ['Image Rows', info.imageRows],
      ['File Count', info.fileCount],
      ['Layer Count', info.layerCount],
      ['Ratio Step', info.ratioStep],
      ['Background Color', info.backgroundColor],
      ['Has Extension Data', info.hasExtensionData ? 'Yes' : 'No']
    );
  }
  
  const config = {
    border: {
      topBody: '─',
      topJoin: '┬',
      topLeft: '┌',
      topRight: '┐',
      bottomBody: '─',
      bottomJoin: '┴',
      bottomLeft: '└',
      bottomRight: '┘',
      bodyLeft: '│',
      bodyRight: '│',
      bodyJoin: '│',
      joinBody: '─',
      joinLeft: '├',
      joinRight: '┤',
      joinJoin: '┼'
    },
    columnDefault: {
      paddingLeft: 1,
      paddingRight: 1
    }
  };
  
  console.log(table(data, config));
}

/**
 * Display basic file information
 */
function displayBasicInfo(info) {
  console.log(chalk.blue.bold('\n📄 TMAP File Info'));
  console.log(chalk.blue('═'.repeat(30)));
  
  console.log(`${chalk.cyan('Version:')} ${info.versionString}`);
  console.log(`${chalk.cyan('Dimensions:')} ${info.totalWidth} × ${info.totalHeight}`);
  console.log(`${chalk.cyan('Magnification:')} ${info.scanMagnification}×`);
  console.log(`${chalk.cyan('Pixel Size:')} ${info.pixelSize} μm/pixel`);
  console.log(`${chalk.cyan('Slide Type:')} ${info.slideType}`);
  console.log(`${chalk.cyan('Color Depth:')} ${info.colorDepth} bits`);
}

/**
 * Display performance metrics
 */
function displayPerformanceMetrics() {
  console.log(chalk.yellow.bold('\n⚡ Performance Metrics'));
  console.log(chalk.yellow('═'.repeat(40)));
  
  const summary = globalPerformanceMonitor.getSummary();
  
  if (summary.totalOperations === 0) {
    console.log(chalk.gray('No performance data available'));
    return;
  }
  
  const data = [
    ['Total Operations', summary.totalOperations],
    ['Total Duration', `${summary.totalDuration}ms`],
    ['Average Duration', `${summary.avgDuration}ms`],
    ['Max Duration', `${summary.maxDuration}ms`],
    ['Min Duration', `${summary.minDuration}ms`],
    ['Total Memory Used', `${summary.totalMemoryUsed}MB`],
    ['Average Memory Used', `${summary.avgMemoryUsed}MB`]
  ];
  
  console.log(table(data));
}

/**
 * Display batch processing results
 */
function displayBatchResults(results) {
  console.log(chalk.blue.bold('\n📊 Batch Processing Results'));
  console.log(chalk.blue('═'.repeat(50)));

  const successful = results.filter(r => r.status === 'success');
  const failed = results.filter(r => r.status === 'error');

  console.log(`${chalk.green('✓ Successful:')} ${successful.length}`);
  console.log(`${chalk.red('✗ Failed:')} ${failed.length}`);
  console.log(`${chalk.blue('📁 Total:')} ${results.length}`);

  if (failed.length > 0) {
    console.log(chalk.red.bold('\n❌ Failed Files:'));
    failed.forEach(result => {
      console.log(`  ${chalk.red('✗')} ${path.basename(result.file)}: ${result.error}`);
    });
  }

  if (successful.length > 0) {
    console.log(chalk.green.bold('\n✅ Successful Files:'));
    const tableData = [['File', 'Version', 'Dimensions', 'Magnification']];

    successful.forEach(result => {
      tableData.push([
        path.basename(result.file),
        result.info.versionString,
        `${result.info.totalWidth}×${result.info.totalHeight}`,
        `${result.info.scanMagnification}×`
      ]);
    });

    console.log(table(tableData));
  }
}

/**
 * Find TMAP files in directory
 */
async function findTmapFiles(directory, recursive = false) {
  const tmapFiles = [];

  async function scanDirectory(dir) {
    const entries = await fs.readdir(dir, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);

      if (entry.isDirectory() && recursive) {
        await scanDirectory(fullPath);
      } else if (entry.isFile() && path.extname(entry.name).toLowerCase() === '.tmap') {
        tmapFiles.push(fullPath);
      }
    }
  }

  await scanDirectory(directory);
  return tmapFiles;
}

/**
 * Save results to file
 */
async function saveResults(results, outputFile) {
  const outputData = {
    timestamp: new Date().toISOString(),
    results: results,
    performanceMetrics: globalPerformanceMonitor.getSummary()
  };

  await fs.writeFile(outputFile, JSON.stringify(outputData, null, 2));
}



// Error handling
process.on('uncaughtException', (error) => {
  console.error(chalk.red('Uncaught Exception:'), error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('Unhandled Rejection at:'), promise, chalk.red('reason:'), reason);
  process.exit(1);
});

// Parse command line arguments
program.parse();
