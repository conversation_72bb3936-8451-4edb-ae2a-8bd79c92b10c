#!/usr/bin/env node

/**
 * TMAP Parser CLI Tool
 * Command line interface for parsing TMAP files
 */

import { Command } from 'commander';
import chalk from 'chalk';
import ora from 'ora';
import { table } from 'table';
import fs from 'fs/promises';
import path from 'path';
import prettyBytes from 'pretty-bytes';
import { Tmap6Parser, getTmapFileInfo, getTmapImageData, getTmapImageInfo, globalPerformanceMonitor } from './index.js';
import { ImageType } from './structures/tmap6-structures.js';

const program = new Command();

// CLI Configuration
program
  .name('tmap-parser')
  .description('TMAP file parser for medical imaging')
  .version('1.0.0');

/**
 * Parse command - Full file parsing
 */
program
  .command('parse <file>')
  .description('Parse a TMAP file and display detailed information')
  .option('-o, --output <file>', 'Output file for JSON results')
  .option('-p, --performance', 'Show performance metrics')
  .option('-v, --verbose', 'Verbose output')
  .action(async (file, options) => {
    const spinner = ora('Parsing TMAP file...').start();
    
    try {
      // Check if file exists
      await fs.access(file);
      
      const parser = new Tmap6Parser();
      const result = await parser.parseFile(file);
      
      spinner.succeed('TMAP file parsed successfully');
      
      // Display results
      displayFileInfo(result, options.verbose);
      
      // Show performance metrics if requested
      if (options.performance) {
        displayPerformanceMetrics();
      }
      
      // Save to output file if specified
      if (options.output) {
        await saveResults(result, options.output);
        console.log(chalk.green(`Results saved to: ${options.output}`));
      }
      
    } catch (error) {
      spinner.fail('Failed to parse TMAP file');
      console.error(chalk.red('Error:'), error.message);
      if (options.verbose) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  });

/**
 * Info command - Quick file information
 */
program
  .command('info <file>')
  .description('Get basic information about a TMAP file (header only)')
  .option('-j, --json', 'Output as JSON')
  .action(async (file, options) => {
    const spinner = ora('Reading TMAP file header...').start();
    
    try {
      await fs.access(file);
      
      const info = await getTmapFileInfo(file);
      
      spinner.succeed('TMAP file header read successfully');
      
      if (options.json) {
        console.log(JSON.stringify(info, null, 2));
      } else {
        displayBasicInfo(info);
      }
      
    } catch (error) {
      spinner.fail('Failed to read TMAP file');
      console.error(chalk.red('Error:'), error.message);
      process.exit(1);
    }
  });

/**
 * Validate command - Check if file is valid TMAP
 */
program
  .command('validate <file>')
  .description('Validate if a file is a valid TMAP file')
  .action(async (file) => {
    const spinner = ora('Validating TMAP file...').start();
    
    try {
      await fs.access(file);
      
      const info = await getTmapFileInfo(file);
      
      spinner.succeed('File is a valid TMAP file');
      console.log(chalk.green('✓ Valid TMAP file'));
      console.log(`  Version: ${info.versionString}`);
      console.log(`  Dimensions: ${info.totalWidth} × ${info.totalHeight}`);
      console.log(`  Magnification: ${info.scanMagnification}×`);
      
    } catch (error) {
      spinner.fail('File is not a valid TMAP file');
      console.error(chalk.red('✗ Invalid TMAP file:'), error.message);
      process.exit(1);
    }
  });

/**
 * Image command - Extract images from TMAP file
 */
program
  .command('image <file> <type>')
  .description('Extract image from TMAP file (thumbnail, navigate, macro, label, macro-label)')
  .option('-o, --output <file>', 'Output image file path')
  .option('-i, --info', 'Show image information only')
  .option('-c, --compressed', 'Return compressed data instead of raw bitmap data')
  .action(async (file, type, options) => {
    const spinner = ora('Processing image request...').start();

    try {
      await fs.access(file);

      // Map string type to enum
      const typeMap = {
        'thumbnail': ImageType.THUMBNAIL,
        'navigate': ImageType.NAVIGATE,
        'macro': ImageType.MACRO,
        'label': ImageType.LABEL,
        'macro-label': ImageType.MACRO_LABEL
      };

      const imageType = typeMap[type.toLowerCase()];
      if (imageType === undefined) {
        throw new Error(`Invalid image type: ${type}. Valid types: thumbnail, navigate, macro, label, macro-label`);
      }

      if (options.info) {
        // Show image information only
        const imageInfo = await getTmapImageInfo(file, imageType);
        spinner.succeed('Image information retrieved');

        if (imageInfo) {
          console.log(chalk.blue.bold('\n📷 Image Information'));
          console.log(chalk.blue('═'.repeat(30)));
          console.log(`${chalk.cyan('Type:')} ${type}`);
          console.log(`${chalk.cyan('Width:')} ${imageInfo.width} pixels`);
          console.log(`${chalk.cyan('Height:')} ${imageInfo.height} pixels`);
          console.log(`${chalk.cyan('Color Depth:')} ${imageInfo.depth} bits`);
        } else {
          console.log(chalk.yellow('No image information available for this type'));
        }
      } else {
        // Extract image data
        const raw = !options.compressed; // Default to raw unless --compressed is specified
        const imageData = await getTmapImageData(file, imageType, raw);

        if (imageData) {
          const dataType = raw ? 'raw bitmap' : 'compressed';
          spinner.succeed(`Image extracted successfully (${dataType} data)`);

          if (options.output) {
            await fs.writeFile(options.output, imageData);
            console.log(chalk.green(`Image saved to: ${options.output}`));
            console.log(`${chalk.cyan('Size:')} ${prettyBytes(imageData.length)}`);
            console.log(`${chalk.cyan('Format:')} ${dataType} data`);
          } else {
            console.log(chalk.blue.bold('\n📷 Image Data'));
            console.log(chalk.blue('═'.repeat(30)));
            console.log(`${chalk.cyan('Type:')} ${type}`);
            console.log(`${chalk.cyan('Format:')} ${dataType} data`);
            console.log(`${chalk.cyan('Size:')} ${prettyBytes(imageData.length)}`);
            console.log(chalk.yellow('Use --output option to save the image to a file'));
          }
        } else {
          spinner.warn('No image data found for this type');
          console.log(chalk.yellow(`No ${type} image found in the TMAP file`));
        }
      }

    } catch (error) {
      spinner.fail('Failed to process image request');
      console.error(chalk.red('Error:'), error.message);
      process.exit(1);
    }
  });

/**
 * Extract-images command - Extract all supported images from TMAP file
 */
program
  .command('extract-images <file>')
  .description('Extract all supported images (thumbnail, navigate, macro, label, macro-label) as JPEG files using iViewerSDK algorithms')
  .option('-o, --output-dir <dir>', 'Output directory for images', './extracted_images')
  .option('-p, --performance', 'Show detailed performance metrics')
  .option('--prefix <prefix>', 'Filename prefix for extracted images', 'image')
  .action(async (file, options) => {
    const spinner = ora('Extracting images from TMAP file...').start();

    try {
      await fs.access(file);

      // Create output directory if it doesn't exist
      await fs.mkdir(options.outputDir, { recursive: true });

      // Define image types to extract (excluding TILE, WHOLE, ALL)
      const imageTypes = [
        { type: ImageType.THUMBNAIL, name: 'thumbnail' },
        { type: ImageType.NAVIGATE, name: 'navigate' },
        { type: ImageType.MACRO, name: 'macro' },
        { type: ImageType.LABEL, name: 'label' },
        { type: ImageType.MACRO_LABEL, name: 'macro-label' }
      ];

      const results = [];
      const performanceData = {
        totalStartTime: Date.now(),
        individualTimes: {},
        totalSize: 0,
        successCount: 0,
        failureCount: 0
      };

      spinner.text = 'Initializing parser (iViewerSDK algorithms)...';
      const parser = new Tmap6Parser();
      const parseStartTime = Date.now();
      await parser.parseFile(file);
      const parseTime = Date.now() - parseStartTime;

      console.log(`\n🔧 Using iViewerSDK segmentation algorithms`);
      performanceData.parseTime = parseTime;

      // Extract each image type
      for (const imageTypeInfo of imageTypes) {
        const { type, name } = imageTypeInfo;

        try {
          spinner.text = `Extracting ${name} image...`;
          const startTime = Date.now();

          // Get compressed JPEG data
          const imageData = await parser.getImageData(type, false); // false = compressed JPEG
          const extractTime = Date.now() - startTime;

          if (imageData) {
            // Save to file
            const filename = `${options.prefix}_${name}.jpg`;
            const filepath = path.join(options.outputDir, filename);
            await fs.writeFile(filepath, imageData);

            results.push({
              type: name,
              success: true,
              filepath,
              size: imageData.length,
              extractTime
            });

            performanceData.totalSize += imageData.length;
            performanceData.successCount++;
          } else {
            results.push({
              type: name,
              success: false,
              reason: 'No image data found',
              extractTime
            });

            performanceData.failureCount++;
          }

          performanceData.individualTimes[name] = extractTime;

        } catch (error) {
          results.push({
            type: name,
            success: false,
            reason: error.message,
            extractTime: 0
          });

          performanceData.failureCount++;
        }
      }

      const totalTime = Date.now() - performanceData.totalStartTime;
      performanceData.totalTime = totalTime;

      spinner.succeed(`Image extraction completed! ${performanceData.successCount} successful, ${performanceData.failureCount} failed`);

      // Display results
      console.log(chalk.blue.bold('\n📷 Image Extraction Results'));
      console.log(chalk.blue('═'.repeat(50)));

      const tableData = [
        ['Image Type', 'Status', 'Size', 'Time (ms)', 'File Path']
      ];

      for (const result of results) {
        const status = result.success ? chalk.green('✓ Success') : chalk.red('✗ Failed');
        const size = result.success ? prettyBytes(result.size) : chalk.gray('N/A');
        const time = result.extractTime.toFixed(0);
        const filepath = result.success ? result.filepath : chalk.gray(result.reason || 'N/A');

        tableData.push([
          chalk.cyan(result.type),
          status,
          size,
          time,
          filepath
        ]);
      }

      console.log(table(tableData, {
        border: {
          topBody: '─',
          topJoin: '┬',
          topLeft: '┌',
          topRight: '┐',
          bottomBody: '─',
          bottomJoin: '┴',
          bottomLeft: '└',
          bottomRight: '┘',
          bodyLeft: '│',
          bodyRight: '│',
          bodyJoin: '│'
        }
      }));

      // Performance analysis
      if (options.performance) {
        console.log(chalk.blue.bold('\n⚡ Performance Analysis'));
        console.log(chalk.blue('═'.repeat(50)));

        const perfTableData = [
          ['Metric', 'Value', 'Details']
        ];

        perfTableData.push([
          'Total Time',
          `${totalTime}ms`,
          'Complete operation time'
        ]);

        perfTableData.push([
          'Parse Time',
          `${parseTime}ms`,
          `${((parseTime / totalTime) * 100).toFixed(1)}% of total`
        ]);

        perfTableData.push([
          'Extraction Time',
          `${(totalTime - parseTime)}ms`,
          `${(((totalTime - parseTime) / totalTime) * 100).toFixed(1)}% of total`
        ]);

        perfTableData.push([
          'Total Data Size',
          prettyBytes(performanceData.totalSize),
          `${performanceData.successCount} images`
        ]);

        perfTableData.push([
          'Average Speed',
          `${(performanceData.totalSize / (totalTime / 1000) / 1024 / 1024).toFixed(2)} MB/s`,
          'Data extraction rate'
        ]);

        perfTableData.push([
          'Success Rate',
          `${((performanceData.successCount / imageTypes.length) * 100).toFixed(1)}%`,
          `${performanceData.successCount}/${imageTypes.length} images`
        ]);

        console.log(table(perfTableData, {
          border: {
            topBody: '─',
            topJoin: '┬',
            topLeft: '┌',
            topRight: '┐',
            bottomBody: '─',
            bottomJoin: '┴',
            bottomLeft: '└',
            bottomRight: '┘',
            bodyLeft: '│',
            bodyRight: '│',
            bodyJoin: '│'
          }
        }));

        // Individual timing breakdown
        console.log(chalk.blue.bold('\n🔍 Individual Timing Breakdown'));
        console.log(chalk.blue('═'.repeat(50)));

        const timingData = [
          ['Image Type', 'Time (ms)', '% of Extraction', 'Status']
        ];

        const extractionTime = totalTime - parseTime;

        for (const [imageName, time] of Object.entries(performanceData.individualTimes)) {
          const percentage = extractionTime > 0 ? ((time / extractionTime) * 100).toFixed(1) : '0.0';
          const result = results.find(r => r.type === imageName);
          const status = result?.success ? chalk.green('Success') : chalk.red('Failed');

          timingData.push([
            chalk.cyan(imageName),
            time.toFixed(0),
            `${percentage}%`,
            status
          ]);
        }

        console.log(table(timingData, {
          border: {
            topBody: '─',
            topJoin: '┬',
            topLeft: '┌',
            topRight: '┐',
            bottomBody: '─',
            bottomJoin: '┴',
            bottomLeft: '└',
            bottomRight: '┘',
            bodyLeft: '│',
            bodyRight: '│',
            bodyJoin: '│'
          }
        }));

        // Memory and efficiency metrics
        if (globalPerformanceMonitor) {
          console.log(chalk.blue.bold('\n💾 Memory & Efficiency Metrics'));
          console.log(chalk.blue('═'.repeat(50)));

          try {
            const metrics = globalPerformanceMonitor.getSummary();
            console.log(`${chalk.cyan('Peak Memory Usage:')} ${prettyBytes(metrics.peakMemoryUsage || 0)}`);
            console.log(`${chalk.cyan('Total Operations:')} ${metrics.totalOperations || 0}`);
            console.log(`${chalk.cyan('Average Operation Time:')} ${(metrics.averageOperationTime || 0).toFixed(2)}ms`);

            if (options.performance) {
              globalPerformanceMonitor.logMetrics();
            }
          } catch (error) {
            console.log(chalk.yellow('Memory metrics unavailable'));
          }
        }
      }

      console.log(chalk.green.bold(`\n✨ Images saved to: ${options.outputDir}`));

    } catch (error) {
      spinner.fail('Failed to extract images');
      console.error(chalk.red('Error:'), error.message);
      process.exit(1);
    }
  });

/**
 * Batch command - Process multiple files
 */
program
  .command('batch <directory>')
  .description('Process all TMAP files in a directory')
  .option('-r, --recursive', 'Process subdirectories recursively')
  .option('-o, --output <file>', 'Output file for batch results')
  .action(async (directory, options) => {
    const spinner = ora('Scanning for TMAP files...').start();
    
    try {
      const tmapFiles = await findTmapFiles(directory, options.recursive);
      
      if (tmapFiles.length === 0) {
        spinner.warn('No TMAP files found');
        return;
      }
      
      spinner.text = `Processing ${tmapFiles.length} TMAP files...`;
      
      const results = [];

      for (let i = 0; i < tmapFiles.length; i++) {
        const file = tmapFiles[i];
        spinner.text = `Processing ${i + 1}/${tmapFiles.length}: ${path.basename(file)}`;
        
        try {
          const info = await getTmapFileInfo(file);
          results.push({ file, status: 'success', info });
        } catch (error) {
          results.push({ file, status: 'error', error: error.message });
        }
      }
      
      spinner.succeed(`Processed ${tmapFiles.length} files`);
      
      // Display summary
      displayBatchResults(results);
      
      // Save results if requested
      if (options.output) {
        await saveResults(results, options.output);
        console.log(chalk.green(`Batch results saved to: ${options.output}`));
      }
      
    } catch (error) {
      spinner.fail('Batch processing failed');
      console.error(chalk.red('Error:'), error.message);
      process.exit(1);
    }
  });

/**
 * Display detailed file information
 */
function displayFileInfo(info, verbose = false) {
  console.log(chalk.blue.bold('\n📄 TMAP File Information'));
  console.log(chalk.blue('═'.repeat(50)));
  
  const data = [
    ['File Path', info.filePath],
    ['File Size', prettyBytes(info.fileSize)],
    ['TMAP Version', info.versionString],
    ['Image Dimensions', `${info.totalWidth} × ${info.totalHeight} pixels`],
    ['Tile Size', `${info.tileWidth} × ${info.tileHeight} pixels`],
    ['Scan Magnification', `${info.scanMagnification}×`],
    ['Pixel Size', `${info.pixelSize} μm/pixel`],
    ['Color Depth', `${info.colorDepth} bits`],
    ['Slide Type', info.slideType],
    ['Total Images', info.totalImages.toLocaleString()],
    ['Shrink Tiles', info.shrinkTileCount.toLocaleString()],
    ['Focus Layers', info.focusLayers]
  ];
  
  if (verbose) {
    data.push(
      ['Image Columns', info.imageColumns],
      ['Image Rows', info.imageRows],
      ['File Count', info.fileCount],
      ['Layer Count', info.layerCount],
      ['Ratio Step', info.ratioStep],
      ['Background Color', info.backgroundColor],
      ['Has Extension Data', info.hasExtensionData ? 'Yes' : 'No']
    );
  }
  
  const config = {
    border: {
      topBody: '─',
      topJoin: '┬',
      topLeft: '┌',
      topRight: '┐',
      bottomBody: '─',
      bottomJoin: '┴',
      bottomLeft: '└',
      bottomRight: '┘',
      bodyLeft: '│',
      bodyRight: '│',
      bodyJoin: '│',
      joinBody: '─',
      joinLeft: '├',
      joinRight: '┤',
      joinJoin: '┼'
    },
    columnDefault: {
      paddingLeft: 1,
      paddingRight: 1
    }
  };
  
  console.log(table(data, config));
}

/**
 * Display basic file information
 */
function displayBasicInfo(info) {
  console.log(chalk.blue.bold('\n📄 TMAP File Info'));
  console.log(chalk.blue('═'.repeat(30)));
  
  console.log(`${chalk.cyan('Version:')} ${info.versionString}`);
  console.log(`${chalk.cyan('Dimensions:')} ${info.totalWidth} × ${info.totalHeight}`);
  console.log(`${chalk.cyan('Magnification:')} ${info.scanMagnification}×`);
  console.log(`${chalk.cyan('Pixel Size:')} ${info.pixelSize} μm/pixel`);
  console.log(`${chalk.cyan('Slide Type:')} ${info.slideType}`);
  console.log(`${chalk.cyan('Color Depth:')} ${info.colorDepth} bits`);
}

/**
 * Display performance metrics
 */
function displayPerformanceMetrics() {
  console.log(chalk.yellow.bold('\n⚡ Performance Metrics'));
  console.log(chalk.yellow('═'.repeat(40)));
  
  const summary = globalPerformanceMonitor.getSummary();
  
  if (summary.totalOperations === 0) {
    console.log(chalk.gray('No performance data available'));
    return;
  }
  
  const data = [
    ['Total Operations', summary.totalOperations],
    ['Total Duration', `${summary.totalDuration}ms`],
    ['Average Duration', `${summary.avgDuration}ms`],
    ['Max Duration', `${summary.maxDuration}ms`],
    ['Min Duration', `${summary.minDuration}ms`],
    ['Total Memory Used', `${summary.totalMemoryUsed}MB`],
    ['Average Memory Used', `${summary.avgMemoryUsed}MB`]
  ];
  
  console.log(table(data));
}

/**
 * Display batch processing results
 */
function displayBatchResults(results) {
  console.log(chalk.blue.bold('\n📊 Batch Processing Results'));
  console.log(chalk.blue('═'.repeat(50)));

  const successful = results.filter(r => r.status === 'success');
  const failed = results.filter(r => r.status === 'error');

  console.log(`${chalk.green('✓ Successful:')} ${successful.length}`);
  console.log(`${chalk.red('✗ Failed:')} ${failed.length}`);
  console.log(`${chalk.blue('📁 Total:')} ${results.length}`);

  if (failed.length > 0) {
    console.log(chalk.red.bold('\n❌ Failed Files:'));
    failed.forEach(result => {
      console.log(`  ${chalk.red('✗')} ${path.basename(result.file)}: ${result.error}`);
    });
  }

  if (successful.length > 0) {
    console.log(chalk.green.bold('\n✅ Successful Files:'));
    const tableData = [['File', 'Version', 'Dimensions', 'Magnification']];

    successful.forEach(result => {
      tableData.push([
        path.basename(result.file),
        result.info.versionString,
        `${result.info.totalWidth}×${result.info.totalHeight}`,
        `${result.info.scanMagnification}×`
      ]);
    });

    console.log(table(tableData));
  }
}

/**
 * Find TMAP files in directory
 */
async function findTmapFiles(directory, recursive = false) {
  const tmapFiles = [];

  async function scanDirectory(dir) {
    const entries = await fs.readdir(dir, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);

      if (entry.isDirectory() && recursive) {
        await scanDirectory(fullPath);
      } else if (entry.isFile() && path.extname(entry.name).toLowerCase() === '.tmap') {
        tmapFiles.push(fullPath);
      }
    }
  }

  await scanDirectory(directory);
  return tmapFiles;
}

/**
 * Save results to file
 */
async function saveResults(results, outputFile) {
  const outputData = {
    timestamp: new Date().toISOString(),
    results: results,
    performanceMetrics: globalPerformanceMonitor.getSummary()
  };

  await fs.writeFile(outputFile, JSON.stringify(outputData, null, 2));
}



// Error handling
process.on('uncaughtException', (error) => {
  console.error(chalk.red('Uncaught Exception:'), error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('Unhandled Rejection at:'), promise, chalk.red('reason:'), reason);
  process.exit(1);
});

// Parse command line arguments
program.parse();
