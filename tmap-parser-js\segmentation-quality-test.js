/**
 * Segmentation Quality Test
 * Compare different modes and analyze which produces better results
 */

import { Tmap6Parser } from './src/parsers/tmap6-parser.js';
import sharp from 'sharp';
import fs from 'fs/promises';

async function testSegmentationQuality() {
  console.log('🔍 Segmentation Quality Analysis');
  console.log('═'.repeat(60));

  const testFiles = [
    'E:\\TMAP\\Test_1.TMAP',
    'E:\\TMAP\\SLICEID-20250324112235.TMAP'
  ];

  for (const filePath of testFiles) {
    console.log(`\n📁 Testing: ${filePath.split('\\').pop()}`);
    console.log('─'.repeat(50));

    try {
      const modes = ['fast', 'accurate'];
      const results = {};

      for (const mode of modes) {
        console.log(`\n🔧 ${mode.toUpperCase()} Mode:`);
        
        const parser = new Tmap6Parser({ segmentationMode: mode });
        await parser.parseFile(filePath);

        // Extract images
        const labelData = await parser.getLabelImage(false);
        const macroData = await parser.getMacroImage(false);
        const macroLabelData = await parser.getMacroLabelImage(false);

        if (!labelData || !macroData || !macroLabelData) {
          console.log(`❌ Failed to extract images`);
          continue;
        }

        // Get metadata
        const labelMeta = await sharp(labelData).metadata();
        const macroMeta = await sharp(macroData).metadata();
        const macroLabelMeta = await sharp(macroLabelData).metadata();

        // Calculate ratios
        const labelRatio = (labelMeta.width / macroLabelMeta.width) * 100;
        const macroRatio = (macroMeta.width / macroLabelMeta.width) * 100;
        const totalRatio = labelRatio + macroRatio;

        results[mode] = {
          labelWidth: labelMeta.width,
          macroWidth: macroMeta.width,
          totalWidth: macroLabelMeta.width,
          labelRatio,
          macroRatio,
          totalRatio,
          labelSize: labelData.length,
          macroSize: macroData.length
        };

        console.log(`  📐 Label: ${labelMeta.width}x${labelMeta.height} (${labelRatio.toFixed(1)}%)`);
        console.log(`  📐 Macro: ${macroMeta.width}x${macroMeta.height} (${macroRatio.toFixed(1)}%)`);
        console.log(`  📊 Total coverage: ${totalRatio.toFixed(1)}%`);
        console.log(`  💾 Sizes: Label ${(labelData.length/1024).toFixed(1)}kB, Macro ${(macroData.length/1024).toFixed(1)}kB`);

        // Save images for visual inspection
        const outputDir = `./quality_test_${filePath.split('\\').pop().replace('.TMAP', '')}_${mode}`;
        await fs.mkdir(outputDir, { recursive: true });
        
        await fs.writeFile(`${outputDir}/label.jpg`, labelData);
        await fs.writeFile(`${outputDir}/macro.jpg`, macroData);
        await fs.writeFile(`${outputDir}/macro-label.jpg`, macroLabelData);
        
        console.log(`  💾 Images saved to: ${outputDir}`);
      }

      // Compare results
      if (results.fast && results.accurate) {
        console.log(`\n📊 Comparison Analysis:`);
        console.log('─'.repeat(30));
        
        const fastResult = results.fast;
        const accurateResult = results.accurate;
        
        console.log(`Label ratio difference: ${Math.abs(fastResult.labelRatio - accurateResult.labelRatio).toFixed(1)}%`);
        console.log(`Macro ratio difference: ${Math.abs(fastResult.macroRatio - accurateResult.macroRatio).toFixed(1)}%`);
        
        // Analyze which seems more reasonable
        console.log(`\n🤔 Analysis:`);
        
        if (Math.abs(fastResult.labelRatio - 66.7) < 5) {
          console.log(`  FAST uses standard 2/3 split (${fastResult.labelRatio.toFixed(1)}% label)`);
        } else {
          console.log(`  FAST uses non-standard split (${fastResult.labelRatio.toFixed(1)}% label)`);
        }
        
        console.log(`  ACCURATE uses intelligent detection (${accurateResult.labelRatio.toFixed(1)}% label)`);
        
        // File size analysis
        const fastTotal = fastResult.labelSize + fastResult.macroSize;
        const accurateTotal = accurateResult.labelSize + accurateResult.macroSize;
        const sizeDiff = Math.abs(fastTotal - accurateTotal);
        
        console.log(`\n💾 File Size Analysis:`);
        console.log(`  FAST total: ${(fastTotal/1024).toFixed(1)}kB`);
        console.log(`  ACCURATE total: ${(accurateTotal/1024).toFixed(1)}kB`);
        console.log(`  Difference: ${(sizeDiff/1024).toFixed(1)}kB`);
        
        // Recommendation
        console.log(`\n💡 Recommendation:`);
        if (Math.abs(fastResult.labelRatio - accurateResult.labelRatio) > 20) {
          console.log(`  ⚠️  Significant difference detected (${Math.abs(fastResult.labelRatio - accurateResult.labelRatio).toFixed(1)}%)`);
          console.log(`  🎯 ACCURATE mode recommended for this file`);
          console.log(`  📝 Reason: Large discrepancy suggests complex boundary`);
        } else {
          console.log(`  ✅ Both modes produce similar results`);
          console.log(`  ⚡ FAST mode acceptable for speed`);
        }
      }

    } catch (error) {
      console.log(`❌ Test failed: ${error.message}`);
    }
  }

  console.log(`\n🏆 Overall Conclusions:`);
  console.log('─'.repeat(30));
  console.log(`1. Different TMAP files have different optimal segmentation ratios`);
  console.log(`2. Simple 2/3 split works for some files but not others`);
  console.log(`3. OpenCV provides adaptive boundary detection`);
  console.log(`4. Visual inspection of extracted images is recommended`);
  console.log(`5. For critical applications, use ACCURATE mode`);
}

// Run the test
testSegmentationQuality().catch(console.error);
