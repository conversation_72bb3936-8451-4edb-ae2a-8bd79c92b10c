/**
 * <PERSON>uffer reading utilities for binary file parsing
 */

export class BufferReader {
  constructor(buffer) {
    this.buffer = buffer;
    this.offset = 0;
    this.littleEndian = true; // Default to little endian
  }

  /**
   * Set endianness
   * @param {boolean} littleEndian - True for little endian, false for big endian
   */
  setEndianness(littleEndian) {
    this.littleEndian = littleEndian;
  }

  /**
   * Get current offset
   * @returns {number} Current offset
   */
  getOffset() {
    return this.offset;
  }

  /**
   * Set offset
   * @param {number} offset - New offset
   */
  setOffset(offset) {
    if (offset < 0 || offset > this.buffer.length) {
      throw new Error(`Invalid offset: ${offset}`);
    }
    this.offset = offset;
  }

  /**
   * Skip bytes
   * @param {number} count - Number of bytes to skip
   */
  skip(count) {
    this.setOffset(this.offset + count);
  }

  /**
   * Check if there are enough bytes remaining
   * @param {number} count - Number of bytes needed
   * @returns {boolean} True if enough bytes available
   */
  hasBytes(count) {
    return this.offset + count <= this.buffer.length;
  }

  /**
   * Read unsigned 8-bit integer
   * @returns {number} Value
   */
  readUInt8() {
    if (!this.hasBytes(1)) {
      throw new Error('Not enough bytes to read UInt8');
    }
    const value = this.buffer.readUInt8(this.offset);
    this.offset += 1;
    return value;
  }

  /**
   * Read signed 8-bit integer
   * @returns {number} Value
   */
  readInt8() {
    if (!this.hasBytes(1)) {
      throw new Error('Not enough bytes to read Int8');
    }
    const value = this.buffer.readInt8(this.offset);
    this.offset += 1;
    return value;
  }

  /**
   * Read unsigned 16-bit integer
   * @returns {number} Value
   */
  readUInt16() {
    if (!this.hasBytes(2)) {
      throw new Error('Not enough bytes to read UInt16');
    }
    const value = this.littleEndian 
      ? this.buffer.readUInt16LE(this.offset)
      : this.buffer.readUInt16BE(this.offset);
    this.offset += 2;
    return value;
  }

  /**
   * Read signed 16-bit integer
   * @returns {number} Value
   */
  readInt16() {
    if (!this.hasBytes(2)) {
      throw new Error('Not enough bytes to read Int16');
    }
    const value = this.littleEndian 
      ? this.buffer.readInt16LE(this.offset)
      : this.buffer.readInt16BE(this.offset);
    this.offset += 2;
    return value;
  }

  /**
   * Read unsigned 32-bit integer
   * @returns {number} Value
   */
  readUInt32() {
    if (!this.hasBytes(4)) {
      throw new Error('Not enough bytes to read UInt32');
    }
    const value = this.littleEndian 
      ? this.buffer.readUInt32LE(this.offset)
      : this.buffer.readUInt32BE(this.offset);
    this.offset += 4;
    return value;
  }

  /**
   * Read signed 32-bit integer
   * @returns {number} Value
   */
  readInt32() {
    if (!this.hasBytes(4)) {
      throw new Error('Not enough bytes to read Int32');
    }
    const value = this.littleEndian 
      ? this.buffer.readInt32LE(this.offset)
      : this.buffer.readInt32BE(this.offset);
    this.offset += 4;
    return value;
  }

  /**
   * Read 32-bit float
   * @returns {number} Value
   */
  readFloat32() {
    if (!this.hasBytes(4)) {
      throw new Error('Not enough bytes to read Float32');
    }
    const value = this.littleEndian 
      ? this.buffer.readFloatLE(this.offset)
      : this.buffer.readFloatBE(this.offset);
    this.offset += 4;
    return value;
  }

  /**
   * Read 64-bit float
   * @returns {number} Value
   */
  readFloat64() {
    if (!this.hasBytes(8)) {
      throw new Error('Not enough bytes to read Float64');
    }
    const value = this.littleEndian 
      ? this.buffer.readDoubleLE(this.offset)
      : this.buffer.readDoubleBE(this.offset);
    this.offset += 8;
    return value;
  }

  /**
   * Read string with specified length
   * @param {number} length - String length
   * @param {string} encoding - String encoding (default: 'utf8')
   * @returns {string} String value
   */
  readString(length, encoding = 'utf8') {
    if (!this.hasBytes(length)) {
      throw new Error(`Not enough bytes to read string of length ${length}`);
    }
    const value = this.buffer.toString(encoding, this.offset, this.offset + length);
    this.offset += length;
    return value;
  }

  /**
   * Read null-terminated string
   * @param {string} encoding - String encoding (default: 'utf8')
   * @returns {string} String value
   */
  readCString(encoding = 'utf8') {
    let end = this.offset;
    while (end < this.buffer.length && this.buffer[end] !== 0) {
      end++;
    }
    const value = this.buffer.toString(encoding, this.offset, end);
    this.offset = end + 1; // Skip the null terminator
    return value;
  }

  /**
   * Read raw bytes
   * @param {number} length - Number of bytes to read
   * @returns {Buffer} Buffer containing the bytes
   */
  readBytes(length) {
    if (!this.hasBytes(length)) {
      throw new Error(`Not enough bytes to read ${length} bytes`);
    }
    const value = this.buffer.subarray(this.offset, this.offset + length);
    this.offset += length;
    return value;
  }

  /**
   * Read array of values
   * @param {number} count - Number of elements
   * @param {Function} readFn - Function to read single element
   * @returns {Array} Array of values
   */
  readArray(count, readFn) {
    const array = [];
    for (let i = 0; i < count; i++) {
      array.push(readFn.call(this));
    }
    return array;
  }

  /**
   * Peek at next bytes without advancing offset
   * @param {number} length - Number of bytes to peek
   * @returns {Buffer} Buffer containing the bytes
   */
  peek(length) {
    if (!this.hasBytes(length)) {
      throw new Error(`Not enough bytes to peek ${length} bytes`);
    }
    return this.buffer.subarray(this.offset, this.offset + length);
  }

  /**
   * Get remaining bytes count
   * @returns {number} Number of remaining bytes
   */
  remaining() {
    return this.buffer.length - this.offset;
  }

  /**
   * Check if at end of buffer
   * @returns {boolean} True if at end
   */
  isAtEnd() {
    return this.offset >= this.buffer.length;
  }
}
