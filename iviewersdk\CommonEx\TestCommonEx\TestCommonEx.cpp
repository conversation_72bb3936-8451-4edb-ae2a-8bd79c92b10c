#include <opencv2/opencv.hpp>
#include "../CommonEx.h"
#include "../Templates.h"

using namespace cv;

void Bayer2RGBLP(unsigned char *pSrcBuf, unsigned char *pOutBuf,
                 int nWidth, int nHeight, int nSrcWidthByte, int nMode);

void Bayer2RGBHQLinear(unsigned char *pSrcBuf, unsigned char *pOutBuf, int nWidth, int nHeight, 
                       int nSrcWidthByte, int nMode);

int main(int argc, char *argv[])
{
    Mat img1, img2;
    img1 = imread("e:/crop-.bmp");
    img2 = imread("e:/circle2.bmp");
    int nX = 0, nY = 0;
    float fScore = 0.0f;
    Mem mem;
    uchar *pucImg1 = mem[img1.cols * img1.rows * 2];
    uchar *pucImg2 = pucImg1 + img1.cols * img1.rows;
    CopyGrayData(img1.data, pucImg1, img1.cols, img1.rows, img1.step);
    CopyGrayData(img2.data, pucImg2, img2.cols, img2.rows, img2.step);
    MatchTemplate(pucImg1, img1.cols, img1.rows, pucImg2, img2.cols, img2.rows, nX, nY, fScore);

// 	float f = GetSubPositionGauss(34118.22636, 33250.52842, 31545.00549);
// 
// 	float g = f;

//     Mat img1, img2;
//     img1 = imread("d:/01.png");
//     img2 = imread("d:/02.png");
//     int nX = 0, nY = 0;
//     float fScore = 0.0f;
//     Mem mem;
//     uchar *pucImg1 = mem[img1.cols * img1.rows * 2];
//     uchar *pucImg2 = pucImg1 + img1.cols * img1.rows;
//     CopyGrayData(img1.data, pucImg1, img1.cols, img1.rows, img1.step);
//     CopyGrayData(img2.data, pucImg2, img1.cols, img1.rows, img1.step);
//     CalcOffset(pucImg1, pucImg2, img1.cols, img1.rows, 8, 8, 1600, nX, nY, fScore);
//     cerr << nX << endl << nY << endl << fScore << endl;
//     return 0;
// 
//     UN_POINT_S astPoints[6];
//     astPoints[0] = PointMake(1, 1);
//     astPoints[1] = PointMake(3, 1);
//     astPoints[2] = PointMake(5, 5);
//     astPoints[3] = PointMake(1, 5);
//     astPoints[4] = PointMake(2, 2);
//     astPoints[5] = PointMake(2, 1);
//     bool bFlags[6];
//     ConvexHull(astPoints, bFlags, 6);
//     for (int i = 0; i < 6; ++i)
//     {
//         cerr << bFlags[i] << endl;
//     }
    /*
    Mem mem;
    Mem dst;
    for (int i = 1; i < argc; ++i)
    {
        Mat img = imread(argv[i], 1);
        uchar *pucImg = mem[img.cols * img.rows];
        ExtractChannel(img.data, pucImg, img.cols, img.rows, img.step, 1, 1);

        int nWidth = 1600, nHeight = 1200;;
        uchar *pucDst = dst[nWidth * nHeight];
        Resize(pucImg, pucDst, img.cols, img.rows, img.cols, nWidth, nHeight, nWidth, RESIZE_CUBIC);

        Smooth(pucDst, pucDst, nWidth, nHeight, nWidth, 3);
        //Sharpen(pucDst, nWidth, nHeight, nWidth, 0.5f);
        Mat sa(nHeight, nWidth, CV_8UC1, pucDst, nWidth);
        sa *= 180.0f / 215.0f;
        imwrite(argv[i], sa);
    }
    return 0;*/

    /*
    Mat img = imread("E:\\Images\\Medical\\compare\\anti-shaking\\00.bmp", 0);
    Mem mem;
    uchar *pucColor = mem[img.cols * img.rows * 3];
    int64 t = GetTick(); 
    ConvertColor(img.data, pucColor, img.cols, img.rows, img.step,
        img.cols, img.rows, img.cols * 3, BayerBG2BGR);
    cerr << GetElapsedTime(t) << endl;
    Mat dst(img.rows, img.cols, CV_8UC3, pucColor, img.cols * 3);
    imwrite("d:/color.bmp", dst);

    t = GetTick(); 
    ConvertColor(img.data, pucColor, img.cols, img.rows, img.step,
        img.cols, img.rows, img.cols * 3, (Color) COLOR_BayerBG2BGR_VNG);
    cerr << GetElapsedTime(t) << endl;
    imwrite("d:/colorvng.bmp", dst);

//     t = GetTick(); 
//     ConvertColor(img.data, pucColor, img.cols, img.rows, img.step,
//         img.cols, img.rows, img.cols * 3, COLOR_BayerBG2BGR_EA);
//     cerr << GetElapsedTime(t) << endl;
//     Mat dst(img.rows, img.cols, CV_8UC3, pucColor, img.cols * 3);
//     imwrite("d:/colorea.bmp", dst);

    t = GetTick(); 
    Bayer2RGBLP(img.data, pucColor, img.cols, img.rows, img.step, 2);
    cerr << GetElapsedTime(t) << endl;
    imwrite("d:/colorLP.bmp", dst);

    t = GetTick(); 
    Bayer2RGBHQLinear(img.data, pucColor, img.cols, img.rows, img.step, 2);
    cerr << GetElapsedTime(t) << endl;
    imwrite("d:/colorHQ.bmp", dst);*/

    /*
    for (int i = 1; i < argc; ++i)
    {
        Mat img = imread(argv[i], 1);
        Mat sub  = img(Rect(0, 0, 1600, 1200));
        imwrite(argv[i], sub);
//         Mem mem;
//         uchar *pucImg = mem[img.cols * img.rows];
//         ExtractChannel(img.data, pucImg, img.cols, img.rows, img.step, 1, 1);
// 
//         int nWidth = img.cols * 12 / 10, nHeight = img.rows * 12 / 10;
//         Mem dst;
//         uchar *pucDst = dst[nWidth * nHeight];
//         Resize(pucImg, pucDst, img.cols, img.rows, img.cols, nWidth, nHeight, nWidth, RESIZE_CUBIC);
// 
//         Smooth(pucDst, pucDst, nWidth, nHeight, nWidth, 3);
//         //Sharpen(pucDst, nWidth, nHeight, nWidth, 0.5f);
//         Mat sa(nHeight, nWidth, CV_8UC1, pucDst, nWidth);
//         imwrite(argv[i], sa);
    }*/

//     Mat img = imread("d:/01.bmp", 1);
//     Mem mem;
//     uchar *pucDst = mem[img.step * img.rows];
//     memcpy(pucDst, img.data, mem.Size());
//     int64 t = GetTick();
//     RotateImage(pucDst, img.data, img.cols, img.rows, img.step, img.cols / 2.0f, img.rows / 2.0f, 10 * 3.14 / 180);
//     cerr << GetElapsedTime(t) << endl;
//     imwrite("d:/rotate.jpg", img);

    /*
    Mat img = imread("d:/01.bmp", 0);
    Mem mem;
    uchar *pucDst = mem[img.step * img.rows];
    memset(pucDst, 0, mem.Size());
    memset(pucDst, 255, img.step * 5);
    memset(pucDst + img.rows * img.step - img.step * 5, 255, img.step * 5);
    bool bOk = Watershed(img.data, pucDst, img.cols, img.rows);
    Mat dst(img.rows, img.cols, CV_8UC1, pucDst);
    imwrite("d:/result.bmp", dst);*/

    /*
    Mat img = imread("d:/01.bmp", 1);
    Mem mem;
    uchar *pucDst = mem[img.step * img.rows];
    ConvertColor(img.data, pucDst, img.cols, img.rows, img.step,
        img.cols, img.rows, img.step, RGB2YUV);
    Mat dst(img.rows, img.cols, CV_8UC(3), pucDst);
    imwrite("d:/result.bmp", dst);*/

    /*
    UN_POINT_S astPoints[17];
    astPoints[0] = PointMake(0, 50);
    astPoints[1] = PointMake(100, 50);
    astPoints[2] = PointMake(50, 0);
    astPoints[3] = PointMake(50, 100);
    astPoints[4] = PointMake(150, 70);
    memcpy(astPoints + 5, astPoints, sizeof(astPoints[0]) * 4);
    memcpy(astPoints + 9, astPoints, sizeof(astPoints[0]) * 4);
    memcpy(astPoints + 13, astPoints, sizeof(astPoints[0]) * 4);

    float fX = 0.0f, fY = 0.0f, fR = 0.0f, fError = 0.0f;
    FitCircleRefit(astPoints, 13, fX, fY, fR, fError);*/
    /*
    Mat img = imread("d:/FitData-Num11_1.bmp", 1); // ER 031728_6_20.bmp, 01s.jpg

    AutoLevel(img.data, img.cols, img.rows, img.step, 0);
    imwrite("d:/levelsh.bmp", img);

    Mem mem;
    const int nChannels = 3;
    uchar *pucImg = mem[img.cols * img.rows * nChannels];
    const int nPitch = img.cols * nChannels;

    Mem_<UN_POINT_S> memp;
    UN_POINT_S *pstContour = memp[1024];
    int n = 0;
    TraceContour(img.data, img.cols, img.rows, 1, 1, 0, 0, img.cols, img.rows, 255, 255, pstContour, 1024, n);

    int nFFTWidth = 0, nFFTHeight = 0;
    FFTSize(img.cols, img.rows, nFFTWidth, nFFTHeight);
    Mem_<COMPLEX_S> memcc;
    COMPLEX_S *pstFFT = memcc[nFFTWidth * nFFTHeight];
    int64 t = GetTick();
    FFT2D(img.data, img.cols, img.rows, pstFFT, nFFTWidth, nFFTHeight);
    cerr << "Time: " << GetElapsedTime(t) << endl;
    pucImg = mem[nFFTWidth * nFFTHeight];
    FFT2Display(pstFFT, nFFTWidth, nFFTHeight, pucImg, 16);
    SaveBmp(pucImg, nFFTWidth, nFFTHeight, nFFTWidth, "d:/rr.bmp");

    t = GetTick();
    FFT2DCV(img.data, img.cols, img.rows, pstFFT);
    cerr << "Time: " << GetElapsedTime(t) << endl;
    FFT2Display(pstFFT, img.cols, img.rows, pucImg, 16);
    SaveBmp(pucImg, img.cols, img.rows, img.cols, "d:/rrcv.bmp");*/

    //ConvertColor(img.data, pucImg, img.cols, img.rows, img.step, img.cols, img.rows, nPitch, BayerRG2RGB);
    //AdjustContrast(img.data, img.cols, img.rows, /*img.step, */10);
//     Equalize(img.data, img.cols, img.rows, img.step);
// 
//     DetectEdge(img.data, pucImg, img.cols, img.rows, 32);
//     Smooth(pucImg, pucImg, img.cols, img.rows, img.cols, 7);
// 
//     Mat dst(img.rows, img.cols, CV_8UC(nChannels), img.data, nPitch);
//     imwrite("d:/r.bmp", dst);
// 
//     int64 t = GetTick();
//     Thinning(pucImg, img.cols, img.rows, 8, 480, 480, 1400, 1400);
//     cerr << "Time: " << GetElapsedTime(t) << endl;

    //SaveBmp(pucImg, img.cols, img.rows, nPitch, "d:/rr.bmp");

    /*
    Mat img = imread("d:/images/01.bmp", 1);

    uchar *pucGray = mnew uchar[img.cols * img.rows * 2];
    if (NULL == pucGray)
    {
        return 1;
    }

    CopyGrayData(img.data, pucGray, img.cols, img.rows, img.step);

    Mat dst(img.rows, img.cols, CV_8UC1, pucGray, img.cols);
    imshow("show", dst);
    waitKey(0);

    uchar *pucLabel = pucGray + img.rows * img.cols;
    memset(pucLabel, 0, sizeof(pucLabel[0]) * img.rows * img.cols);
    pucLabel[34 * img.cols + 42] = 1;
    pucLabel[288 * img.cols + 385] = 2;
    pucLabel[358 * img.cols + 955] = 3;

    Watershed(pucGray, pucLabel, img.cols, img.rows);

    Mat label(img.rows, img.cols, CV_8UC1, pucLabel, img.cols);
    imwrite("d:/label.bmp", label);

    mdelete(pucGray);*/

    return 0;
}
