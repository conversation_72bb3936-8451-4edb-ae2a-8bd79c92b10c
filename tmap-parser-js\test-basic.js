/**
 * Basic test to verify TMAP parser functionality
 */

import { getTmapFileInfo } from './src/index.js';
import chalk from 'chalk';

const TEST_FILE = 'E:\\TMAP\\Test_1.TMAP';

async function basicTest() {
  console.log(chalk.blue('🧪 Basic TMAP Parser Test'));
  console.log(chalk.blue('═'.repeat(30)));
  
  try {
    console.log(chalk.yellow('📖 Reading TMAP file header...'));
    
    const info = await getTmapFileInfo(TEST_FILE);
    
    console.log(chalk.green('✅ Successfully parsed TMAP file!'));
    console.log('\n📄 File Information:');
    console.log(`  Version: ${info.versionString}`);
    console.log(`  Dimensions: ${info.totalWidth} × ${info.totalHeight} pixels`);
    console.log(`  Magnification: ${info.scanMagnification}×`);
    console.log(`  Pixel Size: ${info.pixelSize} μm/pixel`);
    console.log(`  Slide Type: ${info.slideType}`);
    console.log(`  Color Depth: ${info.colorDepth} bits`);
    
  } catch (error) {
    console.error(chalk.red('❌ Test failed:'), error.message);
    console.error(chalk.gray('Stack trace:'), error.stack);
    process.exit(1);
  }
}

basicTest();
