/**
* @date         2012-05-28
* @filename     ImageIO.h
* @purpose      class for loading and saving image
* @version      2.1
* @history      initial draft
* <AUTHOR> UNIC Tech, Beijing, China
* @copyright    UNIC Technologies, 2005-2012. All rights reserved.
*/

#ifndef __UNIC_IMAGE_IO_H__
#define __UNIC_IMAGE_IO_H__

#ifdef IMAGEIODLL_EXPORTS
#define IMAGE_IO_API __declspec(dllexport)
#else
#define IMAGE_IO_API __declspec(dllimport)
#endif

typedef unsigned char uchar;
typedef unsigned int uint;

// class for loading and saving image
class IMAGE_IO_API CImageIO
{

 public:
    /**
    * @method       CImageIO::CImageIO
    * @access       public
    * @brief        construct function
    * @param        void
    * @return
    * <AUTHOR> <EMAIL>
    * @date         2012-05-28
    * @history      initial draft
    */
    CImageIO(void);

    /**
    * @method       CImageIO::~CImageIO
    * @access       public
    * @brief        destruct function
    * @param        void
    * @return
    * <AUTHOR> <EMAIL>
    * @date         2012-05-28
    * @history      initial draft
    */
    ~CImageIO(void);

    /**
    * @method       CImageIO::ReadImage
    * @access       public
    * @brief        load image, if bLoadAsGray is true, image will be loaded as gray
    * @param        const char * pcImagePath
    * @param        bool bLoadAsGray
    * @return       bool
    * <AUTHOR> Lei, <EMAIL>
    * @date         2012-05-28
    * @history      initial draft
    */
    bool ReadImage(const char *pcImagePath, bool bLoadAsGray = false);

    /**
    * @method       CImageIO::SaveImage
    * @access       public
    * @brief        save image
    * @param        const char * pcImagePath
    * @return       bool
    * <AUTHOR> Lei, <EMAIL>
    * @date         2012-05-28
    * @history      initial draft
    */
    bool SaveImage(const char *pcImagePath);

    /**
    * @method       CImageIO::SaveImage
    * @access       public
    * @brief        save image
    * @param        const char * pcImagePath
    * @param        const uchar * pucImg
    * @param        const int nWidth
    * @param        const int nHeight
    * @param        const int nPitch
    * @return       bool
    * <AUTHOR> Lei, <EMAIL>
    * @date         2012-05-28
    * @history      initial draft
    */
    static bool SaveImage(const char *pcImagePath, const uchar *pucImg,
        const int nWidth, const int nHeight, const int nPitch);

    /**
    * @method       CImageIO::GetBuffer
    * @access       public
    * @brief        get image buffer
    * @param        void
    * @return       uchar *
    * <AUTHOR> Lei, <EMAIL>
    * @date         2012-05-28
    * @history      initial draft
    */
    uchar *GetBuffer(void);

    /**
    * @method       CImageIO::GetWidth
    * @access       public
    * @brief        get image width
    * @param        void
    * @return       int
    * <AUTHOR> Lei, <EMAIL>
    * @date         2012-05-28
    * @history      initial draft
    */
    int GetWidth(void) const;

    /**
    * @method       CImageIO::GetHeight
    * @access       public
    * @brief        get image height
    * @param        void
    * @return       int
    * <AUTHOR> Lei, <EMAIL>
    * @date         2012-05-28
    * @history      initial draft
    */
    int GetHeight(void) const;

    /**
    * @method       CImageIO::GetBpp
    * @access       public
    * @brief        get image bpp, bits per pixel
    * @param        void
    * @return       int
    * <AUTHOR> Lei, <EMAIL>
    * @date         2012-05-28
    * @history      initial draft
    */
    int GetBpp(void) const;

    /**
    * @method       CImageIO::GetPitch
    * @access       public
    * @brief        get image width step
    * @param        void
    * @return       int
    * <AUTHOR> Lei, <EMAIL>
    * @date         2012-05-28
    * @history      initial draft
    */
    int GetPitch(void) const;

    enum INTER_E
    {
        INTER_NN = 0, // nearest neighbor
        INTER_LINEAR, // bilinear
        INTER_CUBIC, // bicubic
        INTER_AREA, // area
        INTER_LANCZOS4, // lanczos4

        INTER_ALL
    };

    /**
    * @method       CImageIO::Resize
    * @access       public static 
    * @brief        resize image
    * @param        const int nWidth
    * @param        const int nHeight
    * @param        const INTER_E eType
    * @return       bool
    * <AUTHOR> Lei, <EMAIL>
    * @date         2015-12-01
    * @history      initial draft
    */
    bool Resize(const int nWidth, const int nHeight, const INTER_E eType = INTER_LINEAR);

    /**
    * @method       CImageIO::Display
    * @access       public
    * @brief        display image
    * @param        void
    * @return       void
    * <AUTHOR> Lei, <EMAIL>
    * @date         2012-05-28
    * @history      initial draft
    */
    void Display(void) const;

    /**
    * @method       CImageIO::Display
    * @access       public static
    * @brief        display image
    * @param        const uchar * pucImg
    * @param        const int nWidth
    * @param        const int nHeight
    * @param        const int nPitch
    * @return       void
    * <AUTHOR> Lei, <EMAIL>
    * @date         2012-05-28
    * @history      initial draft
    */
    static void Display(const uchar *pucImg, const int nWidth,
        const int nHeight, const int nPitch);

    /**
    * @method       CImageIO::Decode
    * @access       public static 
    * @brief        load one jpg block from buffer and decode it
    * @param        const uchar * pucJpeg
    * @param        const uint unLength
    * @param        uchar * pucData
    * @param        const int nWidth
    * @param        const int nHeight
    * @param        const int nPitch
    * @return       bool
    * <AUTHOR> Lei, <EMAIL>
    * @date         2015-03-24
    * @history      initial draft
    */
    static bool Decode(const uchar *pucJpeg, const uint unLength,
        uchar *pucData, const int nWidth, const int nHeight, const int nPitch);

    enum ALGO_E
    {
        ALGO_JPEG = 0,
        ALGO_J2K,
        ALGO_ALL
    };

    /**
    * @method       CImageIO::Encode
    * @access       public static 
    * @brief        encode image data to jpg data
    * @param        const uchar * pucData
    * @param        const int nWidth
    * @param        const int nHeight
    * @param        const int nPitch
    * @param        uchar * pucJpeg
    * @param        int & nDataLen
    * @param        const int nQuality
    * @param        const ALGO_E eAlgo
    * @return       bool
    * <AUTHOR> Lei, <EMAIL>
    * @date         2015-03-24
    * @history      initial draft
    */
    static bool Encode(const uchar *pucData, const int nWidth, const int nHeight,
        const int nPitch, uchar *pucJpeg, int &nDataLen,
        const int nQuality = 70, const ALGO_E eAlgo = ALGO_JPEG);

    /**
    * @method       CImageIO::Resize
    * @access       public static 
    * @brief        resize image
    * @param        const uchar * pucImgIn
    * @param        uchar * pucImgOut
    * @param        const int nWidth
    * @param        const int nHeight
    * @param        const int nPitch
    * @param        const int nWidthDst
    * @param        const int nHeightDst
    * @param        const int nPitchDst
    * @param        const INTER_E eType
    * @return       bool
    * <AUTHOR> Lei, <EMAIL>
    * @date         2015-12-01
    * @history      initial draft
    */
    static bool Resize(const uchar *pucImgIn, uchar *pucImgOut,
        const int nWidth, const int nHeight, const int nPitch,
        const int nWidthDst, const int nHeightDst, const int nPitchDst,
        const INTER_E eType = INTER_LINEAR);

    /**
    * @method       CImageIO::Resize
    * @access       public static 
    * @brief        resize image, using user specified channels
    * @param        const uchar * pucImgIn
    * @param        uchar * pucImgOut
    * @param        const int nWidth
    * @param        const int nHeight
    * @param        const int nChannels
    * @param        const int nPitch
    * @param        const int nWidthDst
    * @param        const int nHeightDst
    * @param        const int nChannelsDst
    * @param        const int nPitchDst
    * @param        const INTER_E eType
    * @return       bool
    * <AUTHOR> Lei, <EMAIL>
    * @date         2016-11-14
    * @history      initial draft
    */
    static bool Resize(const uchar *pucImgIn, uchar *pucImgOut,
        const int nWidth, const int nHeight, const int nChannels, const int nPitch,
        const int nWidthDst, const int nHeightDst, const int nChannelsDst, const int nPitchDst,
        const INTER_E eType = INTER_LINEAR);

 private:
    // make it private to prevent copying
    CImageIO(const CImageIO &other); //lint !e1704

    // make it private to prevent copying
    CImageIO &operator=(const CImageIO &other);

    void *m_pImg;
};

#endif // __UNIC_IMAGE_IO_H__
