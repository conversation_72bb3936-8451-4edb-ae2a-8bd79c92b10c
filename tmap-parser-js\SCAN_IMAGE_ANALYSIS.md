# 🔬 TMAP扫描图像分析报告

## 📊 当前发现

### ✅ 已实现功能

1. **扫描图像信息提取** ✅
   - 从TMAP头部读取扫描图像尺寸：109066 × 58476 像素
   - 计算缩放后的输出尺寸
   - 估算文件大小

2. **CLI集成** ✅
   - 添加了 `--include-scan` 选项
   - 添加了 `--scan-scale` 参数（默认32倍缩放）
   - 集成到性能监控系统

3. **基础框架** ✅
   - `getScanImage()` 方法框架
   - ROI（感兴趣区域）支持
   - 缩放参数处理

### 📋 测试结果

#### Test_1.TMAP 文件信息：
- **原始尺寸**: 109066 × 58476 像素
- **64倍缩放**: 1704 × 913 像素
- **预期大小**: 4.5 MB (RGB, 3通道)
- **文件格式**: TMAP6

## 🔍 iViewerSDK代码分析

### 核心发现：

1. **GetImageData函数**：
   ```cpp
   bool GetImageData(const int nID, const int nLeft, const int nTop,
                     const int nRight, const int nBottom, const float fScale,
                     unsigned char *pucBuffer, const int nBufferLength)
   ```

2. **GetTmapROIData函数**：
   ```cpp
   unsigned char* GetTmapROIData(int nUserID, _RECT* pROI, int nScale, 
                                 int& nWidth, int& nHeight, bool bChangeRB, int nAlignBytes)
   ```

3. **关键参数**：
   - `nScale`: 缩放因子（1 = 全分辨率）
   - `pROI`: 感兴趣区域矩形
   - `nWidth/nHeight`: 输出图像尺寸
   - `fScale`: 浮点缩放因子

### 🏗️ 实现架构

#### iViewerSDK的扫描图像提取流程：

1. **验证参数**：
   ```cpp
   if (nScale > m_nMaxZoomRate || pROI->right <= pROI->left || pROI->bottom <= pROI->top)
       return NULL;
   ```

2. **计算缩放比例**：
   ```cpp
   double dRate = double(nScale) / m_nMaxZoomRate;
   ```

3. **边界检查**：
   ```cpp
   if (pROI->left < 0) pROI->left = 0;
   if (pROI->right > m_nWholeImgWidth) pROI->right = m_nWholeImgWidth;
   ```

4. **图像重建**：
   - 从tile数据重建图像
   - 应用缩放变换
   - 处理内存对齐

## 🚧 实现挑战

### 1. **Tile-based重建**
- TMAP文件使用tile结构存储大图像
- 需要读取和组合多个tile
- 处理tile边界和重叠

### 2. **内存管理**
- 大图像需要大量内存
- 需要流式处理避免内存溢出
- 缓冲区管理和释放

### 3. **性能优化**
- 大图像处理耗时
- 需要进度报告
- 可能需要多线程处理

### 4. **数据格式**
- Tile数据可能是压缩的
- 需要解压缩和格式转换
- 处理不同的颜色深度

## 📋 下一步实现计划

### 阶段1：基础Tile读取 🎯
1. **解析Tile信息**：
   - 读取tile索引表
   - 获取tile位置和大小
   - 验证tile数据完整性

2. **单Tile提取**：
   - 实现单个tile的读取
   - 解压缩tile数据
   - 转换为标准格式

### 阶段2：图像重建 🔧
1. **Tile组合**：
   - 计算tile布局
   - 按顺序组合tile
   - 处理边界对齐

2. **缩放处理**：
   - 实现图像缩放算法
   - 优化内存使用
   - 支持不同缩放比例

### 阶段3：优化和完善 ⚡
1. **性能优化**：
   - 流式处理大图像
   - 内存池管理
   - 进度报告

2. **错误处理**：
   - 损坏tile检测
   - 优雅降级
   - 详细错误报告

## 🛠️ 技术实现要点

### 1. **Tile数据结构**
```javascript
struct TileInfo {
  uint8_t layerNo;      // 层级编号
  uint8_t tileCol;      // Tile列
  uint8_t tileRow;      // Tile行
  int32_t fileOffset;   // 文件偏移
  uint32_t dataLength;  // 数据长度
}
```

### 2. **ROI计算**
```javascript
// 计算需要的tile范围
const startTileX = Math.floor(roi.left / tileWidth);
const startTileY = Math.floor(roi.top / tileHeight);
const endTileX = Math.ceil(roi.right / tileWidth);
const endTileY = Math.ceil(roi.bottom / tileHeight);
```

### 3. **内存管理**
```javascript
// 分块处理避免内存溢出
const maxBufferSize = 256 * 1024 * 1024; // 256MB
const processInChunks = outputSize > maxBufferSize;
```

## 📈 预期性能

### 不同缩放比例的预期表现：

| 缩放比例 | 输出尺寸 | 文件大小 | 处理时间 | 内存使用 |
|----------|----------|----------|----------|----------|
| 1x | 109066×58476 | ~18GB | >10分钟 | >18GB |
| 4x | 27266×14619 | ~1.1GB | ~2分钟 | ~1.1GB |
| 16x | 6816×3654 | ~71MB | ~30秒 | ~71MB |
| 32x | 3408×1827 | ~18MB | ~10秒 | ~18MB |
| 64x | 1704×913 | ~4.5MB | ~3秒 | ~4.5MB |

### 建议使用场景：

- **64x缩放**: 快速预览，文件分享
- **32x缩放**: 一般分析，平衡质量和大小
- **16x缩放**: 详细分析，较高质量
- **4x缩放**: 高质量分析，大文件
- **1x缩放**: 完整分辨率，极大文件（谨慎使用）

## 🎯 当前状态

### ✅ 已完成：
- [x] 扫描图像信息读取
- [x] CLI参数支持
- [x] 基础框架搭建
- [x] 尺寸计算和验证

### 🚧 进行中：
- [ ] Tile数据结构解析
- [ ] 单Tile读取实现
- [ ] 图像重建算法

### 📋 待实现：
- [ ] 完整的tile-based重建
- [ ] 性能优化
- [ ] 错误处理完善
- [ ] 大文件流式处理

## 💡 使用建议

### 当前可用功能：
```bash
# 查看扫描图像信息（不实际提取）
node src/cli.js info file.tmap

# 尝试扫描图像提取（当前返回null）
node src/cli.js extract-images file.tmap --include-scan --scan-scale=64
```

### 推荐工作流程：
1. 先使用 `info` 命令查看文件信息
2. 根据文件大小选择合适的缩放比例
3. 使用较大的缩放比例进行测试
4. 等待完整实现后再尝试小缩放比例

扫描图像提取功能的框架已经搭建完成，接下来需要实现核心的tile-based图像重建算法！🚀
