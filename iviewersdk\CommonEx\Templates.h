#ifndef __MLEI_TEMPLATE_EX_H__
#define __MLEI_TEMPLATE_EX_H__

#include "./AlgoInterface.h"

// limit data
template <class Type>
void LimitBorder(Type &value, const Type minV, const Type maxV)
{
    if (value < minV)
    {
        value = minV;
    }
    if (value > maxV)
    {
        value = maxV;
    }
}

// determine whether the nIndex-th data in pData is peak or not
template <class Type>
bool IsPeak(const Type *pData, const int nLength, const int nIndex, const bool bStrict = true)
{
    if (NULL == pData)
    {
        return false;
    }

    Type left = pData[(nIndex - 1 + nLength) % nLength];
    Type right = pData[(nIndex + 1) % nLength];

    if (bStrict)
    {
        return (pData[nIndex] > left) && (pData[nIndex] > right);
    }
    else
    {
        return (pData[nIndex] >= left) && (pData[nIndex] >= right);
    }
}

// smooth array to reduce the influence of noise
template <class Type>
void SmoothArray(Type *pData, const int nLength, bool bClosed = true)
{
    if (NULL == pData || nLength <= 2)
    {
        return;
    }

    double dPrev = pData[nLength - 1], dNew;
    for (int j = 0; j < nLength; j++)
    {
        if (!bClosed && (j == 0 || j == nLength - 1))
        {
            dPrev = pData[j];
            continue;
        }

        dNew = (dPrev + pData[j] * 2 + pData[(j + 1) % nLength]) / 4; //lint !e790
        dPrev = pData[j];
        pData[j] = (Type) dNew;
    }
}

template <class Type>
bool SmoothArray(Type *pIn, const int nLength, const int nR) 
{
    if (NULL == pIn || 0 >= nLength)
    {
        return false;
    }

    int i, nCount;
    int nStart, nEnd;
    Type sum = 0;

    Type *pOut = mnew Type[nLength];
    if (NULL == pOut)
    {
        return false;
    }

    if (nLength <= nR + 1)
    {
        nCount = nLength;
        if (nCount <= 0) 
        {
            pOut[0] = pIn[0];
            return true;
        }

        sum = 0;
        for (i = 0; i < nLength; ++i)
        {
            sum += pIn[i];
        }
        sum = (sum + nCount / 2) / nCount;
        for (i = 0; i < nLength; i ++)
        {
            pOut[i] = sum;
        }
        return true;
    }

    nStart = 0;
    nEnd = nR;
    if (nEnd > nLength)
    {
        nEnd = nLength;
    }
    for (i = nStart; i < nEnd; ++i)
    {
        sum += pIn[i];
    }
    nCount = nEnd - nStart;

    if (nEnd >= nLength - nR)
    {
        nEnd = nLength - nR - 1;
    }
    for (i = 0; i <= nEnd; i ++) {
        sum += pIn[i + nR];
        nCount ++;
        pOut[i] = (sum + nCount / 2) / nCount;
    }

    nEnd = nLength - nR;
    for (; i < nEnd; i ++) {
        sum += pIn[i + nR] - pIn[i - nR - 1];
        pOut[i] = (sum + nCount / 2) / nCount;
    }

    nEnd = nR + 1;
    if (nEnd > nLength) nEnd = nLength;
    for (; i < nEnd; i++)
    {
        pOut[i] = (sum + nCount / 2) / nCount;
    }

    nStart = i;
    if (nStart < nR + 1) nStart = nR + 1;
    for (i = nStart; i < nLength; i ++) {
        nCount --;
        sum -= pIn[i - nR - 1];
        pOut[i] = (sum + nCount / 2) / nCount;
    }
    memcpy(pIn, pOut, sizeof(pIn[0]) * nLength);
    mdelete(pOut);

    return true;
}

// return the index of the maximum in pData
template <class Type>
const int FindMaxIndex(const Type *pData, const int nLength)
{
    if (NULL == pData)
    {
        return -1;
    }

    Type fMax = pData[0];
    int nIndex = 0;
    for (int i = 1; i < nLength; i++)
    {
        if (pData[i] > fMax)
        {
            fMax = pData[i];
            nIndex = i;
        }
    }

    return nIndex;
}

// find the nMaxNum largest number and store the index
template <class Type>
bool FindMaxIndex(const Type *pData, const int nLen, int *pnIndex,
                  const int nMaxNum, const bool *pbFlags = NULL)
{
    if (NULL == pData || NULL == pnIndex
        || 0 >= nLen || 0 >= nMaxNum || nLen < nMaxNum)
    {
        return false;
    }

    // initialize
    Type *pValue = mnew Type[nMaxNum];
    if (NULL == pValue)
    {
        return false;
    }
    memset(pValue, 0, sizeof(Type) * nMaxNum);
    int nNum = 0;

    // find four corners of maximal distance
    for (int i = 0; i < nLen; ++i)
    {
        if (NULL != pbFlags && !pbFlags[i])
        {
            continue;
        }

        for (int j = 0; j < nNum + 1; ++j)
        {
            if (pData[i] >= pValue[j])
            {
                // remove previous distances one step backward
                for (int k = nNum - 1; k >= j; --k)
                {
                    pnIndex[k + 1] = pnIndex[k];
                    pValue[k + 1] = pValue[k];
                }

                nNum++;
                nNum = min(nMaxNum - 1, nNum);

                // save the new best
                pnIndex[j] = i;
                pValue[j] = pData[i];
                break;
            }
        }
    }
    mdelete(pValue);

    return nNum == nMaxNum;
}

// find the nMinNum smallest number and store the index
template <class Type>
bool FindMinIndex(const Type *pData, const int nLen, int *pnIndex,
                  const int nMinNum, const bool *pbFlags = NULL)
{
    if (NULL == pData || NULL == pnIndex
        || 0 >= nLen || 0 >= nMinNum || nLen < nMinNum)
    {
        return false;
    }

    // initialize
    Type *pValue = mnew Type[nMinNum];
    if (NULL == pValue)
    {
        return false;
    }
    memset(pValue, 0, sizeof(Type) * nMinNum);
    int nNum = 0;

    // find four corners of maximal distance
    for (int i = 0; i < nLen; ++i)
    {
        if (NULL != pbFlags && !pbFlags[i])
        {
            continue;
        }

        for (int j = 0; j < nNum + 1; ++j)
        {
            if (pData[i] <= pValue[j])
            {
                // remove previous distances one step backward
                for (int k = nNum - 1; k >= j; --k)
                {
                    pnIndex[k + 1] = pnIndex[k];
                    pValue[k + 1] = pValue[k];
                }

                nNum++;
                nNum = min(nMinNum - 1, nNum);

                // save the new best
                pnIndex[j] = i;
                pValue[j] = pData[i];
                break;
            }
        }
    }
    mdelete(pValue);

    return nNum == nMinNum;
}

// return the index of the minimum in pData
template <class Type>
const int FindMinIndex(const Type *pData, const int nLength)
{
    if (NULL == pData)
    {
        return -1;
    }

    Type fMin = pData[0];
    int nIndex = 0;
    for (int i = 1; i < nLength; i++)
    {
        if (pData[i] < fMin)
        {
            fMin = pData[i];
            nIndex = i;
        }
    }

    return nIndex;
}

// swap two data
template <class Type>
void Swap(Type &a, Type &b)
{
    Type c = b;
    b = a;
    a = c;
}

// delete element in an array
template <class Type>
bool DeleteElement(Type *pData, int &nNum, const int nID)
{
    if (NULL == pData || nID < 0 || nID >= nNum)
    {
        return false;
    }

    if (nID == nNum - 1)
    {
        nNum--;
        return true;
    }

    for (int i = nID; i < nNum - 1; i++)
    {
        pData[i] = pData[i + 1]; //lint !e676
    }
    memset(&pData[nNum - 1], 0, sizeof(Type));

    nNum--;
    return true;
}

// delete element in an array fast
template <class Type>
bool DeleteElementFast(Type *pData, int &nNum, const int nID)
{
    if (NULL == pData || nID < 0 || nID >= nNum)
    {
        return false;
    }

    if (nID == nNum - 1)
    {
        nNum--;
        return true;
    }

    pData[nID] = pData[nNum - 1];
    nNum--;
    return true;
}

// insert an element into an array
template <class Type>
bool InsertElement(Type *pData, int &nNum, const int nLength,
                   const Type &element, const int nID)
{
    if (NULL == pData || nID < 0 || nID > nNum || nNum >= nLength)
    {
        return false;
    }

    for (int i = nNum - 1; i >= nID; i--)
    {
        pData[i + 1] = pData[i];
    }

    pData[nID] = element;
    nNum++;
    return true;
}

// find local minimum
template <class Type>
bool FindLocalMin(const Type *pValue, const int nLength, bool *pbFlag,
                  const int nStart, const int nEnd,
                  const Type nMaxV, const int nR, const bool bStrict = false)
{
    int i = 0, j = 0;
    bool bFlag = false;

    if (NULL == pValue || NULL == pbFlag)
    {
        return false;
    }

    if (0 > nStart || nEnd > nLength || 0 >= nR)
    {
        return false;
    }

    memset(pbFlag + nStart, false, (nEnd - nStart) * sizeof(bool));
    if (nR * 2 >= nLength)
    {
        return false;
    }

    // find local minimal value
    for (i = nStart; i < nEnd; i++)
    {
        if (pValue[i] >= nMaxV)
        {
            continue;
        }

        bFlag = true;
        for (j = 1; j <= nR; j++)
        {
            if (i - j < 0)
            {
                break;
            }
            if (pValue[i - j] == pValue[i] && bStrict)
            {
                bFlag = false;
                break;
            }
            if (pValue[i] > pValue[i - j])
            {
                bFlag = false;
                break;
            }
        }
        if (bFlag)
        {
            for (j = 1; j <= nR; j++)
            {
                if (i + j >= nLength)
                {
                    break;
                }
                if (pValue[i + j] == pValue[i] && bStrict)
                {
                    bFlag = false;
                    break;
                }
                if (pValue[i] > pValue[i + j])
                {
                    bFlag = false;
                    break;
                }
            }
        }
        pbFlag[i] = bFlag;
    }

    return true;
}

// find local maximum
template <class Type>
bool FindLocalMax(const Type *pValue, const int nLength, bool *pbFlag,
                  const int nStart, const int nEnd,
                  const Type nMinV, const int nR, const bool bStrict = false)
{
    int i = 0, j = 0;
    bool bFlag = false;
    Type nTh = (Type) 0;

    if (NULL == pValue || NULL == pbFlag)
    {
        return false;
    }

    if (0 > nStart || nEnd > nLength || 0 >= nR)
    {
        return false;
    }

    memset(pbFlag + nStart, false, (nEnd - nStart) * sizeof(bool));
    if (nR * 2 >= nLength)
    {
        return false;
    }

    // find local maximal value
    for (i = nStart; i < nEnd; i++)
    {
        if (pValue[i] <= nMinV)
        {
            continue;
        }
        nTh = pValue[i] / 4;

        bFlag = true;
        for (j = 1; j <= nR; j++)
        {
            if (i - j < 0)
            {
                break;
            }
            if (pValue[i - j] < nTh && !bStrict)
            {
                break;
            }
            if (pValue[i] < pValue[i - j])
            {
                bFlag = false;
                break;
            }
        }
        if (bFlag)
        {
            for (j = 1; j <= nR; j++)
            {
                if (i + j >= nLength)
                {
                    break;
                }
                if (pValue[i + j] < nTh && !bStrict)
                {
                    break;
                }
                if (pValue[i] <= pValue[i + j])
                {
                    bFlag = false;
                    break;
                }
            }
        }
        pbFlag[i] = bFlag;
    }

    return true;
}

// find local maximum and minimum at the same time
template <class Type>
bool FindLocalMinMax(const Type *pValue, const int nLength, bool *pFlag,
                     const int nStart, const int nEnd, const int nR)
{
    int j = 0;
    bool bFlag = false;

    if (NULL == pValue || NULL == pFlag)
    {
        return false;
    }

    if (nStart < 0 || nEnd > nLength || 0 >= nR)
    {
        return false;
    }

    memset(pFlag + nStart, false, (nEnd - nStart) * sizeof(pFlag[0])); //lint !e730
    if (nR * 2 >= nLength)
    {
        return false;
    }
    // find local maximal value
    for (int i = nStart; i < nEnd; i++)
    {
        int nLft = max(nStart, i - nR);
        int nRgt = min(nEnd - 1, i + nR);
        if (0 < pValue[i])
        {
            bFlag = true;
            for (j = i - 1; j >= nLft; j--)
            {
                if (pValue[i] <= pValue[j])
                {
                    bFlag = false;
                    break;
                }
            }
            if (bFlag)
            {
                for (j = i + 1; j <= nRgt; j++)
                {
                    if (pValue[i] < pValue[j])
                    {
                        bFlag = false;
                        break;
                    }
                }
            }
            pFlag[i] = bFlag;
        }
        else if (0 > pValue[i])
        {
            bFlag = true;
            for (j = i - 1; j >= nLft; j--)
            {
                if (pValue[i] >= pValue[j])
                {
                    bFlag = false;
                    break;
                }
            }
            if (bFlag)
            {
                for (j = i + 1; j <= nRgt; j++)
                {
                    if (pValue[i] > pValue[j])
                    {
                        bFlag = false;
                        break;
                    }
                }
            }
            pFlag[i] = bFlag;
        }
    }

    return true;
}

// calculate precise position according to three neighbors
template<class T>
float GetSubPosition(const T nCur, const T nPre, const T nNext)
{
    T f = nNext + nPre - 2 * nCur;
    if (f > -M_EPS && f < M_EPS)
    {
        return 0.0f;
    }
    return (float) ((nPre - nNext) / (f * 2.0));
}

// calculate precise position using Gaussian method
template<class T>
float GetSubPositionGauss(const T nCur, const T nPre, const T nNext)
{
    if (0 > nCur || 0 > nPre || 0 > nNext)
    {
        return GetSubPosition(nCur, nPre, nNext);
    }
    float fCur = log10(nCur + 1);
    float fPre = log10(nPre + 1);
    float fNext = log10(nNext + 1);
    float f = fNext + fPre - 2.0 * fCur;
    if (f > -M_EPS && f < M_EPS)
    {
        return 0.0;
    }
    return (fPre - fNext) / (f * 2);
}

template<class T>
float GetSubPosition(const T *pData, const int nLen, const int nCur,
                     const int nLeft, const int nRight, const T th)
{
    if (NULL == pData || 0 >= nCur || nLen - 1 <= nCur || 0 >= nLen
        || 0 > nLeft || nLen - 1 < nRight || nLeft >= nRight)
    {
        return 0.0f;
    }

    int nStart = max(nLeft, nCur - 1);
    int nEnd = min(nRight, nCur + 1);
    T sum = pData[nCur];
    while (pData[nStart] > th)
    {
        sum += pData[nStart];
        nStart--;
        if (nStart <= nLeft)
        {
            break;
        }
    }
    while (pData[nEnd] > th)
    {
        sum += pData[nEnd];
        nEnd++;
        if (nEnd >= nRight)
        {
            break;
        }
    }

    float fAve = (float) sum / max(1, nEnd - nStart - 1);
    float fPos = 0.0f, fWeight = 0.0f;
    for (int i = nStart + 1; i < nEnd; i++)
    {
        if (pData[i] < fAve + 0.1f)
        {
            continue;
        }
        fPos += i * (pData[i] - fAve);
        fWeight += pData[i] - fAve;
    }

    if (fWeight > 0.1f)
    {
        return fPos / fWeight;
    }
    else
    {
        float f = (float) (pData[nCur + 1] + pData[nCur - 1] - 2.0f * pData[nCur]);
        if (M_EPS >= fabs(f))
        {
            return 0.0f;
        }
        return (pData[nCur - 1] - pData[nCur + 1]) / (f * 2);
    }
}

template <class T>
void BiInsert(T *data, const int nLen)
{
    T value = (T) 0;
    int index = 0;
    int low = 0, high = 0, mid = 0, j = 0;
    for (int i = 1; i < nLen; i++)
    {
        low = 0;
        high = i - 1;
        mid = 0;
        value = data[i];
        while (low <= high)
        {
            mid = (low + high) / 2;
            if (data[mid] > value)
            {
                high = mid - 1;
            }
            else
            {
                low = mid + 1;
            }
        }

        for (j = i - 1; j >= high; j--)
        {
            data[j + 1] = data[j];
        }
        data[high + 1] = value;
    }
}

template <class T>
void BiInsert(T *data, const int nMaxLen, const int nLen, const T &add)
{
    if (nLen >= nMaxLen)
    {
        return;
    }

    int low = 0, high = nLen - 1, mid = 0;
    while (low <= high)
    {
        mid = (low + high) / 2;
        if (data[mid] > add)
        {
            high = mid - 1;
        }
        else
        {
            low = mid + 1;
        }
    }

    for (int j = nLen - 1; j >= high; j--)
    {
        data[j + 1] = data[j];
    }

    data[high + 1] = add;
}

template <class T>
int BiSearch(T *data, const int nLen, const T &item)
{
    int low = 0, high = nLen - 1, mid = 0;
    while (low <= high)
    {
        mid = (low + high) / 2;
        if (data[mid] > item)
        {
            high = mid - 1;
        }
        else
        {
            low = mid + 1;
        }
    }

    if (data[high] == item)
    {
        return high;
    }
    else
    {
        return -1;
    }
}

// get data in a rect
template <class T>
bool GetRectData(const T *pData, const int nWidth, const int nHeight,
                 const int nX, const int nY, const int nW, const int nH,
                 T *pRect)
{
    if (NULL == pData || NULL == pRect)
    {
        return false;
    }

    for (int i = 0; i < nH; ++i)
    {
        int y = nY + i - nH / 2;
        if (0 > y || nHeight - 1 < y)
        {
            y = nY * 2 - y;
        }

        const int nIndex = y * nWidth;
        for (int j = 0; j < nW; ++j)
        {
            int x = nX + j - nW / 2;
            if (0 > x || nWidth - 1 < x)
            {
                x = nX * 2 - x;
            }

            *pRect++ = pData[nIndex + x];
        }
    }

    return true;
}

template <class T>
bool GetColData(const T *pData, const int nWidth, const int nHeight,
                const int nX, const int nY, const int nXOffset, const int nH,
                T *pCol)
{
    if (NULL == pData || NULL == pCol)
    {
        return false;
    }

    for (int i = 0; i < nH; ++i)
    {
        int y = nY + i - nH / 2;
        if (0 > y || nHeight - 1 < y)
        {
            y = nY * 2 - y;
        }

        const int nIndex = y * nWidth;
        int x = nX + nXOffset;
        if (0 > x || nWidth - 1 < x)
        {
            x = nX * 2 - x;
        }

        *pCol++ = pData[nIndex + x];
    }

    return true;
}

template <class T>
bool GetRowData(const T *pData, const int nWidth, const int nHeight,
                const int nX, const int nY, const int nW, const int nYOffset,
                T *pRow)
{
    if (NULL == pData || NULL == pRow)
    {
        return false;
    }

    int y = nY + nYOffset;
    if (0 > y || nHeight - 1 < y)
    {
        y = nY * 2 - y;
    }
    const int nIndex = y * nWidth;
    for (int i = 0; i < nW; ++i)
    {
        int x = nX + i - nW / 2;
        if (0 > x || nWidth - 1 < x)
        {
            x = nX * 2 - x;
        }

        *pRow++ = pData[nIndex + x];
    }

    return true;
}

// calculate gray range in a rectangle (max - min)
// nW >= 3, nH >= 3
template <class T>
bool CalcGrayRange(const T *pData, const int nWidth, const int nHeight,
                   const int nW, const int nH, T *pRange)
{
    if (NULL == pData || NULL == pRange || 0 >= nWidth || 0 >= nHeight
        || 3 > nW || 3 > nH || nW > nWidth / 2 || nH > nHeight / 2)
    {
        return false;
    }

    const int nLen = nW * nH;
    T *pSort = mnew T[(nLen + nH + nW) * 2];
    if (NULL == pSort)
    {
        return false;
    }
    T *pBak = pSort + nLen;
    T *pColL = pBak + nLen;
    T *pColR = pColL + nH;
    T *pRowT = pColR + nH;
    T *pRowB = pRowT + nW;

    // get initial data and sort them
    GetRectData(pData, nWidth, nHeight, 0, 0, nW, nH, pBak);
    sort(pBak, pBak + nLen);

    // set the first row to be zeros
    memset(pRange, 0, sizeof(T) * nWidth);
    pRange += nWidth;

    for (int i = 1; i < nHeight; ++i)
    {
        // copy the last row's data to current row
        memcpy(pSort, pBak, sizeof(T) * nLen);

        // get the first row data of (i-1)-row and the last row data of i-row
        GetRowData(pData, nWidth, nHeight, 0, i, nW, nH / 2 - nH, pRowT);
        GetRowData(pData, nWidth, nHeight, 0, i, nW, nH / 2, pRowB);

        // remove old row data and insert new row data
        for (int m = 0; m < nW; ++m)
        {
            if (pRowT[m] == pRowB[m])
            {
                continue;
            }

            // binary search
            int n = BiSearch(pSort, nLen, pRowT[m]);
            int nTmp = nLen;
            // remove previous row's data
            DeleteElement(pSort, nTmp, n);
            // insert the new row's data
            BiInsert(pSort, nLen, nLen - 1, pRowB[m]);
        }

        // backup current row for further using
        memcpy(pBak, pSort, sizeof(T) * nLen);

        // save maximum and minimum
        *pRange++ = pSort[nLen - 1] - pSort[0];

        for (int j = 1; j < nWidth; ++j)
        {
            // get the first column data of (j-1)-column and the last row data of j-column
            GetColData(pData, nWidth, nHeight, j, i, nW / 2 - nW, nH, pColL);
            GetColData(pData, nWidth, nHeight, j, i, nW / 2, nH, pColR);

            // remove old column data and insert new column data
            for (int m = 0; m < nH; ++m)
            {
                if (pColL[m] == pColR[m])
                {
                    continue;
                }

                // binary search
                int n = BiSearch(pSort, nLen, pColL[m]);
                int nTmp = nLen;
                // remove previous column's data
                DeleteElement(pSort, nTmp, n);
                // insert the new column's data
                BiInsert(pSort, nLen, nLen - 1, pColR[m]);
            }

            // save maximum and minimum
            *pRange++ = pSort[nLen - 1] - pSort[0];
        }
    }

    mdelete(pSort);
    return true;
}

// shift FFT
template <class T>
bool FFTShift(T *pstFFT, const int nWidth, const int nHeight)
{
    if (NULL == pstFFT || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    T *pstShifted = new T[nWidth * nHeight];
    if (NULL == pstShifted)
    {
        return false;
    }
    memcpy(pstShifted, pstFFT, sizeof(T) * nWidth * nHeight);

    const int nHalfH = nHeight / 2, nHalfW = nWidth / 2;
    const int nDeltaH = nHeight % 2, nDeltaW = nWidth % 2;

    // 1 2
    // 4 3
    const int nIndexDalta1 = nHalfH * nWidth + nHalfW;
    const int nIndexDalta2 = nHalfH * nWidth - (nWidth - nHalfW);
    const int nIndexDalta3 = (nHeight - nHalfH) * nWidth + (nWidth - nHalfW);
    const int nIndexDalta4 = (nHeight - nHalfH) * nWidth - nHalfW;

    for (int i = 0; i < nHalfH + nDeltaH; ++i)
    {
        const int nIndex1 = i * nWidth;
        const int nIndex2 = (nHeight - 1 - i) * nWidth;
        for (int j = 0; j < nHalfW + nDeltaW; ++j)
        {
            pstFFT[nIndex1 + j + nIndexDalta1] = pstShifted[nIndex1 + j];

            // central line (both vertical and horizontal)
            if (nHalfH != i && nHalfW != j)
            {
                pstFFT[nIndex1 + (nWidth - 1 - j) + nIndexDalta2] =
                    pstShifted[nIndex1 + (nWidth - 1 - j)];
                pstFFT[nIndex2 + j - nIndexDalta4] = pstShifted[nIndex2 + j];
                pstFFT[nIndex2 + (nWidth - 1 - j) - nIndexDalta3] =
                    pstShifted[nIndex2 + (nWidth - 1 - j)];
            }
            else if (nHalfW == j && nHalfH != i)
            {
                pstFFT[nIndex2 + j - nIndexDalta4] = pstShifted[nIndex2 + j];
            }
            else if (nHalfH == i && nHalfW != j)
            {
                pstFFT[nIndex1 + (nWidth - 1 - j) + nIndexDalta2] =
                    pstShifted[nIndex1 + (nWidth - 1 - j)];
            }
        }
    }

    delete [] pstShifted;
    return true;
}

// class for managing memory (delete memory automatically)
template<class T>
class Mem_
{
public:
    Mem_()
    {
        pData = NULL;
        nSize = 0;
    }
    ~Mem_()
    {
        mdelete(pData);
    }
    T *operator [](const int n)
    {
        mdelete(pData);
        if (0 < n)
        {
            pData = mnew T[n];
			memset(pData, 0, n * sizeof(T));
        }
        nSize = (NULL == pData) ? 0 : n;
        return pData;
    }
    T *Data(void)
    {
        return pData;
    }
    const T *Data(void) const
    {
        return pData;
    }
    int Size(void) const
    {
        return nSize;
    }
    void Release(T* &p)
    {
        nSize = 0;
        mdelete(pData);
        if (NULL != p)
        {
            p = NULL;
        }
    }
    void Release(void)
    {
        nSize = 0;
        mdelete(pData);
    }
private:
    T *pData;
    int nSize;
};

typedef Mem_<uchar> Mem;
typedef Mem_<int> Memn;
typedef Mem_<bool> Memb;
typedef Mem_<char> Memc;
typedef Mem_<float> Memf;

#endif // __MLEI_TEMPLATE_EX_H__
