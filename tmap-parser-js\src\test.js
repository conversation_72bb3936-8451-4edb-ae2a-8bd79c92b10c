/**
 * Test script for TMAP Parser
 */

import { TmapParser, getTmapFileInfo, globalPerformanceMonitor } from './index.js';
import chalk from 'chalk';
import { table } from 'table';

// Test file path
const TEST_FILE = 'E:\\TMAP\\Test_1.TMAP';

/**
 * Run all tests
 */
async function runTests() {
  console.log(chalk.blue.bold('🧪 TMAP Parser Test Suite'));
  console.log(chalk.blue('═'.repeat(40)));
  
  const tests = [
    { name: 'Basic File Info Test', fn: testBasicFileInfo },
    { name: 'Full Parse Test', fn: testFullParse },
    { name: 'Performance Test', fn: testPerformance },
    { name: 'Error Handling Test', fn: testErrorHandling }
  ];
  
  const results = [];
  
  for (const test of tests) {
    console.log(chalk.yellow(`\n🔍 Running: ${test.name}`));
    
    try {
      const startTime = Date.now();
      await test.fn();
      const duration = Date.now() - startTime;
      
      console.log(chalk.green(`✅ ${test.name} passed (${duration}ms)`));
      results.push({ name: test.name, status: 'PASS', duration });
    } catch (error) {
      console.log(chalk.red(`❌ ${test.name} failed: ${error.message}`));
      results.push({ name: test.name, status: 'FAIL', error: error.message });
    }
  }
  
  // Display summary
  displayTestSummary(results);
  
  // Display performance metrics
  displayPerformanceMetrics();
}

/**
 * Test basic file info functionality
 */
async function testBasicFileInfo() {
  console.log('  📖 Testing basic file info...');
  
  const info = await getTmapFileInfo(TEST_FILE);
  
  // Validate required fields
  const requiredFields = [
    'filePath', 'version', 'versionString', 'totalWidth', 'totalHeight',
    'scanMagnification', 'pixelSize', 'slideType', 'colorDepth'
  ];
  
  for (const field of requiredFields) {
    if (!(field in info)) {
      throw new Error(`Missing required field: ${field}`);
    }
  }
  
  // Validate data types and ranges
  if (typeof info.version !== 'number' || info.version < 5.0 || info.version >= 7.0) {
    throw new Error(`Invalid version: ${info.version}`);
  }
  
  if (typeof info.totalWidth !== 'number' || info.totalWidth <= 0) {
    throw new Error(`Invalid total width: ${info.totalWidth}`);
  }
  
  if (typeof info.totalHeight !== 'number' || info.totalHeight <= 0) {
    throw new Error(`Invalid total height: ${info.totalHeight}`);
  }
  
  if (typeof info.scanMagnification !== 'number' || info.scanMagnification <= 0) {
    throw new Error(`Invalid scan magnification: ${info.scanMagnification}`);
  }
  
  console.log(`  ✓ Version: ${info.versionString}`);
  console.log(`  ✓ Dimensions: ${info.totalWidth} × ${info.totalHeight}`);
  console.log(`  ✓ Magnification: ${info.scanMagnification}×`);
  console.log(`  ✓ Pixel Size: ${info.pixelSize} μm/pixel`);
}

/**
 * Test full file parsing
 */
async function testFullParse() {
  console.log('  🔍 Testing full file parsing...');
  
  const parser = new TmapParser();
  const result = await parser.parseFile(TEST_FILE);
  
  // Validate comprehensive results
  const requiredFields = [
    'filePath', 'fileSize', 'version', 'versionString',
    'totalWidth', 'totalHeight', 'imageWidth', 'imageHeight',
    'tileWidth', 'tileHeight', 'imageColumns', 'imageRows',
    'totalImages', 'shrinkTileCount', 'scanMagnification',
    'pixelSize', 'focusLayers', 'colorDepth', 'backgroundColor',
    'slideType', 'fileCount', 'layerCount', 'ratioStep',
    'hasExtensionData', 'extensionTypes', 'performanceMetrics'
  ];
  
  for (const field of requiredFields) {
    if (!(field in result)) {
      throw new Error(`Missing required field in full parse: ${field}`);
    }
  }
  
  // Validate file size
  if (typeof result.fileSize !== 'number' || result.fileSize <= 0) {
    throw new Error(`Invalid file size: ${result.fileSize}`);
  }
  
  // Validate tile information
  if (typeof result.tileWidth !== 'number' || result.tileWidth <= 0) {
    throw new Error(`Invalid tile width: ${result.tileWidth}`);
  }
  
  if (typeof result.tileHeight !== 'number' || result.tileHeight <= 0) {
    throw new Error(`Invalid tile height: ${result.tileHeight}`);
  }
  
  // Validate image counts
  if (typeof result.totalImages !== 'number' || result.totalImages < 0) {
    throw new Error(`Invalid total images: ${result.totalImages}`);
  }
  
  console.log(`  ✓ File size: ${formatBytes(result.fileSize)}`);
  console.log(`  ✓ Total images: ${result.totalImages.toLocaleString()}`);
  console.log(`  ✓ Shrink tiles: ${result.shrinkTileCount.toLocaleString()}`);
  console.log(`  ✓ Tile size: ${result.tileWidth} × ${result.tileHeight}`);
  console.log(`  ✓ Extension data: ${result.hasExtensionData ? 'Yes' : 'No'}`);
}

/**
 * Test performance monitoring
 */
async function testPerformance() {
  console.log('  ⚡ Testing performance monitoring...');
  
  // Clear previous metrics
  globalPerformanceMonitor.clear();
  
  // Run a parsing operation
  const parser = new TmapParser();
  await parser.parseFile(TEST_FILE);
  
  // Check if metrics were recorded
  const summary = globalPerformanceMonitor.getSummary();
  
  if (summary.totalOperations === 0) {
    throw new Error('No performance metrics recorded');
  }
  
  if (typeof summary.totalDuration !== 'string') {
    throw new Error('Invalid total duration format');
  }
  
  if (typeof summary.avgDuration !== 'string') {
    throw new Error('Invalid average duration format');
  }
  
  console.log(`  ✓ Operations recorded: ${summary.totalOperations}`);
  console.log(`  ✓ Total duration: ${summary.totalDuration}ms`);
  console.log(`  ✓ Average duration: ${summary.avgDuration}ms`);
  console.log(`  ✓ Memory usage: ${summary.totalMemoryUsed}MB`);
}

/**
 * Test error handling
 */
async function testErrorHandling() {
  console.log('  🚫 Testing error handling...');
  
  // Test with non-existent file
  try {
    await getTmapFileInfo('non-existent-file.tmap');
    throw new Error('Should have thrown error for non-existent file');
  } catch (error) {
    if (!error.message.includes('ENOENT') && !error.message.includes('no such file')) {
      throw new Error(`Unexpected error for non-existent file: ${error.message}`);
    }
    console.log('  ✓ Non-existent file error handled correctly');
  }
  
  // Test with invalid file (create a temporary invalid file)
  const fs = await import('fs/promises');
  const invalidFile = 'temp-invalid.tmap';
  
  try {
    await fs.writeFile(invalidFile, 'INVALID_TMAP_DATA');
    
    try {
      await getTmapFileInfo(invalidFile);
      throw new Error('Should have thrown error for invalid TMAP file');
    } catch (error) {
      if (!error.message.includes('Invalid TMAP file')) {
        throw new Error(`Unexpected error for invalid file: ${error.message}`);
      }
      console.log('  ✓ Invalid TMAP file error handled correctly');
    }
  } finally {
    // Clean up temporary file
    try {
      await fs.unlink(invalidFile);
    } catch (e) {
      // Ignore cleanup errors
    }
  }
}

/**
 * Display test summary
 */
function displayTestSummary(results) {
  console.log(chalk.blue.bold('\n📊 Test Summary'));
  console.log(chalk.blue('═'.repeat(40)));
  
  const passed = results.filter(r => r.status === 'PASS').length;
  const failed = results.filter(r => r.status === 'FAIL').length;
  
  console.log(`${chalk.green('✅ Passed:')} ${passed}`);
  console.log(`${chalk.red('❌ Failed:')} ${failed}`);
  console.log(`${chalk.blue('📋 Total:')} ${results.length}`);
  
  // Display detailed results
  const tableData = [['Test Name', 'Status', 'Duration/Error']];
  
  results.forEach(result => {
    const status = result.status === 'PASS' 
      ? chalk.green('PASS') 
      : chalk.red('FAIL');
    
    const detail = result.status === 'PASS' 
      ? `${result.duration}ms`
      : result.error;
    
    tableData.push([result.name, status, detail]);
  });
  
  console.log('\n' + table(tableData));
}

/**
 * Display performance metrics
 */
function displayPerformanceMetrics() {
  console.log(chalk.yellow.bold('\n⚡ Performance Metrics'));
  console.log(chalk.yellow('═'.repeat(40)));
  
  const summary = globalPerformanceMonitor.getSummary();
  
  if (summary.totalOperations === 0) {
    console.log(chalk.gray('No performance data available'));
    return;
  }
  
  const data = [
    ['Total Operations', summary.totalOperations],
    ['Total Duration', `${summary.totalDuration}ms`],
    ['Average Duration', `${summary.avgDuration}ms`],
    ['Max Duration', `${summary.maxDuration}ms`],
    ['Min Duration', `${summary.minDuration}ms`],
    ['Total Memory Used', `${summary.totalMemoryUsed}MB`],
    ['Average Memory Used', `${summary.avgMemoryUsed}MB`]
  ];
  
  console.log(table(data));
}

/**
 * Format bytes to human readable string
 */
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().catch(error => {
    console.error(chalk.red('Test suite failed:'), error);
    process.exit(1);
  });
}
