﻿生成启动时间为 2025/2/28 10:29:34。
     1>项目“E:\code\work\youyun\DEV-build-ok\DEV\iviewersdk\CommonEx\CommonEx.vcxproj”在节点 2 上(Rebuild 个目标)。
     1>ClCompile:
         D:\softInstance\vsstduio\VC\bin\x86_amd64\CL.exe /c /I../../3rdtool/opencv/include /nologo /W4 /WX- /O2 /Ot /D WIN64 /D NDEBUG /D _WINDOWS /D _USRDLL /D COMMONEX_EXPORTS /D _WINDLL /D _MBCS /Gm- /EHsc /MT /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /Fo"x64\Release\\" /Fd"x64\Release\vc140.pdb" /Gd /TP /errorReport:prompt Angle.cpp CommonEx.cpp Frequency.cpp OpenCVBased.cpp Trace.cpp
         Angle.cpp
         CommonEx.cpp
     1>CommonEx.cpp(3456): warning C4457: “nX”的声明隐藏了函数参数
         CommonEx.cpp(3380): note: 参见“nX”的声明
     1>CommonEx.cpp(3457): warning C4457: “nY”的声明隐藏了函数参数
         CommonEx.cpp(3380): note: 参见“nY”的声明
     1>CommonEx.cpp(5646): warning C4312: “类型转换”: 从“const int”转换到更大的“char *”
         Frequency.cpp
     1>Frequency.cpp(735): warning C4456: “dY”的声明隐藏了上一个本地声明
         Frequency.cpp(723): note: 参见“dY”的声明
     1>Frequency.cpp(738): warning C4456: “dX”的声明隐藏了上一个本地声明
         Frequency.cpp(722): note: 参见“dX”的声明
     1>Frequency.cpp(767): warning C4456: “dY”的声明隐藏了上一个本地声明
         Frequency.cpp(723): note: 参见“dY”的声明
     1>Frequency.cpp(770): warning C4456: “dX”的声明隐藏了上一个本地声明
         Frequency.cpp(722): note: 参见“dX”的声明
         OpenCVBased.cpp
     1>OpenCVBased.cpp(548): warning C4100: “nPitch”: 未引用的形参
     1>OpenCVBased.cpp(548): warning C4100: “nHeight”: 未引用的形参
     1>OpenCVBased.cpp(548): warning C4100: “nWidth”: 未引用的形参
     1>OpenCVBased.cpp(548): warning C4100: “pucImg”: 未引用的形参
         Trace.cpp
     1>Trace.cpp(623): warning C4457: “nWidth”的声明隐藏了函数参数
         Trace.cpp(496): note: 参见“nWidth”的声明
     1>Trace.cpp(624): warning C4457: “nHeight”的声明隐藏了函数参数
         Trace.cpp(496): note: 参见“nHeight”的声明
         正在生成代码...
       Link:
         D:\softInstance\vsstduio\VC\bin\x86_amd64\link.exe /ERRORREPORT:PROMPT /OUT:"../../output/x64/Release/CommonEx.dll" /INCREMENTAL:NO /NOLOGO ../../3rdtool/opencv/lib/opencv_world340.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:"../../output/x64/Release/CommonEx.pdb" /SUBSYSTEM:WINDOWS /OPT:REF /OPT:ICF /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"../../lib/x64/Release/CommonEx.lib" /MACHINE:X64 /DLL x64\Release\Angle.obj
         x64\Release\CommonEx.obj
         x64\Release\Frequency.obj
         x64\Release\OpenCVBased.obj
         x64\Release\Trace.obj
           正在创建库 ../../lib/x64/Release/CommonEx.lib 和对象 ../../lib/x64/Release/CommonEx.exp
         CommonEx.vcxproj -> E:\code\work\youyun\DEV-build-ok\DEV\iviewersdk\CommonEx\../../output/x64/Release/CommonEx.dll
     1>已完成生成项目“E:\code\work\youyun\DEV-build-ok\DEV\iviewersdk\CommonEx\CommonEx.vcxproj”(Rebuild 个目标)的操作。

已成功生成。

已用时间 00:00:03.34
