{"version": 3, "file": "properties.js", "sourceRoot": "", "sources": ["../../../lib/vocabularies/applicator/properties.ts"], "names": [], "mappings": ";;AACA,qDAAiD;AACjD,kCAA2D;AAC3D,6CAA4E;AAC5E,iEAA0C;AAE1C,MAAM,GAAG,GAA0B;IACjC,OAAO,EAAE,YAAY;IACrB,IAAI,EAAE,QAAQ;IACd,UAAU,EAAE,QAAQ;IACpB,IAAI,CAAC,GAAe;QAClB,MAAM,EAAC,GAAG,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,EAAE,EAAC,GAAG,GAAG,CAAA;QACjD,IAAI,EAAE,CAAC,IAAI,CAAC,gBAAgB,KAAK,KAAK,IAAI,YAAY,CAAC,oBAAoB,KAAK,SAAS,EAAE,CAAC;YAC1F,8BAAK,CAAC,IAAI,CAAC,IAAI,qBAAU,CAAC,EAAE,EAAE,8BAAK,EAAE,sBAAsB,CAAC,CAAC,CAAA;QAC/D,CAAC;QACD,MAAM,QAAQ,GAAG,IAAA,0BAAmB,EAAC,MAAM,CAAC,CAAA;QAC5C,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;YAC5B,EAAE,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QAChC,CAAC;QACD,IAAI,EAAE,CAAC,IAAI,CAAC,WAAW,IAAI,QAAQ,CAAC,MAAM,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;YAChE,EAAE,CAAC,KAAK,GAAG,qBAAc,CAAC,KAAK,CAAC,GAAG,EAAE,IAAA,aAAM,EAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;QAClE,CAAC;QACD,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAA,wBAAiB,EAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAC5E,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC;YAAE,OAAM;QACnC,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAE/B,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;YAC9B,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrB,mBAAmB,CAAC,IAAI,CAAC,CAAA;YAC3B,CAAC;iBAAM,CAAC;gBACN,GAAG,CAAC,EAAE,CAAC,IAAA,qBAAc,EAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAA;gBAC9D,mBAAmB,CAAC,IAAI,CAAC,CAAA;gBACzB,IAAI,CAAC,EAAE,CAAC,SAAS;oBAAE,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;gBAC9C,GAAG,CAAC,KAAK,EAAE,CAAA;YACb,CAAC;YACD,GAAG,CAAC,EAAE,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;YAClC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;QACf,CAAC;QAED,SAAS,UAAU,CAAC,IAAY;YAC9B,OAAO,EAAE,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,EAAE,CAAC,aAAa,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,SAAS,CAAA;QACvF,CAAC;QAED,SAAS,mBAAmB,CAAC,IAAY;YACvC,GAAG,CAAC,SAAS,CACX;gBACE,OAAO,EAAE,YAAY;gBACrB,UAAU,EAAE,IAAI;gBAChB,QAAQ,EAAE,IAAI;aACf,EACD,KAAK,CACN,CAAA;QACH,CAAC;IACH,CAAC;CACF,CAAA;AAED,kBAAe,GAAG,CAAA"}