/**
* @date         2014-02-17
* @filename     Angle.cpp
* @purpose      commonly used functions based on opencv (angle module)
* @version      1.0
* @history      initial draft
* <AUTHOR> Beijing, China
* @copyright    <EMAIL>, 2009-2014. All rights reserved.
*/

#include <cmath>
#include <algorithm>
#include <opencv2/opencv.hpp>
#include "./CommonEx.h"

using namespace std;

extern bool g_bExpired;

#ifndef UWN_ANGLE_PRECICE
#define UWN_ANGLE_PRECICE    64
#endif
#ifndef UWN_MAX_TRACE_STEP
#define UWN_MAX_TRACE_STEP    24
#endif
#ifndef UN_ANGLE_PI
#define UN_ANGLE_PI            128
#endif
#ifndef UN_ANGLE_2PI
#define UN_ANGLE_2PI        256
#endif

// arctan
int g_ArcTan[UWN_ANGLE_PRECICE][UWN_ANGLE_PRECICE] =
{{0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0},
{64,32,19,13,10,8,7,6,5,5,4,4,3,3,3,3,3,2,2,2,2,2,2,2,2,2,2,2,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1},
{64,45,32,24,19,16,13,11,10,9,8,7,7,6,6,5,5,5,5,4,4,4,4,4,3,3,3,3,3,3,3,3,3,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,1,1,1,1,1,1,1,1,1},
{64,51,40,32,26,22,19,16,15,13,12,11,10,9,9,8,8,7,7,6,6,6,6,5,5,5,5,5,4,4,4,4,4,4,4,3,3,3,3,3,3,3,3,3,3,3,3,3,3,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2},
{64,54,45,38,32,27,24,21,19,17,16,14,13,12,11,11,10,9,9,8,8,8,7,7,7,6,6,6,6,6,5,5,5,5,5,5,5,4,4,4,4,4,4,4,4,4,4,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3},
{64,56,48,42,37,32,28,25,23,21,19,17,16,15,14,13,12,12,11,10,10,10,9,9,8,8,8,7,7,7,7,7,6,6,6,6,6,5,5,5,5,5,5,5,5,5,4,4,4,4,4,4,4,4,4,4,4,4,4,3,3,3,3,3},
{64,57,51,45,40,36,32,29,26,24,22,20,19,18,16,16,15,14,13,12,12,11,11,10,10,10,9,9,9,8,8,8,8,7,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,4,4,4,4,4,4,4,4,4},
{64,58,53,48,43,39,35,32,29,27,25,23,22,20,19,18,17,16,15,14,14,13,13,12,12,11,11,10,10,10,9,9,9,9,8,8,8,8,7,7,7,7,7,7,6,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5},
{64,59,54,49,45,41,38,35,32,30,27,26,24,22,21,20,19,18,17,16,16,15,14,14,13,13,12,12,11,11,11,10,10,10,9,9,9,9,8,8,8,8,8,7,7,7,7,7,7,7,6,6,6,6,6,6,6,6,6,5,5,5,5,5},
{64,59,55,51,47,43,40,37,34,32,30,28,26,25,23,22,21,20,19,18,17,16,16,15,15,14,14,13,13,12,12,12,11,11,11,10,10,10,9,9,9,9,9,8,8,8,8,8,8,7,7,7,7,7,7,7,6,6,6,6,6,6,6,6},
{64,60,56,52,48,45,42,39,37,34,32,30,28,27,25,24,23,22,21,20,19,18,17,17,16,16,15,14,14,14,13,13,12,12,12,11,11,11,10,10,10,10,10,9,9,9,9,9,8,8,8,8,8,8,7,7,7,7,7,7,7,7,7,6},
{64,60,57,53,50,47,44,41,38,36,34,32,30,29,27,26,25,23,22,21,20,20,19,18,18,17,16,16,15,15,14,14,13,13,13,12,12,12,11,11,11,11,10,10,10,10,10,9,9,9,9,9,8,8,8,8,8,8,8,8,7,7,7,7},
{64,61,57,54,51,48,45,42,40,38,36,34,32,30,29,27,26,25,24,23,22,21,20,20,19,18,18,17,16,16,16,15,15,14,14,13,13,13,12,12,12,12,11,11,11,11,10,10,10,10,10,9,9,9,9,9,9,8,8,8,8,8,8,8},
{64,61,58,55,52,49,46,44,42,39,37,35,34,32,30,29,28,27,25,24,23,23,22,21,20,20,19,18,18,17,17,16,16,15,15,14,14,14,13,13,13,13,12,12,12,11,11,11,11,11,10,10,10,10,10,9,9,9,9,9,9,9,8,8},
{64,61,58,55,53,50,48,45,43,41,39,37,35,34,32,31,29,28,27,26,25,24,23,22,22,21,20,19,19,18,18,17,17,16,16,16,15,15,14,14,14,13,13,13,13,12,12,12,12,11,11,11,11,11,10,10,10,10,10,9,9,9,9,9},
{64,61,59,56,53,51,48,46,44,42,40,38,37,35,33,32,31,29,28,27,26,25,24,24,23,22,21,21,20,19,19,18,18,17,17,16,16,16,15,15,15,14,14,14,13,13,13,13,12,12,12,12,11,11,11,11,11,10,10,10,10,10,10,10},
{64,61,59,56,54,52,49,47,45,43,41,39,38,36,35,33,32,31,30,29,27,27,26,25,24,23,22,22,21,21,20,19,19,18,18,17,17,17,16,16,16,15,15,15,14,14,14,13,13,13,13,12,12,12,12,12,11,11,11,11,11,10,10,10},
{64,62,59,57,55,52,50,48,46,44,42,41,39,37,36,35,33,32,31,30,29,28,27,26,25,24,24,23,22,22,21,20,20,19,19,18,18,18,17,17,16,16,16,15,15,15,14,14,14,14,13,13,13,13,12,12,12,12,12,11,11,11,11,11},
{64,62,59,57,55,53,51,49,47,45,43,42,40,39,37,36,34,33,32,31,30,29,28,27,26,25,25,24,23,23,22,21,21,20,20,19,19,18,18,18,17,17,16,16,16,16,15,15,15,14,14,14,14,13,13,13,13,12,12,12,12,12,12,11},
{64,62,60,58,56,54,52,50,48,46,44,43,41,40,38,37,35,34,33,32,31,30,29,28,27,26,26,25,24,24,23,22,22,21,21,20,20,19,19,18,18,18,17,17,17,16,16,16,15,15,15,15,14,14,14,14,13,13,13,13,12,12,12,12},
{64,62,60,58,56,54,52,50,48,47,45,44,42,41,39,38,37,35,34,33,32,31,30,29,28,27,27,26,25,25,24,23,23,22,22,21,21,20,20,19,19,18,18,18,17,17,17,16,16,16,16,15,15,15,14,14,14,14,14,13,13,13,13,13},
{64,62,60,58,56,54,53,51,49,48,46,44,43,41,40,39,37,36,35,34,33,32,31,30,29,28,28,27,26,26,25,24,24,23,23,22,22,21,21,20,20,19,19,19,18,18,17,17,17,16,16,16,16,15,15,15,15,14,14,14,14,14,13,13},
{64,62,60,58,57,55,53,51,50,48,47,45,44,42,41,40,38,37,36,35,34,33,32,31,30,29,29,28,27,26,26,25,25,24,23,23,22,22,21,21,20,20,20,19,19,19,18,18,18,17,17,17,16,16,16,16,15,15,15,15,14,14,14,14},
{64,62,60,59,57,55,54,52,50,49,47,46,44,43,42,40,39,38,37,36,35,34,33,32,31,30,30,29,28,27,27,26,25,25,24,24,23,23,22,22,21,21,20,20,20,19,19,19,18,18,18,17,17,17,16,16,16,16,15,15,15,15,14,14},
{64,62,61,59,57,56,54,52,51,49,48,46,45,44,42,41,40,39,38,37,36,35,34,33,32,31,30,30,29,28,27,27,26,26,25,24,24,23,23,22,22,22,21,21,20,20,20,19,19,19,18,18,18,17,17,17,16,16,16,16,16,15,15,15},
{64,62,61,59,58,56,54,53,51,50,48,47,46,44,43,42,41,40,39,38,37,36,35,34,33,32,31,30,30,29,28,28,27,26,26,25,25,24,24,23,23,22,22,21,21,21,20,20,20,19,19,19,18,18,18,17,17,17,17,16,16,16,16,15},
{64,62,61,59,58,56,55,53,52,50,49,48,46,45,44,43,42,40,39,38,37,36,35,34,34,33,32,31,30,30,29,28,28,27,27,26,25,25,24,24,23,23,23,22,22,21,21,21,20,20,20,19,19,19,18,18,18,17,17,17,17,16,16,16},
{64,62,61,59,58,57,55,54,52,51,50,48,47,46,45,43,42,41,40,39,38,37,36,35,34,34,33,32,31,31,30,29,29,28,27,27,26,26,25,25,24,24,23,23,22,22,22,21,21,21,20,20,20,19,19,19,18,18,18,17,17,17,17,16},
{64,63,61,60,58,57,55,54,53,51,50,49,48,46,45,44,43,42,41,40,39,38,37,36,35,34,34,33,32,31,31,30,29,29,28,27,27,26,26,25,25,24,24,24,23,23,22,22,22,21,21,20,20,20,19,19,19,19,18,18,18,18,17,17},
{64,63,61,60,58,57,56,54,53,52,50,49,48,47,46,45,43,42,41,40,39,38,38,37,36,35,34,33,33,32,31,31,30,29,29,28,28,27,27,26,26,25,25,24,24,23,23,23,22,22,21,21,21,20,20,20,19,19,19,19,18,18,18,18},
{64,63,61,60,59,57,56,55,53,52,51,50,48,47,46,45,44,43,42,41,40,39,38,37,37,36,35,34,33,33,32,31,31,30,29,29,28,28,27,27,26,26,25,25,24,24,24,23,23,22,22,22,21,21,21,20,20,20,19,19,19,19,18,18},
{64,63,61,60,59,57,56,55,54,52,51,50,49,48,47,46,45,44,43,42,41,40,39,38,37,36,36,35,34,33,33,32,31,31,30,30,29,28,28,27,27,26,26,25,25,25,24,24,23,23,23,22,22,22,21,21,21,20,20,20,19,19,19,19},
{64,63,61,60,59,58,56,55,54,53,52,51,49,48,47,46,45,44,43,42,41,40,39,39,38,37,36,35,35,34,33,33,32,31,31,30,30,29,29,28,27,27,27,26,26,25,25,24,24,24,23,23,22,22,22,21,21,21,21,20,20,20,19,19},
{64,63,62,60,59,58,57,55,54,53,52,51,50,49,48,47,46,45,44,43,42,41,40,39,38,38,37,36,35,35,34,33,33,32,31,31,30,30,29,29,28,28,27,27,26,26,25,25,25,24,24,23,23,23,22,22,22,21,21,21,20,20,20,20},
{64,63,62,60,59,58,57,56,55,53,52,51,50,49,48,47,46,45,44,43,42,41,41,40,39,38,37,37,36,35,35,34,33,33,32,31,31,30,30,29,29,28,28,27,27,26,26,26,25,25,24,24,24,23,23,23,22,22,22,21,21,21,20,20},
{64,63,62,61,59,58,57,56,55,54,53,52,51,50,48,48,47,46,45,44,43,42,41,40,40,39,38,37,37,36,35,34,34,33,33,32,31,31,30,30,29,29,28,28,27,27,27,26,26,25,25,25,24,24,23,23,23,22,22,22,22,21,21,21},
{64,63,62,61,59,58,57,56,55,54,53,52,51,50,49,48,47,46,45,44,43,42,42,41,40,39,39,38,37,36,36,35,34,34,33,33,32,31,31,30,30,29,29,28,28,27,27,27,26,26,25,25,25,24,24,24,23,23,23,22,22,22,21,21},
{64,63,62,61,60,59,57,56,55,54,53,52,51,50,49,48,47,46,46,45,44,43,42,41,41,40,39,38,38,37,36,36,35,34,34,33,33,32,31,31,30,30,29,29,28,28,28,27,27,26,26,26,25,25,24,24,24,23,23,23,23,22,22,22},
{64,63,62,61,60,59,58,57,56,55,54,53,52,51,50,49,48,47,46,45,44,43,43,42,41,40,40,39,38,37,37,36,35,35,34,34,33,33,32,31,31,30,30,29,29,29,28,28,27,27,26,26,26,25,25,25,24,24,24,23,23,23,22,22},
{64,63,62,61,60,59,58,57,56,55,54,53,52,51,50,49,48,47,46,46,45,44,43,42,42,41,40,39,39,38,37,37,36,35,35,34,34,33,33,32,31,31,30,30,30,29,29,28,28,27,27,27,26,26,25,25,25,24,24,24,23,23,23,23},
{64,63,62,61,60,59,58,57,56,55,54,53,52,51,50,49,48,48,47,46,45,44,44,43,42,41,41,40,39,38,38,37,37,36,35,35,34,34,33,33,32,31,31,31,30,30,29,29,28,28,27,27,27,26,26,26,25,25,25,24,24,24,23,23},
{64,63,62,61,60,59,58,57,56,55,54,53,52,51,51,50,49,48,47,46,46,45,44,43,42,42,41,40,40,39,38,38,37,36,36,35,35,34,34,33,33,32,32,31,31,30,30,29,29,28,28,28,27,27,26,26,26,25,25,25,24,24,24,24},
{64,63,62,61,60,59,58,57,56,55,54,54,53,52,51,50,49,48,48,47,46,45,44,44,43,42,41,41,40,39,39,38,37,37,36,36,35,35,34,34,33,32,32,32,31,31,30,30,29,29,28,28,28,27,27,27,26,26,26,25,25,25,24,24},
{64,63,62,61,60,59,58,57,57,56,55,54,53,52,51,50,49,49,48,47,46,45,45,44,43,43,42,41,40,40,39,39,38,37,37,36,36,35,35,34,33,33,32,32,32,31,31,30,30,29,29,29,28,28,27,27,27,26,26,26,25,25,25,24},
{64,63,62,61,60,59,58,58,57,56,55,54,53,52,51,51,50,49,48,47,47,46,45,44,44,43,42,42,41,40,40,39,38,38,37,37,36,36,35,34,34,33,33,32,32,32,31,31,30,30,29,29,29,28,28,27,27,27,26,26,26,25,25,25},
{64,63,62,61,60,59,59,58,57,56,55,54,53,53,52,51,50,49,48,48,47,46,45,45,44,43,43,42,41,41,40,39,39,38,38,37,37,36,35,35,34,34,33,33,32,32,32,31,31,30,30,29,29,29,28,28,28,27,27,27,26,26,26,25},
{64,63,62,61,60,60,59,58,57,56,55,54,54,53,52,51,50,50,49,48,47,47,46,45,44,44,43,42,42,41,40,40,39,39,38,37,37,36,36,35,35,34,34,33,33,32,32,32,31,31,30,30,30,29,29,28,28,28,27,27,27,26,26,26},
{64,63,62,61,61,60,59,58,57,56,55,55,54,53,52,51,51,50,49,48,48,47,46,45,45,44,43,43,42,41,41,40,40,39,38,38,37,37,36,36,35,35,34,34,33,33,32,32,32,31,31,30,30,30,29,29,28,28,28,27,27,27,26,26},
{64,63,62,61,61,60,59,58,57,56,56,55,54,53,52,52,51,50,49,49,48,47,46,46,45,44,44,43,42,42,41,41,40,39,39,38,38,37,37,36,36,35,35,34,34,33,33,32,32,32,31,31,30,30,30,29,29,29,28,28,27,27,27,27},
{64,63,62,62,61,60,59,58,57,57,56,55,54,53,53,52,51,50,50,49,48,48,47,46,45,45,44,43,43,42,42,41,40,40,39,39,38,38,37,37,36,36,35,35,34,34,33,33,32,32,32,31,31,30,30,30,29,29,29,28,28,28,27,27},
{64,63,62,62,61,60,59,58,58,57,56,55,54,54,53,52,51,51,50,49,48,48,47,46,46,45,44,44,43,43,42,41,41,40,40,39,39,38,38,37,37,36,36,35,35,34,34,33,33,32,32,32,31,31,30,30,30,29,29,29,28,28,28,27},
{64,63,62,62,61,60,59,58,58,57,56,55,55,54,53,52,52,51,50,49,49,48,47,47,46,45,45,44,44,43,42,42,41,41,40,39,39,38,38,37,37,36,36,35,35,35,34,34,33,33,32,32,32,31,31,30,30,30,29,29,29,28,28,28},
{64,63,62,62,61,60,59,59,58,57,56,56,55,54,53,53,52,51,50,50,49,48,48,47,46,46,45,44,44,43,43,42,42,41,40,40,39,39,38,38,37,37,36,36,35,35,34,34,34,33,33,32,32,32,31,31,30,30,30,29,29,29,28,28},
{64,63,62,62,61,60,59,59,58,57,56,56,55,54,53,53,52,51,51,50,49,49,48,47,47,46,45,45,44,44,43,42,42,41,41,40,40,39,39,38,38,37,37,36,36,35,35,34,34,34,33,33,32,32,32,31,31,31,30,30,29,29,29,28},
{64,63,62,62,61,60,59,59,58,57,57,56,55,54,54,53,52,52,51,50,50,49,48,48,47,46,46,45,45,44,43,43,42,42,41,41,40,40,39,39,38,38,37,37,36,36,35,35,34,34,34,33,33,32,32,32,31,31,31,30,30,30,29,29},
{64,63,63,62,61,60,60,59,58,57,57,56,55,55,54,53,52,52,51,50,50,49,48,48,47,47,46,45,45,44,44,43,43,42,41,41,40,40,39,39,38,38,37,37,37,36,36,35,35,34,34,34,33,33,32,32,32,31,31,31,30,30,30,29},
{64,63,63,62,61,60,60,59,58,58,57,56,55,55,54,53,53,52,51,51,50,49,49,48,48,47,46,46,45,45,44,43,43,42,42,41,41,40,40,39,39,38,38,37,37,36,36,36,35,35,34,34,34,33,33,32,32,32,31,31,31,30,30,30},
{64,63,63,62,61,60,60,59,58,58,57,56,56,55,54,54,53,52,52,51,50,50,49,48,48,47,47,46,45,45,44,44,43,43,42,42,41,41,40,40,39,39,38,38,37,37,36,36,35,35,35,34,34,33,33,33,32,32,32,31,31,31,30,30},
{64,63,63,62,61,60,60,59,58,58,57,56,56,55,54,54,53,52,52,51,50,50,49,49,48,47,47,46,46,45,45,44,43,43,42,42,41,41,40,40,39,39,38,38,38,37,37,36,36,35,35,35,34,34,33,33,33,32,32,32,31,31,31,30},
{64,63,63,62,61,61,60,59,59,58,57,56,56,55,55,54,53,53,52,51,51,50,49,49,48,48,47,47,46,45,45,44,44,43,43,42,42,41,41,40,40,39,39,38,38,37,37,37,36,36,35,35,35,34,34,33,33,33,32,32,32,31,31,31},
{64,63,63,62,61,61,60,59,59,58,57,57,56,55,55,54,53,53,52,52,51,50,50,49,48,48,47,47,46,46,45,45,44,44,43,42,42,41,41,41,40,40,39,39,38,38,37,37,37,36,36,35,35,35,34,34,33,33,33,32,32,32,31,31},
{64,63,63,62,61,61,60,59,59,58,57,57,56,55,55,54,54,53,52,52,51,50,50,49,49,48,48,47,46,46,45,45,44,44,43,43,42,42,41,41,40,40,39,39,39,38,38,37,37,36,36,36,35,35,34,34,34,33,33,33,32,32,32,31},
{64,63,63,62,61,61,60,59,59,58,57,57,56,56,55,54,54,53,52,52,51,51,50,50,49,48,48,47,47,46,46,45,45,44,44,43,43,42,42,41,41,40,40,39,39,38,38,38,37,37,36,36,36,35,35,34,34,34,33,33,33,32,32,32},
{64,63,63,62,61,61,60,59,59,58,58,57,56,56,55,54,54,53,53,52,51,51,50,50,49,49,48,48,47,46,46,45,45,44,44,43,43,42,42,41,41,40,40,40,39,39,38,38,37,37,37,36,36,36,35,35,34,34,34,33,33,33,32,32}
};

int g_DeltaX[UN_ANGLE_PI][UWN_MAX_TRACE_STEP] =
{
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22},
    {0,1,2,3,4,4,5,6,7,8,9,10,11,11,12,13,14,15,16,17,18,18,19,20},
    {0,1,2,2,3,4,5,6,7,7,8,9,10,11,11,12,13,14,15,15,16,17,18,19},
    {0,1,2,2,3,4,5,5,6,7,8,8,9,10,11,11,12,13,14,14,15,16,17,17},
    {0,1,1,2,3,3,4,5,6,6,7,8,8,9,10,10,11,12,12,13,14,14,15,16},
    {0,1,1,2,3,3,4,4,5,6,6,7,8,8,9,9,10,11,11,12,13,13,14,14},
    {0,1,1,2,2,3,3,4,5,5,6,6,7,7,8,8,9,10,10,11,11,12,12,13},
    {0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12},
    {0,0,1,1,2,2,3,3,4,4,4,5,5,6,6,7,7,7,8,8,9,9,10,10},
    {0,0,1,1,2,2,2,3,3,3,4,4,5,5,5,6,6,6,7,7,8,8,8,9},
    {0,0,1,1,1,2,2,2,3,3,3,3,4,4,4,5,5,5,6,6,6,7,7,7},
    {0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,6,6},
    {0,0,0,1,1,1,1,1,2,2,2,2,2,2,3,3,3,3,3,4,4,4,4,4},
    {0,0,0,0,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,3,3,3,3},
    {0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1},
    {0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0},
    {0,0,0,0,0,0,0,0,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1},
    {0,0,0,0,-1,-1,-1,-1,-1,-1,-1,-1,-2,-2,-2,-2,-2,-2,-2,-2,-3,-3,-3,-3},
    {0,0,0,-1,-1,-1,-1,-1,-2,-2,-2,-2,-2,-2,-3,-3,-3,-3,-3,-4,-4,-4,-4,-4},
    {0,0,-1,-1,-1,-1,-2,-2,-2,-2,-3,-3,-3,-3,-4,-4,-4,-4,-5,-5,-5,-5,-6,-6},
    {0,0,-1,-1,-1,-2,-2,-2,-3,-3,-3,-3,-4,-4,-4,-5,-5,-5,-6,-6,-6,-7,-7,-7},
    {0,0,-1,-1,-2,-2,-2,-3,-3,-3,-4,-4,-5,-5,-5,-6,-6,-6,-7,-7,-8,-8,-8,-9},
    {0,0,-1,-1,-2,-2,-3,-3,-4,-4,-4,-5,-5,-6,-6,-7,-7,-7,-8,-8,-9,-9,-10,-10},
    {0,-1,-1,-2,-2,-3,-3,-4,-4,-5,-5,-6,-6,-7,-7,-8,-8,-9,-9,-10,-10,-11,-11,-12},
    {0,-1,-1,-2,-2,-3,-3,-4,-5,-5,-6,-6,-7,-7,-8,-8,-9,-10,-10,-11,-11,-12,-12,-13},
    {0,-1,-1,-2,-3,-3,-4,-4,-5,-6,-6,-7,-8,-8,-9,-9,-10,-11,-11,-12,-13,-13,-14,-14},
    {0,-1,-1,-2,-3,-3,-4,-5,-6,-6,-7,-8,-8,-9,-10,-10,-11,-12,-12,-13,-14,-14,-15,-16},
    {0,-1,-2,-2,-3,-4,-5,-5,-6,-7,-8,-8,-9,-10,-11,-11,-12,-13,-14,-14,-15,-16,-17,-17},
    {0,-1,-2,-2,-3,-4,-5,-6,-7,-7,-8,-9,-10,-11,-11,-12,-13,-14,-15,-15,-16,-17,-18,-19},
    {0,-1,-2,-3,-4,-4,-5,-6,-7,-8,-9,-10,-11,-11,-12,-13,-14,-15,-16,-17,-18,-18,-19,-20},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22},
    {0,-1,-2,-3,-4,-4,-5,-6,-7,-8,-9,-10,-11,-11,-12,-13,-14,-15,-16,-17,-18,-18,-19,-20},
    {0,-1,-2,-2,-3,-4,-5,-6,-7,-7,-8,-9,-10,-11,-11,-12,-13,-14,-15,-15,-16,-17,-18,-19},
    {0,-1,-2,-2,-3,-4,-5,-5,-6,-7,-8,-8,-9,-10,-11,-11,-12,-13,-14,-14,-15,-16,-17,-17},
    {0,-1,-1,-2,-3,-3,-4,-5,-6,-6,-7,-8,-8,-9,-10,-10,-11,-12,-12,-13,-14,-14,-15,-16},
    {0,-1,-1,-2,-3,-3,-4,-4,-5,-6,-6,-7,-8,-8,-9,-9,-10,-11,-11,-12,-13,-13,-14,-14},
    {0,-1,-1,-2,-2,-3,-3,-4,-5,-5,-6,-6,-7,-7,-8,-8,-9,-10,-10,-11,-11,-12,-12,-13},
    {0,-1,-1,-2,-2,-3,-3,-4,-4,-5,-5,-6,-6,-7,-7,-8,-8,-9,-9,-10,-10,-11,-11,-12},
    {0,0,-1,-1,-2,-2,-3,-3,-4,-4,-4,-5,-5,-6,-6,-7,-7,-7,-8,-8,-9,-9,-10,-10},
    {0,0,-1,-1,-2,-2,-2,-3,-3,-3,-4,-4,-5,-5,-5,-6,-6,-6,-7,-7,-8,-8,-8,-9},
    {0,0,-1,-1,-1,-2,-2,-2,-3,-3,-3,-3,-4,-4,-4,-5,-5,-5,-6,-6,-6,-7,-7,-7},
    {0,0,-1,-1,-1,-1,-2,-2,-2,-2,-3,-3,-3,-3,-4,-4,-4,-4,-5,-5,-5,-5,-6,-6},
    {0,0,0,-1,-1,-1,-1,-1,-2,-2,-2,-2,-2,-2,-3,-3,-3,-3,-3,-4,-4,-4,-4,-4},
    {0,0,0,0,-1,-1,-1,-1,-1,-1,-1,-1,-2,-2,-2,-2,-2,-2,-2,-2,-3,-3,-3,-3},
    {0,0,0,0,0,0,0,0,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1},
    {0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0},
    {0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1},
    {0,0,0,0,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,3,3,3,3},
    {0,0,0,1,1,1,1,1,2,2,2,2,2,2,3,3,3,3,3,4,4,4,4,4},
    {0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,6,6},
    {0,0,1,1,1,2,2,2,3,3,3,3,4,4,4,5,5,5,6,6,6,7,7,7},
    {0,0,1,1,2,2,2,3,3,3,4,4,5,5,5,6,6,6,7,7,8,8,8,9},
    {0,0,1,1,2,2,3,3,4,4,4,5,5,6,6,7,7,7,8,8,9,9,10,10},
    {0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12},
    {0,1,1,2,2,3,3,4,5,5,6,6,7,7,8,8,9,10,10,11,11,12,12,13},
    {0,1,1,2,3,3,4,4,5,6,6,7,8,8,9,9,10,11,11,12,13,13,14,14},
    {0,1,1,2,3,3,4,5,6,6,7,8,8,9,10,10,11,12,12,13,14,14,15,16},
    {0,1,2,2,3,4,5,5,6,7,8,8,9,10,11,11,12,13,14,14,15,16,17,17},
    {0,1,2,2,3,4,5,6,7,7,8,9,10,11,11,12,13,14,15,15,16,17,18,19},
    {0,1,2,3,4,4,5,6,7,8,9,10,11,11,12,13,14,15,16,17,18,18,19,20},
    {0,1,2,3,4,5,6,7,8,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23}
};

int g_DeltaY[UN_ANGLE_PI][UWN_MAX_TRACE_STEP] =
{
    {0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0},
    {0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1},
    {0,0,0,0,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,3,3,3,3},
    {0,0,0,1,1,1,1,1,2,2,2,2,2,2,3,3,3,3,3,4,4,4,4,4},
    {0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,6,6},
    {0,0,1,1,1,2,2,2,3,3,3,3,4,4,4,5,5,5,6,6,6,7,7,7},
    {0,0,1,1,2,2,2,3,3,3,4,4,5,5,5,6,6,6,7,7,8,8,8,9},
    {0,0,1,1,2,2,3,3,4,4,4,5,5,6,6,7,7,7,8,8,9,9,10,10},
    {0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12},
    {0,1,1,2,2,3,3,4,5,5,6,6,7,7,8,8,9,10,10,11,11,12,12,13},
    {0,1,1,2,3,3,4,4,5,6,6,7,8,8,9,9,10,11,11,12,13,13,14,14},
    {0,1,1,2,3,3,4,5,6,6,7,8,8,9,10,10,11,12,12,13,14,14,15,16},
    {0,1,2,2,3,4,5,5,6,7,8,8,9,10,11,11,12,13,14,14,15,16,17,17},
    {0,1,2,2,3,4,5,6,7,7,8,9,10,11,11,12,13,14,15,15,16,17,18,19},
    {0,1,2,3,4,4,5,6,7,8,9,10,11,11,12,13,14,15,16,17,18,18,19,20},
    {0,1,2,3,4,5,6,7,8,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23},
    {0,1,2,3,4,5,6,7,8,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22},
    {0,1,2,3,4,4,5,6,7,8,9,10,11,11,12,13,14,15,16,17,18,18,19,20},
    {0,1,2,2,3,4,5,6,7,7,8,9,10,11,11,12,13,14,15,15,16,17,18,19},
    {0,1,2,2,3,4,5,5,6,7,8,8,9,10,11,11,12,13,14,14,15,16,17,17},
    {0,1,1,2,3,3,4,5,6,6,7,8,8,9,10,10,11,12,12,13,14,14,15,16},
    {0,1,1,2,3,3,4,4,5,6,6,7,8,8,9,9,10,11,11,12,13,13,14,14},
    {0,1,1,2,2,3,3,4,5,5,6,6,7,7,8,8,9,10,10,11,11,12,12,13},
    {0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12},
    {0,0,1,1,2,2,3,3,4,4,4,5,5,6,6,7,7,7,8,8,9,9,10,10},
    {0,0,1,1,2,2,2,3,3,3,4,4,5,5,5,6,6,6,7,7,8,8,8,9},
    {0,0,1,1,1,2,2,2,3,3,3,3,4,4,4,5,5,5,6,6,6,7,7,7},
    {0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,6,6},
    {0,0,0,1,1,1,1,1,2,2,2,2,2,2,3,3,3,3,3,4,4,4,4,4},
    {0,0,0,0,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,3,3,3,3},
    {0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1},
    {0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0},
    {0,0,0,0,0,0,0,0,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1},
    {0,0,0,0,-1,-1,-1,-1,-1,-1,-1,-1,-2,-2,-2,-2,-2,-2,-2,-2,-3,-3,-3,-3},
    {0,0,0,-1,-1,-1,-1,-1,-2,-2,-2,-2,-2,-2,-3,-3,-3,-3,-3,-4,-4,-4,-4,-4},
    {0,0,-1,-1,-1,-1,-2,-2,-2,-2,-3,-3,-3,-3,-4,-4,-4,-4,-5,-5,-5,-5,-6,-6},
    {0,0,-1,-1,-1,-2,-2,-2,-3,-3,-3,-3,-4,-4,-4,-5,-5,-5,-6,-6,-6,-7,-7,-7},
    {0,0,-1,-1,-2,-2,-2,-3,-3,-3,-4,-4,-5,-5,-5,-6,-6,-6,-7,-7,-8,-8,-8,-9},
    {0,0,-1,-1,-2,-2,-3,-3,-4,-4,-4,-5,-5,-6,-6,-7,-7,-7,-8,-8,-9,-9,-10,-10},
    {0,-1,-1,-2,-2,-3,-3,-4,-4,-5,-5,-6,-6,-7,-7,-8,-8,-9,-9,-10,-10,-11,-11,-12},
    {0,-1,-1,-2,-2,-3,-3,-4,-5,-5,-6,-6,-7,-7,-8,-8,-9,-10,-10,-11,-11,-12,-12,-13},
    {0,-1,-1,-2,-3,-3,-4,-4,-5,-6,-6,-7,-8,-8,-9,-9,-10,-11,-11,-12,-13,-13,-14,-14},
    {0,-1,-1,-2,-3,-3,-4,-5,-6,-6,-7,-8,-8,-9,-10,-10,-11,-12,-12,-13,-14,-14,-15,-16},
    {0,-1,-2,-2,-3,-4,-5,-5,-6,-7,-8,-8,-9,-10,-11,-11,-12,-13,-14,-14,-15,-16,-17,-17},
    {0,-1,-2,-2,-3,-4,-5,-6,-7,-7,-8,-9,-10,-11,-11,-12,-13,-14,-15,-15,-16,-17,-18,-19},
    {0,-1,-2,-3,-4,-4,-5,-6,-7,-8,-9,-10,-11,-11,-12,-13,-14,-15,-16,-17,-18,-18,-19,-20},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23},
    {0,-1,-2,-3,-4,-5,-6,-7,-8,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22},
    {0,-1,-2,-3,-4,-4,-5,-6,-7,-8,-9,-10,-11,-11,-12,-13,-14,-15,-16,-17,-18,-18,-19,-20},
    {0,-1,-2,-2,-3,-4,-5,-6,-7,-7,-8,-9,-10,-11,-11,-12,-13,-14,-15,-15,-16,-17,-18,-19},
    {0,-1,-2,-2,-3,-4,-5,-5,-6,-7,-8,-8,-9,-10,-11,-11,-12,-13,-14,-14,-15,-16,-17,-17},
    {0,-1,-1,-2,-3,-3,-4,-5,-6,-6,-7,-8,-8,-9,-10,-10,-11,-12,-12,-13,-14,-14,-15,-16},
    {0,-1,-1,-2,-3,-3,-4,-4,-5,-6,-6,-7,-8,-8,-9,-9,-10,-11,-11,-12,-13,-13,-14,-14},
    {0,-1,-1,-2,-2,-3,-3,-4,-5,-5,-6,-6,-7,-7,-8,-8,-9,-10,-10,-11,-11,-12,-12,-13},
    {0,-1,-1,-2,-2,-3,-3,-4,-4,-5,-5,-6,-6,-7,-7,-8,-8,-9,-9,-10,-10,-11,-11,-12},
    {0,0,-1,-1,-2,-2,-3,-3,-4,-4,-4,-5,-5,-6,-6,-7,-7,-7,-8,-8,-9,-9,-10,-10},
    {0,0,-1,-1,-2,-2,-2,-3,-3,-3,-4,-4,-5,-5,-5,-6,-6,-6,-7,-7,-8,-8,-8,-9},
    {0,0,-1,-1,-1,-2,-2,-2,-3,-3,-3,-3,-4,-4,-4,-5,-5,-5,-6,-6,-6,-7,-7,-7},
    {0,0,-1,-1,-1,-1,-2,-2,-2,-2,-3,-3,-3,-3,-4,-4,-4,-4,-5,-5,-5,-5,-6,-6},
    {0,0,0,-1,-1,-1,-1,-1,-2,-2,-2,-2,-2,-2,-3,-3,-3,-3,-3,-4,-4,-4,-4,-4},
    {0,0,0,0,-1,-1,-1,-1,-1,-1,-1,-1,-2,-2,-2,-2,-2,-2,-2,-2,-3,-3,-3,-3},
    {0,0,0,0,0,0,0,0,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1}
};

//--------------------------------------------------------------------
//    NAME:        ComputeAngle
//  PARAMS:
//  RETURN:
//  FUNCTION:    get angle(0 ~ 255) from x&y distance
//---------------------------------------------------------------------
// compute angle (basic function)
void ComputeAngle(const int nDx, const int nDy, uchar &chAng)
{
    int nDxAbs = abs(nDx);
    int nDyAbs = abs(nDy);
    int nMaxAbs = max(nDxAbs, nDyAbs);

    int nQuotient = nMaxAbs / 64 + 1;
    nDyAbs /= nQuotient;
    nDxAbs /= nQuotient;
    chAng = (uchar) g_ArcTan[nDyAbs][nDxAbs];

    if (nDx > 0 && nDy < 0)
    {
        chAng = (uchar) (UN_ANGLE_2PI - chAng);
    }
    else if (nDx < 0)
    {
        if (nDy > 0)
            chAng = UN_ANGLE_PI - chAng;
        else if (nDy <= 0)
            chAng = UN_ANGLE_PI + chAng;
    }
    else
    {
        if (nDy < 0)
            chAng = UN_ANGLE_PI + chAng;
        else if (nDy == 0)
            chAng = 0;
    }
}

// compute angle according to gray information
void CalcGrayAngle(const uchar **pucImg, const int nWidth, const int nHeight,
                   const int x, const int y, uchar &nAngle)
{
    if (g_bExpired || NULL == pucImg || x <= 0 || y <= 0 || x >= nWidth - 1 || y >= nHeight - 1)
    {
        return;
    }

    int nD1 = pucImg[y + 1][x + 1] - pucImg[y - 1][x - 1];
    int nD2 = pucImg[y + 1][x - 1] - pucImg[y - 1][x + 1];

    // Sobel algorithm
    int nGy = nD1 + nD2 + ((pucImg[y + 1][x] - pucImg[y - 1][x]) * 2);
    int nGx = nD1 - nD2 + ((pucImg[y][x + 1] - pucImg[y][x - 1]) * 2);

    ComputeAngle(nGx, nGy, nAngle);
}

// compute angle according to gray information
void CalcGrayAngle(const uchar *pImg, const int nWidth, const int nHeight,
                   const int x, const int y, uchar &nAngle)
{
    if (g_bExpired || NULL == pImg || x <= 0 || y <= 0 || x >= nWidth - 1 || y >= nHeight - 1)
    {
        return;
    }

    int index = y * nWidth + x;
    int nD1 = pImg[index + nWidth + 1] - pImg[index - nWidth - 1];
    int nD2 = pImg[index + nWidth - 1] - pImg[index - nWidth + 1];

    // Sobel algorithm
    int nGy = nD1 + nD2 + ((pImg[index + nWidth] - pImg[index - nWidth]) * 2);
    int nGx = nD1 - nD2 + ((pImg[index + 1] - pImg[index - 1]) * 2);

    ComputeAngle(nGx, nGy, nAngle);
}

// compute angle according to position
void CalcPosAngle(const float x, const float y, const int j, const int i, uchar &nAngle)
{
    const float dx = j - x, dy = i - y;
    ComputeAngle(cvRound(dx), cvRound(dy), nAngle);
}
