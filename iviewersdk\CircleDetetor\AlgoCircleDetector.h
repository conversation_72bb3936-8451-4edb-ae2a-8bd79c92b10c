/**
* @date         2014-07-16
* @filename     AlgoExample.h
* @purpose      example class for using CAlgoInterface class
* @version      1.0
* @history      initial draft
* <AUTHOR> UNIC, Beijing, China
* @copyright    UNIC Technologies, 2005-2014. All rights reserved.
*/

#ifndef __ALGO_CIRCLE_DETECTOR_H__
#define __ALGO_CIRCLE_DETECTOR_H__

#include "./AlgoInterface.h"
#include "./CircleDetectorCore.h"

#pragma warning(disable:4275)
#pragma warning(disable:4251)

class CAlgoInterface;
class CircleDetector;

class CAlgoCircleDetector : public CAlgoInterface
{
 public:
    /**
    * @method       CAlgoCircleDetector::CAlgoCircleDetector
    * @access       public 
    * @brief        
    * @param        void
    * @return       
    * <AUTHOR> <EMAIL>
    * @date         2014-07-22
    * @history      initial draft
    */
    CAlgoCircleDetector(void);

    /**
    * @method       CAlgoCircleDetector::~CAlgoCircleDetector
    * @access       public 
    * @brief        
    * @param        void
    * @return       
    * <AUTHOR> Morgan.Le<PERSON>@unic-tech.cn
    * @date         2014-07-22
    * @history      initial draft
    */
    virtual ~CAlgoCircleDetector(void);

    /**
    * @method       CAlgoCircleDetector::PackPara
    * @access       public 
    * @brief        
    * @param        void
    * @return       int
    * <AUTHOR> Lei, <EMAIL>
    * @date         2014-07-22
    * @history      initial draft
    */
    virtual int PackPara(void);

    /**
    * @method       CAlgoCircleDetector::UnpackPara
    * @access       public 
    * @brief        
    * @param        void
    * @return       int
    * <AUTHOR> Lei, <EMAIL>
    * @date         2014-07-22
    * @history      initial draft
    */
    virtual int UnpackPara(void);

    /**
    * @method       CAlgoCircleDetector::SetImage
    * @access       virtual private 
    * @brief        
    * @param        const int nIndex
    * @param        const UN_IMAGE_INFO_S * pstImg
    * @return       int
    * <AUTHOR> Lei, <EMAIL>
    * @date         2014-07-22
    * @history      initial draft
    */
    virtual int SetImage(const int nIndex, const UN_IMAGE_INFO_S *pstImg);

    /**
    * @method       CAlgoCircleDetector::DoInspect
    * @access       public 
    * @brief        do the main job
    * @param        const UN_IMAGE_INFO_S * pstImg
    * @return       int
    * <AUTHOR> Lei, <EMAIL>
    * @date         2014-07-22
    * @history      initial draft
    */
    virtual int DoInspect(const UN_IMAGE_INFO_S *pstImg);

    /**
    * @method       CAlgoCircleDetector::PackResult
    * @access       public 
    * @brief        pack results for displaying in text
    * @param        void
    * @return       int
    * <AUTHOR> Lei, <EMAIL>
    * @date         2014-07-22
    * @history      initial draft
    */
    virtual int PackResult(void);
 
    /**
    * @method       CAlgoCircleDetector::PackDisplay
    * @access       public 
    * @brief        pack elements for display on image
    * @param        void
    * @return       int
    * <AUTHOR> Lei, <EMAIL>
    * @date         2014-07-22
    * @history      initial draft
    */
    virtual int PackDisplay(void);

    /**
    * @method       CAlgoCircleDetector::GetPara
    * @access       public 
    * @brief        
    * @param        CircleDetector::Param & para
    * @return       void
    * <AUTHOR> Lei, <EMAIL>
    * @date         2014-07-22
    * @history      initial draft
    */
    void GetPara(CircleDetector::Param &para) const;

    /**
    * @method       CAlgoCircleDetector::SetPara
    * @access       public 
    * @brief        
    * @param        const CircleDetector::Param & para
    * @return       int
    * <AUTHOR> Lei, <EMAIL>
    * @date         2014-07-22
    * @history      initial draft
    */
    int SetPara(const CircleDetector::Param &para);

    /**
    * @method       CAlgoCircleDetector::GetResults
    * @access       public 
    * @brief        
    * @param        float & fCircleX
    * @param        float & fCircleY
    * @param        float & fCircleR
    * @return       bool
    * <AUTHOR> Lei, <EMAIL>
    * @date         2014-07-22
    * @history      initial draft
    */
    bool GetResults(float &fCircleX, float &fCircleY, float &fCircleR) const;

 private:
    /**
    * @method       CAlgoCircleDetector::SetImage
    * @access       virtual private 
    * @brief        
    * @param        const UN_IMAGE_INFO_S * pstImg
    * @param        const bool bTemplate
    * @return       int
    * <AUTHOR> Lei, <EMAIL>
    * @date         2014-07-22
    * @history      initial draft
    */
    int SetImage(const UN_IMAGE_INFO_S *pstImg, const bool bTemplate); //lint !e1411

    /**
    * @method       CAlgoCircleDetector::IsRoiOk
    * @access       private 
    * @brief        
    * @param        const int nWidth
    * @param        const int nHeight
    * @param        const int nLeft
    * @param        const int nRight
    * @param        const int nTop
    * @param        const int nBottom
    * @return       bool
    * <AUTHOR> Lei, <EMAIL>
    * @date         2014-07-22
    * @history      initial draft
    */
    bool IsRoiOk(const int nWidth, const int nHeight, const int nLeft,
        const int nRight, const int nTop, const int nBottom) const;

    /**
    * @method       CAlgoCircleDetector::CopyGrayData
    * @access       private 
    * @brief        
    * @param        const uchar * pucIn
    * @param        uchar * pucOut
    * @param        const int nWidth
    * @param        const int nHeight
    * @param        const int nPitch
    * @param        const int nLeft
    * @param        const int nRight
    * @param        const int nTop
    * @param        const int nBottom
    * @return       bool
    * <AUTHOR> Lei, <EMAIL>
    * @date         2014-07-22
    * @history      initial draft
    */
    bool CopyGrayData(const uchar *pucIn, uchar *pucOut, const int nWidth,
        const int nHeight, const int nPitch, const int nLeft,
        const int nRight, const int nTop, const int nBottom) const;

    uchar *m_pucImgSrc;         // save gray image of source image
    uchar *m_pucImgTpl;         // template image
    bool m_bHasTemplate;        // has template or not

    int m_nWidth;               // image width
    int m_nHeight;              // image height
    int m_nImgSize;             // size of image

    CircleDetector::Param m_para;                // parameters

    CircleDetector *m_pCircDetect;

    // Output
    float m_fCircleX;
    float m_fCircleY;
    float m_fCircleR;
};

#endif // __ALGO_CIRCLE_DETECTOR_H__
