/**
* @date         2016-03-12
* @filename     TmapHttp.cpp
* @purpose      interface for reading TMAP 7 and above using HTTP method
* @version      1.0
* @history      initial draft
* <AUTHOR> UNIC, Beijing, China
* @copyright    <EMAIL>, UNIC Technologies Inc, 2005-2016. All rights reserved.
*/

#include <QMap>
#include "./TMAPConvert.h"
#include "./iConvertor.h"

QMap<QString, CTmap7Downloader*> g_map;
QMutex g_mutex;

#if 0
CTmapDownloader *GetDownloader(const char *pcFileName)
{
    CTmapDownloader *p = NULL;
    if (g_map.contains(pcFileName))
    {
        p = g_map[pcFileName];
    }
    else
    {
        p = new CTmapDownloader();
        QUrl url = QUrl(pcFileName);
        bool bOk = p->Open(url);
        if (!bOk)
        {
            delete p;
            p = NULL;
        }

        g_map[pcFileName] = p;
    }

    return p;
}
#else
CTmap7Downloader *GetDownloader(const char *pcFileName)
{
    g_mutex.lock();
    CTmap7Downloader *p = NULL;
    if (g_map.contains(pcFileName))
    {
        p = g_map[pcFileName];
        g_mutex.unlock();
    }
    else
    {
        g_mutex.unlock();
        p = new CTmap7Downloader();
        bool bOk = p->Open(pcFileName);
        if (!bOk)
        {
            delete p;
            p = NULL;
            return NULL;
        }

        g_mutex.lock();
        g_map[pcFileName] = p;
        g_mutex.unlock();
    }

    return p;
}
#endif

// get information of TMAP
// pcFileName should start with http://
bool GetTMAPImageInfo(const char* pcFileName, int& nWidth, int& nHeight,
                      int &nScanScale, float &fPixelSizeOf100X)
{
    CTmap7Downloader *p = GetDownloader(pcFileName);
    if (NULL == p)
    {
        return false;
    }

    TMAP_INFO_7 stTmap = p->GetTmapHeader();
    nScanScale = stTmap.stHeader.ucMaxZoomRate;
    fPixelSizeOf100X = stTmap.stHeader.fPixelSize;
    bool bOk = false;
    for (int i = 0; i < MAX_IMAGE_NUM; ++i)
    {
        if (eImageWhole == stTmap.stImages[i].eType)
        {
            nWidth = stTmap.stImages[i].nWidth;
            nHeight = stTmap.stImages[i].nHeight;
            bOk = true;
            break;
        }
    }

    return bOk;
}

// pcFileName should start with http://
int DecodeTileStream(const char* pcFileName, const int nLayer,
                     const int nPosX, const int nPosY, unsigned char* pucData,
                     const int nJpegQuality/* = 85*/)
{
    CTmap7Downloader *p = GetDownloader(pcFileName);
    if (NULL == p)
    {
        return 0;
    }

    TMAP_INFO_7 stTmap = p->GetTmapHeader();
    const int _nLayer = stTmap.stHeader.nLayerNumber - nLayer - 1;
    if (0 > _nLayer || _nLayer >= stTmap.stHeader.nLayerNumber
        || 0 > nPosX || nPosX >= stTmap.stLayers[_nLayer].nTileCol
        || 0 > nPosY || nPosY >= stTmap.stLayers[_nLayer].nTileRow)
    {
        return 0;
    }

    // download tile information
    TILE_S tile;
    int64 iStart = stTmap.stLayers[nLayer].nOffset +
        (nPosY * stTmap.stLayers[_nLayer].nTileCol + nPosX) * sizeof(tile);
    bool bOk = p->Download(iStart, iStart + sizeof(tile), &tile);
    if (!bOk || 0 >= tile.nLength)
    {
        return 0;
    }

    // download tile image data
    bOk = p->Download(tile.lOffset, tile.lOffset + tile.nLength, pucData);
    if (bOk)
    {
        return tile.nLength;
    }
    
    return 0;
}

// pcFileName should start with http://
int GetNaviImgData(const char* pcFileName, unsigned char* pucData,
                   const int nJpegQuality/* = 85*/)
{
    CTmap7Downloader *p = GetDownloader(pcFileName);
    if (NULL == p)
    {
        return 0;
    }

    TMAP_INFO_7 stTmap = p->GetTmapHeader();
    for (int i = 0; i < MAX_IMAGE_NUM; ++i)
    {
        if (eImageNavigate == stTmap.stImages[i].eType)
        {
            int64 iStart = stTmap.stImages[i].lOffset;
            int nLen = stTmap.stImages[i].nLength;
            bool bOk = p->Download(iStart, iStart + nLen, pucData);
            return (bOk ? nLen : 0);
        }
    }

    return 0;
}

// pcFileName should start with http://
int GetMacroImgData(const char* pcFileName, unsigned char* pucData,
                    const int nJpegQuality/* = 85*/)
{
    CTmap7Downloader *p = GetDownloader(pcFileName);
    if (NULL == p)
    {
        return 0;
    }

    TMAP_INFO_7 stTmap = p->GetTmapHeader();
    for (int i = 0; i < MAX_IMAGE_NUM; ++i)
    {
        if (eImageMacroLabel == stTmap.stImages[i].eType)
        {
            int64 iStart = stTmap.stImages[i].lOffset;
            int nLen = stTmap.stImages[i].nLength;
            bool bOk = p->Download(iStart, iStart + nLen, pucData);
            return (bOk ? nLen : 0);
        }
    }

    return 0;
}

// pcFileName should start with http://
int GetLabelImgData(const char* pcFileName, unsigned char* pucData,
                    const int nJpegQuality/* = 85*/)
{
    CTmap7Downloader *p = GetDownloader(pcFileName);
    if (NULL == p)
    {
        return false;
    }

    TMAP_INFO_7 stTmap = p->GetTmapHeader();
    for (int i = 0; i < MAX_IMAGE_NUM; ++i)
    {
        if (eImageLabel == stTmap.stImages[i].eType)
        {
            int64 iStart = stTmap.stImages[i].lOffset;
            int nLen = stTmap.stImages[i].nLength;
            bool bOk = p->Download(iStart, iStart + nLen, pucData);
            return (bOk ? nLen : 0);
        }
    }

    return 0;
}

bool GetExtentFileData(const char *pcFileName, const int nDataType,
                       int *pDataLen, void **pData, void *Paras)
{
    return false;
}

void ReleaseResources()
{
    QStringList list = g_map.keys();
    foreach (QString str, list)
    {
        CTmap7Downloader *p = g_map.take(str);
        delete p;
        p = NULL;
    }

    g_map.clear();
}
