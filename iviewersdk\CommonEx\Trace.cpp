/**
* @date         2014-02-17
* @filename     Trace.cpp
* @purpose      commonly used functions based on opencv (trace module)
* @version      1.0
* @history      initial draft
* <AUTHOR> Beijing, China
* @copyright    <EMAIL>, 2009-2014. All rights reserved.
*/

#include <math.h>
#include "./CommonEx.h"
#include "./Templates.h"

extern bool g_bExpired;

bool TraceRegion(const uchar *pucImg, uchar *pucMask, const int nWidth, const int nHeight,
                 const int x, const int y, const int nMinTh, const int nMaxTh,
                 const uchar ucLabel, TRACE_AREA_S &stTrace, const bool bEightNeighbor/* = false*/)
{
    if (g_bExpired || NULL == pucMask || NULL == pucImg
        || 0 >= nWidth || 0 >= nHeight || pucMask[y * nWidth + x] == ucLabel
        || nMinTh > pucImg[y * nWidth + x] || nMaxTh < pucImg[y * nWidth + x])
    {
        return false;
    }

    const int QUEUESIZE = 4096;
    int anQueue[QUEUESIZE][2];
    int nHead = 0;
    int nTail = 1;
    const DIR_E nDir = bEightNeighbor ? DIR_EIGHT : DIR_FOUR;
    int nX, nY;
    int nGray = pucImg[y * nWidth + x] + 1; // add one for calculating gravity

    anQueue[nHead][1] = y;
    anQueue[nHead][0] = x;
    pucMask[y * nWidth + x] = ucLabel;
    stTrace.nX = x;
    stTrace.nY = y;
    stTrace.nArea = 1;
    stTrace.nGray = nGray;
    stTrace.nMin = nGray - 1;
    stTrace.nMax = nGray - 1;
    stTrace.nXGravity = x * nGray;
    stTrace.nYGravity = y * nGray;
    stTrace.nLeft = x;
    stTrace.nRight = x;
    stTrace.nTop = y;
    stTrace.nBottom = y;
    stTrace.nScore = 0;
    stTrace.nLabel = ucLabel;

    while (nHead != nTail)
    {
        for (int k = 0; k < nDir; k ++)
        {
            nY = anQueue[nHead][1] + g_anDirections[k][1];
            nX = anQueue[nHead][0] + g_anDirections[k][0];
            if (nX < 0 || nY < 0 || nX > nWidth - 1 || nY > nHeight - 1)
            {
                continue;
            }

            int nIndex = nY * nWidth + nX;
            nGray = pucImg[nIndex];
            if (nGray >= nMinTh && nGray <= nMaxTh && pucMask[nIndex] != ucLabel)
            {
                nGray++; // at least one for calculating gravity
                pucMask[nIndex] = ucLabel;
                stTrace.nArea++;
                stTrace.nGray += nGray;
                stTrace.nXGravity += nX * nGray;
                stTrace.nYGravity += nY * nGray;
                stTrace.nLeft = min(stTrace.nLeft, nX);
                stTrace.nRight = max(stTrace.nRight, nX);
                stTrace.nTop = min(stTrace.nTop, nY);
                stTrace.nBottom = max(stTrace.nBottom, nY);
                stTrace.nMin = min(stTrace.nMin, nGray - 1);
                stTrace.nMax = max(stTrace.nMax, nGray - 1);

                anQueue[nTail][0] = nX;
                anQueue[nTail][1] = nY;
                nTail++;
                nTail &= QUEUESIZE-1;
            }
        }
        nHead++;
        nHead &= QUEUESIZE-1;
    }

    stTrace.nXGravity /= max(stTrace.nGray, 1);
    stTrace.nYGravity /= max(stTrace.nGray, 1);
    stTrace.nGray -= stTrace.nArea; // subtract gray level added before
    stTrace.nGray /= stTrace.nArea;
    stTrace.nBottom++;
    stTrace.nRight++;
    return true;
}

// trace one region
bool TraceOneRegion(const uchar *pucImg, uchar *pucMask,
                    const int nWidth, const int nHeight,
                    const int x, const int y, const int nMinTh, const int nMaxTh,
                    const uchar ucLabel, TRACE_AREA_S &stTrace,
                    int pnQueue[][2], const int QUEUESIZE, const bool bEightNeighbor)
{
    if (g_bExpired || NULL == pucMask || NULL == pucImg || NULL == pnQueue
        || 0 >= nWidth || 0 >= nHeight || pucMask[y * nWidth + x] == ucLabel
        || nMinTh > pucImg[y * nWidth + x] || nMaxTh < pucImg[y * nWidth + x])
    {
        return false;
    }

    int nHead = 0;
    int nTail = 1;
    const DIR_E nDir = bEightNeighbor ? DIR_EIGHT : DIR_FOUR;
    int nX, nY;
    int nGray = pucImg[y * nWidth + x] + 1; // add one for calculating gravity

    pnQueue[nHead][1] = y;
    pnQueue[nHead][0] = x;
    pucMask[y * nWidth + x] = ucLabel;
    stTrace.nX = x;
    stTrace.nY = y;
    stTrace.nArea = 1;
    stTrace.nGray = nGray;
    stTrace.nMin = nGray - 1;
    stTrace.nMax = nGray - 1;
    stTrace.nXGravity = x * nGray;
    stTrace.nYGravity = y * nGray;
    stTrace.nLeft = x;
    stTrace.nRight = x;
    stTrace.nTop = y;
    stTrace.nBottom = y;
    stTrace.nScore = 0;
    stTrace.nLabel = ucLabel;

    while (nHead != nTail)
    {
        for (int k = 0; k < nDir; k ++)
        {
            nY = pnQueue[nHead][1] + g_anDirections[k][1];
            nX = pnQueue[nHead][0] + g_anDirections[k][0];
            if (nX < 0 || nY < 0 || nX > nWidth - 1 || nY > nHeight - 1)
            {
                continue;
            }

            int nIndex = nY * nWidth + nX;
            nGray = pucImg[nIndex];
            if (nGray >= nMinTh && nGray <= nMaxTh && pucMask[nIndex] != ucLabel)
            {
                nGray++; // at least one for calculating gravity
                pucMask[nIndex] = ucLabel;
                stTrace.nArea++;
                stTrace.nGray += nGray;
                stTrace.nXGravity += nX * nGray;
                stTrace.nYGravity += nY * nGray;
                stTrace.nLeft = min(stTrace.nLeft, nX);
                stTrace.nRight = max(stTrace.nRight, nX);
                stTrace.nTop = min(stTrace.nTop, nY);
                stTrace.nBottom = max(stTrace.nBottom, nY);
                stTrace.nMin = min(stTrace.nMin, nGray - 1);
                stTrace.nMax = max(stTrace.nMax, nGray - 1);

                pnQueue[nTail][0] = nX;
                pnQueue[nTail][1] = nY;
                nTail++;
                nTail &= QUEUESIZE-1;
            }
        }
        nHead++;
        nHead &= QUEUESIZE-1;
    }

    stTrace.nXGravity /= max(stTrace.nGray, 1);
    stTrace.nYGravity /= max(stTrace.nGray, 1);
    stTrace.nGray -= stTrace.nArea; // subtract gray level added before
    stTrace.nGray /= stTrace.nArea;
    stTrace.nBottom++;
    stTrace.nRight++;
    return true;
}

// trace one region with edge points
bool TraceOneRegion(const uchar *pucImg, uchar *pucMask,
                    const int nWidth, const int nHeight,
                    const uchar ucMinTh, const uchar ucMaxTh,
                    const uchar ucLabel, TRACE_AREA_S &stTrace,
                    int pnQueue[][2], int pnEdge[][2], const int QUEUESIZE,
                    int &nEdge, int &nAddArea, int &nGraySum, const bool bEightNeighbor)
{
    if (g_bExpired || NULL == pucMask || NULL == pucImg || NULL == pnQueue
        || NULL == pnEdge || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    const DIR_E nDir = bEightNeighbor ? DIR_EIGHT : DIR_FOUR;
    int nHead = 0, nTail = nEdge, nX, nY;

    nEdge = 0;
    nAddArea = 0;
    nGraySum = 0;

    while (nHead != nTail)
    {
        bool bSaveFlag = true;
        if (nEdge >= QUEUESIZE - 1)
        {
            bSaveFlag = false;
        }

        for (int k = 0; k < nDir; k ++)
        {
            nY = pnQueue[nHead][1] + g_anDirections[k][1];
            nX = pnQueue[nHead][0] + g_anDirections[k][0];
            if (nX < 0 || nY < 0 || nX > nWidth - 1 || nY > nHeight - 1)
            {
                continue;
            }

            int nIndex = nY * nWidth + nX;
            int nGray = pucImg[nIndex];
            if (nGray >= ucMinTh && nGray <= ucMaxTh && pucMask[nIndex] != ucLabel)
            {
                nGray++; // at least one for calculating gravity
                pucMask[nIndex] = ucLabel;
                stTrace.nArea++;
                stTrace.nGray += nGray;
                stTrace.nXGravity += nX * nGray;
                stTrace.nYGravity += nY * nGray;
                stTrace.nLeft = min(stTrace.nLeft, nX);
                stTrace.nRight = max(stTrace.nRight, nX);
                stTrace.nTop = min(stTrace.nTop, nY);
                stTrace.nBottom = max(stTrace.nBottom, nY);
                stTrace.nMin = min(stTrace.nMin, nGray - 1);
                stTrace.nMax = max(stTrace.nMax, nGray - 1);

                nAddArea++;
                nGraySum += nGray;

                pnQueue[nTail][0] = nX;
                pnQueue[nTail][1] = nY;
                nTail++;
                nTail &= QUEUESIZE-1;
            }
            else if ((nGray < ucMinTh || nGray > ucMaxTh) && bSaveFlag)
            {
                pnEdge[nEdge][0] = pnQueue[nHead][0];
                pnEdge[nEdge][1] = pnQueue[nHead][1];
                nEdge++;
                bSaveFlag = false;
            }
        }
        nHead++;
        nHead &= QUEUESIZE-1;
    }

    return true;
}

bool CalcTraceAreaScore(const uchar *pucMask, const uchar *pucBkg,
                        const int nWidth, const int nHeight,
                        const float fMinRatio, const float fMaxRatio,
                        const int nRefSize, const uchar ucTh,
                        TRACE_AREA_S &stTrace)
{
    if (g_bExpired || NULL == pucMask || NULL == pucBkg || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    // 1. gray score
    int nGrayScore = 0;
    if (stTrace.nGray > ucTh)
    {
        // do not make it zero, maybe its relative gray score is good
        nGrayScore = 1;
    }
    else
    {
        nGrayScore = (ucTh - stTrace.nGray) * 100 / max(1, (int) ucTh);
    }

    // 2. relative gray score
    int nRelativeGrayScore = 100;
    const int nW = stTrace.nRight - stTrace.nLeft + 1;
    const int nH = stTrace.nBottom - stTrace.nTop + 1;
#ifdef CONSIDER_CONTRAST
    const int nX = (stTrace.nLeft + stTrace.nRight) / 2;
    const int nY = (stTrace.nTop + stTrace.nBottom) / 2;
    int nRadius = max(nW, nH) / 2 + max(1, max(nW, nH) / 4);
    int x = 0, y = 0, nMinGray = 255;
    for (int i = 0; i < DIR_EIGHT; i++)
    {
        x = nX + nRadius * g_anDirections[i][0];
        y = nY + nRadius * g_anDirections[i][1];
        if (0 > x || 0 > y || nWidth - 1 < x || nHeight - 1 < y)
        {
            continue;
        }
        nMinGray = min(nMinGray, (int) pucBkg[y * nWidth + x]);
    }

    if (nMinGray < stTrace.nGray)
    {
        nRelativeGrayScore = 0;
    }
    else
    {
        nRelativeGrayScore = (nMinGray - stTrace.nGray) * 100 / max(1, (int) ucTh);
    }
#endif

    // 3. Shape Score
    float fW2H = float(nW) / nH;
    float fMidRatio = sqrtf(fMinRatio * fMaxRatio * 1.0f);
    int nShapeScore = 0;
    if (fW2H > fMidRatio)
    {
        nShapeScore = int (100 * fMidRatio / fW2H);
    }
    else
    {
        nShapeScore = int (100 * fW2H / fMidRatio);
    }

    // 4. Size Score
    float fSize = float (min(nW, nH));
    int nSizeScore = 100;
    int nMidSize = max(1, nRefSize / 2);

    if (fSize < nMidSize)
    {
        nSizeScore -= int (80 * (nMidSize-fSize) / nMidSize);
    }

    fSize = float (max(nW, nH));
    nMidSize = nRefSize * 2;
    if (fSize > nMidSize)
    {
        nSizeScore -= int (80 * (fSize-nMidSize) / fSize);
    }

    stTrace.nScore = nGrayScore * 4 / 10 + nRelativeGrayScore * 3 / 10
        + nSizeScore * 1 / 10 + nShapeScore * 2 / 10;

    // compactness
    float fAreaCircle = (float) (M_PI * nW * nH / 4);
    stTrace.nScore -= (int) (fabs(fAreaCircle - stTrace.nArea) * 100 / fAreaCircle);

    return true;
}

bool CalcTraceAreaScore(const uchar *pucMask, const uchar *pucBkg,
                        const int nWidth, const int nHeight,
                        const float fMinRatio, const float fMaxRatio,
                        const int nRefSize, const uchar ucTh,
                        const int anEdge[][2], const int nEdge,
                        TRACE_AREA_S &stTrace)
{
    if (g_bExpired || NULL == pucMask || NULL == pucBkg || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    // 1. gray score
    int nGrayScore = 0;
    if (stTrace.nGray > ucTh)
    {
        // do not make it zero, maybe its relative gray score is good
        nGrayScore = 1;
    }
    else
    {
        nGrayScore = (ucTh - stTrace.nGray) * 100 / max(1, (int) ucTh);
    }

    // 2. relative gray score
    int nRelativeGrayScore = 100;
    const int nW = stTrace.nRight - stTrace.nLeft + 1;
    const int nH = stTrace.nBottom - stTrace.nTop + 1;
#ifdef CONSIDER_CONTRAST
    const int nX = (stTrace.nLeft + stTrace.nRight) / 2;
    const int nY = (stTrace.nTop + stTrace.nBottom) / 2;
    int nRadius = max(nW, nH) / 2 + max(1, max(nW, nH) / 4);
    int x = 0, y = 0, nMinGray = 255;
    for (int i = 0; i < DIR_EIGHT; i++)
    {
        x = nX + nRadius * g_anDirections[i][0];
        y = nY + nRadius * g_anDirections[i][1];
        if (0 > x || 0 > y || nWidth - 1 < x || nHeight - 1 < y)
        {
            continue;
        }
        nMinGray = min(nMinGray, (int) pucBkg[y * nWidth + x]);
    }

    if (nMinGray < stTrace.nGray)
    {
        nRelativeGrayScore = 0;
    }
    else
    {
        nRelativeGrayScore = (nMinGray - stTrace.nGray) * 100 / max(1, (int) ucTh);
    }
#endif

    // 3. Shape Score
    float fW2H = float(nW) / nH;
    float fMidRatio = sqrt(fMinRatio * fMaxRatio * 1.0f);
    int nShapeScore = 0;
    if (fW2H > fMidRatio)
    {
        nShapeScore = int (100 * fMidRatio / fW2H);
    }
    else
    {
        nShapeScore = int (100 * fW2H / fMidRatio);
    }

    // 4. Size Score
    float fSize = float (min(nW, nH));
    int nSizeScore = 100;
    int nMidSize = max(1, nRefSize / 2);

    if (fSize < nMidSize)
    {
        nSizeScore -= int (80 * (nMidSize-fSize) / nMidSize);
    }

    fSize = float (max(nW, nH));
    nMidSize = nRefSize * 2;
    if (fSize > nMidSize)
    {
        nSizeScore -= int (80 * (fSize-nMidSize) / fSize);
    }

    // roundness
    UN_POINT_S astEdge[1024];
    memcpy(astEdge, anEdge, sizeof(UN_POINT_S) * min(1024, nEdge));
    int nRoundness = Roundness(astEdge, nEdge);

    stTrace.nScore = nRoundness * 6 / 10 + nGrayScore * 2 / 10 + nRelativeGrayScore * 1 / 10
        + nSizeScore * 1 / 10 + nShapeScore * 1 / 10;

    // compactness
    float fAreaCircle = (float) (M_PI * nW * nH / 4);
    stTrace.nScore -= (int) (fabs(fAreaCircle - stTrace.nArea) * 100 / fAreaCircle);

    return true;
}

bool IsNewTraceAreaBetter(const TRACE_AREA_S &stRef, const TRACE_AREA_S &stNew,
                          const float fMinRatio, const float fMaxRatio,
                          const int nRefArea, const int nMinScore)
{
    const int nWidth = stNew.nRight - stNew.nLeft + 1;
    const int nHeight = stNew.nBottom - stNew.nTop + 1;
    const float fRatio = (nWidth + 0.0f) / nHeight;
    if (fMinRatio > fRatio || fMaxRatio < fRatio)
    {
        return false;
    }

    if (stNew.nScore >= stRef.nScore && nMinScore <= stNew.nScore)
    {
        return true;
    }

    // make area more important, keep tracing if it's too small
    if (stNew.nArea > stRef.nArea && stNew.nArea < nRefArea * 4 / 5)
    {
        return true;
    }

    if (stNew.nScore <= stRef.nScore * 95 / 100)
    {
        return false;
    }

    const int nAreaScoreNew = (int) fabs(nRefArea - stNew.nArea + 0.0f) * 20 / max(1, nRefArea);
    const int nAreaScoreRef = (int) fabs(nRefArea - stRef.nArea + 0.0f) * 20 / max(1, nRefArea);
    if (stNew.nScore - nAreaScoreNew > stRef.nScore - nAreaScoreRef)
    {
        return true;
    }

    return false;
}

bool TraceTheBestRegion(const uchar *pucImg, const uchar *pucBkg,
                        uchar *pucMask, uchar *pucTmp,
                        const int nWidth, const int nHeight,
                        const int x, const int y, const uchar ucStartTh,
                        const uchar ucMinTh, const uchar ucMaxTh, const uchar ucGlobalTh,
                        const int nMinArea, const int nMaxArea, const int nRefSize,
                        const float fMinRatio, const float fMaxRatio,
                        const uchar ucLabel, TRACE_AREA_S &stTrace,
                        int anQueue[][2], int anEdge[][2], const int QUEUESIZE,
                        const bool bEightNeighbor/* = false*/)
{
    if (g_bExpired || NULL == pucImg || NULL == pucMask || NULL == pucTmp
        || !IsRoiOk(nWidth, nHeight, x, y, x + 1, y + 1)
        || ucStartTh < ucMinTh || ucStartTh > ucMaxTh
        || ucMinTh > ucMaxTh || nMinArea > nMaxArea)
    {
        return false;
    }

    const uchar ucValue = pucImg[y * nWidth + x];
    if (ucMinTh > ucValue || ucMaxTh < ucValue
        || ucLabel == pucMask[y * nWidth + x])
    {
        return false;
    }

    int nEdgeNum = 1;
    int nArea = 1, nGray = pucImg[y * nWidth + x] + 1;
    pucMask[y * nWidth + x] = ucLabel;
    anEdge[0][0] = x;
    anEdge[0][1] = y;

    stTrace.nX = x;
    stTrace.nY = y;
    stTrace.nArea = 1;
    stTrace.nGray = nGray;
    stTrace.nMin = nGray - 1;
    stTrace.nMax = nGray - 1;
    stTrace.nXGravity = x * nGray;
    stTrace.nYGravity = y * nGray;
    stTrace.nLeft = x;
    stTrace.nRight = x;
    stTrace.nTop = y;
    stTrace.nBottom = y;
    stTrace.nScore = 0;
    stTrace.nLabel = ucLabel;

    // tracing step
    uchar ucStep = (ucMaxTh - ucStartTh) / 8 + 1;
    LimitBorder(ucStep, (uchar) 2, (uchar) 32);

    const int nMaxNoAddTimes = 8;
    const int nRefArea = max(32, nRefSize * nRefSize);
    const int nMinScore = 15;
    int nAdd = 0, nGraySum = 0, nNoAdd = 0;
    uchar ucTh = ucStartTh, ucBestTh = ucStartTh;

    TRACE_AREA_S stBest;
    memset(&stBest, 0, sizeof(stBest));
    memcpy(pucTmp, pucMask, sizeof(uchar) * nWidth * nHeight);

    while (ucTh <= ucMaxTh)
    {
        memcpy(anQueue, anEdge, sizeof(int) * nEdgeNum * 2);
        memset(anEdge, 0, sizeof(int) * QUEUESIZE * 2);

        nAdd = nMaxArea;
        TraceOneRegion(pucImg, pucTmp, nWidth, nHeight,
            ucMinTh, ucTh, ucLabel, stTrace, anQueue, anEdge,
            QUEUESIZE, nEdgeNum, nAdd, nGraySum, bEightNeighbor);

        nArea += nAdd;
        // too large
        if (nArea > nMaxArea)
        {
            break;
        }

        // no adding or too small
        if (0 >= nAdd || nArea < nMinArea)
        {
            nNoAdd++;
            // bad for many times
            if (nMaxNoAddTimes <= nNoAdd)
            {
                break;
            }
        }
        else if (nArea >= nMinArea)
        {
            // backup gray value as it will be changed in the following procedure
            int nGrayBak = stTrace.nGray;
            stTrace.nGray /= max(1, stTrace.nArea);
            CalcTraceAreaScore(pucTmp, pucBkg, nWidth, nHeight,
                fMinRatio, fMaxRatio, nRefSize, ucGlobalTh, stTrace);
            stTrace.nGray = nGrayBak;

            bool bImproved = IsNewTraceAreaBetter(stBest, stTrace,
                fMinRatio, fMaxRatio, nRefArea, nMinScore);
            if (bImproved)
            {
                nNoAdd = 0;

                stBest = stTrace;
                ucBestTh = ucTh;

                // update mask
                for (int i = stBest.nTop; i <= stBest.nBottom; ++i)
                {
                    memcpy(pucMask + i * nWidth + stBest.nLeft,
                        pucTmp + i * nWidth + stBest.nLeft,
                        sizeof(uchar) * (stBest.nRight - stBest.nLeft + 1));
                }
            }
            else
            {
                nNoAdd++;
                if ((nMaxNoAddTimes <= nNoAdd)
                    || (nMaxNoAddTimes / 2 <= nNoAdd && 0 >= stBest.nScore))
                {
                    break;
                }

                if (nMinScore <= stBest.nScore
                    && ucBestTh > (ucMaxTh + ucStartTh) / 2)
                {
                    break;
                }

                const int nWidth = stTrace.nRight - stTrace.nLeft + 1;
                const int nHeight = stTrace.nBottom - stTrace.nTop + 1;
                const float fRatio = (nWidth + 0.0f) / nHeight;
                if ((fMinRatio > fRatio || fMaxRatio < fRatio)
                    && nWidth > nRefSize && nHeight > nRefSize)
                {
                    break;
                }
            }
        }

        ucTh += ucStep;
    }

    if (nMinScore / 2 > stBest.nScore)
    {
        // update mask
        for (int i = stTrace.nTop; i <= stTrace.nBottom; ++i)
        {
            memcpy(pucMask + i * nWidth + stTrace.nLeft,
                pucTmp + i * nWidth + stTrace.nLeft,
                sizeof(uchar) * (stTrace.nRight - stTrace.nLeft + 1));
        }

        return false;
    }

    stTrace = stBest;
    stTrace.nBottom++;
    stTrace.nRight++;
    stTrace.nXGravity /= max(stTrace.nGray, 1);
    stTrace.nYGravity /= max(stTrace.nGray, 1);
    stTrace.nGray = ucBestTh; // threshold

    return true;
}
