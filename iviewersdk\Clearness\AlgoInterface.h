/**
* @date         2012-06-21
* @filename     AlgoInterface.h
* @purpose      interface and base class for image processing algorithms
* @version      1.0
* @history      initial draft, improved 2014-06~2014-07
* <AUTHOR> UNIC, Beijing, China
* @copyright    UNIC Technologies, 2005-2015. All rights reserved.
*/

#ifndef __UNIC_ALGO_INTERFACE_H__
#define __UNIC_ALGO_INTERFACE_H__

#include <cmath>
#include <string>
#include <algorithm>
using namespace std;

#ifndef NULL
#define NULL                (0)
#endif

// image constants
#ifndef IMAGE_BORDER
#define IMAGE_BORDER        (2)
#endif
#ifndef IMAGE_MIN_SIZE
#define IMAGE_MIN_SIZE      (32)
#endif
#ifndef IMAGE_MAX_SIZE
#define IMAGE_MAX_SIZE      (4096)
#endif

// KB and MB
#ifndef _KB_
#define _KB_                (1024)
#endif
#ifndef _MB_
#define _MB_                (_KB_ * _KB_)
#endif

#ifndef M_PI
#define M_PI                (3.1415926535897932384626433832795)
#endif

#ifndef M_LOG2
#define M_LOG2              (0.69314718055994530941723212145818)
#endif

#ifndef M_EPS
#define M_EPS               (1e-6)
#endif

// min & max
#ifndef __cplusplus
#ifndef min
#define min(a, b)            (((a) < (b)) ? (a) : (b))
#endif
#ifndef max
#define max(a, b)            (((a) > (b)) ? (a) : (b))
#endif
#endif

// min & max without jumps, for integers only
#ifndef IMIN
#define IMIN(a, b)          ((a) ^ (((a)^(b)) & (((a) < (b)) - 1)))
#endif
#ifndef IMAX
#define IMAX(a, b)          ((a) ^ (((a)^(b)) & (((a) > (b)) - 1)))
#endif

// simple new, return NULL if failed
#ifndef mnew
#define mnew                new(std::nothrow)
#endif

// safe delete
#ifndef mdelete
#define mdelete(p)          { delete [] (p); (p) = NULL; }
#endif

// safe close
#ifndef mclose
#define mclose(p)           { if (NULL != (p)) {fclose(p); (p) = NULL;} }
#endif

// maximal length of variable's name
#define UN_VAR_NAME_LEN     (64)

#ifndef MAX_STR_LEN
#define MAX_STR_LEN         (1024)
#endif

// macro for all types
typedef enum eTypesMacro
{
    UN_BASIC_START = 0x00010001,
    UN_BOOL = UN_BASIC_START,       // bool
    UN_CHAR,                        // char
    UN_UCHAR,                       // unsigned char
    UN_SHORT,                       // short
    UN_USHORT,                      // unsigned short
    UN_INT,                         // int
    UN_UINT,                        // unsigned int
    UN_FLOAT,                       // float
    UN_DOUBLE,                      // double
    UN_INT64,                       // __int64
    UN_UINT64,                      // unsigned __int64
    UN_BASIC_BUTT,                  // end of basic types

    UN_STRUCT_START = 0x00010100,
    UN_POINT = UN_STRUCT_START,     // int point
    UN_SIZE,                        // int size
    UN_RECT,                        // int rectangle
    UN_CIRCLE,                      // int circle
    UN_ELLIPSE,                     // int ellipse
    UN_LINE,                        // int line
    UN_POINT_F,                     // float point
    UN_SIZE_F,                      // float size
    UN_RECT_F,                      // float rectangle
    UN_CIRCLE_F,                    // float circle
    UN_ELLIPSE_F,                   // float ellipse
    UN_LINE_F,                      // float line
    UN_STRUCT_BUTT,                 // end of structures

    UN_IMAGE = 0x00010200,          // image

    UN_ARRAY_START = 0x00010210,
    UN_ARRAY_CHAR = UN_ARRAY_START, // string, array of char
    UN_ARRAY_INT,                   // array of int
    UN_ARRAY_FLOAT,                 // array of float
    UN_ARRAY_DOUBLE,                // array of double
    UN_ARRAY_POINT,                 // array of points
    UN_ARRAY_POINT_F,               // array of float points
    UN_ARRAY_RECT,                  // array of rectangles
    UN_ARRAY_RECT_F,                // array of float rectangles
    UN_ARRAY_CIRCLE,                // array of circles
    UN_ARRAY_CIRCLE_F,              // array of float circles
    UN_ARRAY_ELLIPSE,               // array of ellipses
    UN_ARRAY_ELLIPSE_F,             // array of float ellipses
    UN_ARRAY_LINE,                  // array of lines
    UN_ARRAY_LINE_F,                // array of float lines
    UN_ARRAY_BUTT,

    UI_START = 0x00010300,
    UI_GROUP = UI_START,            // indicating a new group, it's NOT a actual type
    UI_EDIT,                        // int edit
    UI_EDIT_F,                      // float edit
    UI_CHECK,                       // check
    UI_RADIO,                       // radio
    UI_IMAGE,                       // image element for selecting image
    UI_FILE,                        // for selecting any type of file
    UI_FOLDER,                      // for selecting folder
    UI_STRING,                      // string
    UI_BUTT                         // end of UI elements
}UN_TYPE_MACRO_E;

typedef unsigned char       uchar;
typedef unsigned int        uint;
typedef unsigned short      ushort;
typedef __int64             int64;
typedef unsigned __int64    uint64;

// type-length-(name)-value header
typedef struct tagUnTLVHeader
{
    int nType;                      // type of buffer after acName, UN_MACRO_TYPE_E
    int nLength;                    // length of buffer after acName
    char acName[UN_VAR_NAME_LEN];   // name of the following variable

#ifdef __cplusplus
    tagUnTLVHeader()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415 //lint !e1415
    }
#endif

    // memory for value
    // ...
}UN_TLV_HEADER_S;

// image information
typedef struct tagUnImageInfo
{
    int nWidth;                     // image width
    int nHeight;                    // image height
    int nPitch;                     // image pitch
    uchar *pucBuffer;               // image buffer

    int nLeft;                      // left of roi
    int nRight;                     // right of roi
    int nTop;                       // top of roi
    int nBottom;                    // bottom of roi

#ifdef __cplusplus
    tagUnImageInfo()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_IMAGE_INFO_S;

// point
typedef struct tagUnPoint
{
    int nX;                         // x coordinate
    int nY;                         // y coordinate

#ifdef __cplusplus
    tagUnPoint()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_POINT_S;

// size
typedef struct tagUnSize
{
    int nWidth;                     // width
    int nHeight;                    // height

#ifdef __cplusplus
    tagUnSize()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_SIZE_S;

// rectangle
typedef struct tagUnRect
{
    int nLeft;                      // left
    int nRight;                     // right
    int nTop;                       // top
    int nBottom;                    // bottom

#ifdef __cplusplus
    tagUnRect()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_RECT_S;

// circle
typedef struct tagUnCircle
{
    int nX;                         // x of center point
    int nY;                         // y of center point
    int nRadius;                    // radius of circle

#ifdef __cplusplus
    tagUnCircle()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_CIRCLE_S;

// ellipse
typedef struct tagUnEllipse
{
    int nLeft;                      // left of ellipse
    int nTop;                       // top of ellipse
    int nRight;                     // right of ellipse
    int nBottom;                    // bottom of ellipse

#ifdef __cplusplus
    tagUnEllipse()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_ELLIPSE_S;

// int line, nA * x + nB * y + nC = 0
typedef struct tagUnLine
{
    int nA;                         // nA * x + nB * y + nC = 0;
    int nB;
    int nC;
    UN_POINT_S stStart;             // start of line
    UN_POINT_S stEnd;               // end of line

#ifdef __cplusplus
    tagUnLine()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_LINE_S;

// float point
typedef struct tagUnPointF
{
    float fX;                       // x coordinate
    float fY;                       // y coordinate

#ifdef __cplusplus
    tagUnPointF()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_POINT_F_S;

// float size
typedef struct tagUnSizeF
{
    float fWidth;                   // width
    float fHeight;                  // height

#ifdef __cplusplus
    tagUnSizeF()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_SIZE_F_S;

// float rectangle
typedef struct tagUnRectF
{
    float fLeft;                    // left
    float fRight;                   // right
    float fTop;                     // top
    float fBottom;                  // bottom

#ifdef __cplusplus
    tagUnRectF()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_RECT_F_S;

// float circle
typedef struct tagUnCircleF
{
    float fX;                       // x of center
    float fY;                       // y of center
    float fRadius;                  // radius of circle

#ifdef __cplusplus
    tagUnCircleF()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_CIRCLE_F_S;

// float ellipse
typedef struct tagUnEllipseF
{
    float fLeft;                    // left of ellipse
    float fTop;                     // top of ellipse
    float fRight;                   // right of ellipse
    float fBottom;                  // bottom of ellipse

#ifdef __cplusplus
    tagUnEllipseF()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_ELLIPSE_F_S;

// float line, fA * x + fB * y + fC = 0
typedef struct tagUnLineF
{
    float fA;                       // fA * x + fB * y + fC = 0;
    float fB;
    float fC;
    UN_POINT_F_S stStart;           // start of line
    UN_POINT_F_S stEnd;             // end of line

#ifdef __cplusplus
    tagUnLineF()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_LINE_F_S;

// array of char
typedef struct tagUnArrayChar
{
    int nNum;                       // number of char
    char *pcData;                   // data

#ifdef __cplusplus
    tagUnArrayChar()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_ARRAY_CHAR_S;

// array of int
typedef struct tagUnArrayInt
{
    int nNum;                       // number of int
    int *pnData;                    // data

#ifdef __cplusplus
    tagUnArrayInt()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_ARRAY_INT_S;

// array of float
typedef struct tagUnArrayFloat
{
    int nNum;                       // number of float
    float *pfData;                  // data

#ifdef __cplusplus
    tagUnArrayFloat()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_ARRAY_FLOAT_S;

// array of double
typedef struct tagUnArrayDouble
{
    int nNum;                       // number of double
    double *pdData;                 // data

#ifdef __cplusplus
    tagUnArrayDouble()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_ARRAY_DOUBLE_S;

// array of points
typedef struct tagUnArrayPoint
{
    int nNum;                       // number of points
    UN_POINT_S *pstData;            // data

#ifdef __cplusplus
    tagUnArrayPoint()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_ARRAY_POINT_S;

// array of float points
typedef struct tagUnArrayPointF
{
    int nNum;                       // number of float points
    UN_POINT_F_S *pstData;          // data

#ifdef __cplusplus
    tagUnArrayPointF()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_ARRAY_POINT_F_S;

// array of rectangles
typedef struct tagUnArrayRect
{
    int nNum;                       // number of rectangles
    UN_RECT_S *pstData;             // data

#ifdef __cplusplus
    tagUnArrayRect()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_ARRAY_RECT_S;

// array of float rectangles
typedef struct tagUnArrayRectF
{
    int nNum;                       // number of float rectangles
    UN_RECT_F_S *pstData;           // data

#ifdef __cplusplus
    tagUnArrayRectF()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_ARRAY_RECT_F_S;

// array of circles
typedef struct tagUnArrayCircle
{
    int nNum;                       // number of circles
    UN_CIRCLE_S *pstData;           // data

#ifdef __cplusplus
    tagUnArrayCircle()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_ARRAY_CIRCLE_S;

// array of float circles
typedef struct tagUnArrayCircleF
{
    int nNum;                       // number of float circles
    UN_CIRCLE_F_S *pstData;         // data

#ifdef __cplusplus
    tagUnArrayCircleF()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_ARRAY_CIRCLE_F_S;

// array of ellipses
typedef struct tagUnArrayEllipse
{
    int nNum;                       // number of ellipses
    UN_ELLIPSE_S *pstData;          // data

#ifdef __cplusplus
    tagUnArrayEllipse()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_ARRAY_ELLIPSE_S;

// array of float ellipses
typedef struct tagUnArrayEllipseF
{
    int nNum;                       // number of float ellipses
    UN_ELLIPSE_F_S *pstData;        // data

#ifdef __cplusplus
    tagUnArrayEllipseF()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_ARRAY_ELLIPSE_F_S;

// array of lines
typedef struct tagUnArrayLine
{
    int nNum;                       // number of lines
    UN_LINE_S *pstData;           // data

#ifdef __cplusplus
    tagUnArrayLine()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_ARRAY_LINE_S;

// array of float lines
typedef struct tagUnArrayLineF
{
    int nNum;                       // number of float lines
    UN_LINE_F_S *pstData;         // data

#ifdef __cplusplus
    tagUnArrayLineF()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UN_ARRAY_LINE_F_S;

// just a label
typedef struct tagUiGroup
{
}UI_GROUP_S;

// edit
typedef struct tagUiEdit
{
    int nValue;                     // default of current value
    int nMin;                       // minimal value
    int nMax;                       // maximal value

#ifdef __cplusplus
    tagUiEdit()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UI_EDIT_S;

// float edit
typedef struct tagUiEditF
{
    float fValue;                   // default of current value
    float fMin;                     // minimal value
    float fMax;                     // maximal value

#ifdef __cplusplus
    tagUiEditF()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UI_EDIT_F_S;

// check
typedef struct tagUiCheck
{
    bool bChecked;                  // checked or not
    char acReserved[3];             // reserved

#ifdef __cplusplus
    tagUiCheck()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UI_CHECK_S;

typedef struct tagUiRadio
{
    bool bChecked;                  // checked or not
    char acReserved[3];             // reserved

#ifdef __cplusplus
    tagUiRadio()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UI_RADIO_S;

typedef struct tagUiImage
{
    UN_IMAGE_INFO_S stImage;        // image information, shallow pointer
    char acImage[MAX_STR_LEN];      // path of image
    bool bForceGray;                // force gray or not
    char acReserved[3];             // reserved

#ifdef __cplusplus
    tagUiImage()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UI_IMAGE_S;

typedef struct tagUiFile
{
    char acFile[MAX_STR_LEN];       // file path
    char acReserved[4];             // reserved

#ifdef __cplusplus
    tagUiFile()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UI_FILE_S;

typedef struct tagUiFolder
{
    char acFolder[MAX_STR_LEN];     // folder path
    char acReserved[4];             // reserved

#ifdef __cplusplus
    tagUiFolder()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UI_FOLDER_S;

typedef struct tagUiString
{
    char acString[MAX_STR_LEN];     // string
    char acReserved[4];             // reserved

#ifdef __cplusplus
    tagUiString()
    {
        memset(this, 0, sizeof(*this)); //lint !e1415
    }
#endif
}UI_STRING_S;

// length of UN_TLV_HEADER_S
#define UN_TLV_HEADER_LEN       (sizeof(UN_TLV_HEADER_S))

// functions for creating structures
inline UN_POINT_S PointMake(const int nX, const int nY)
{
    UN_POINT_S p;
    p.nX = nX;
    p.nY = nY;
    return p;
}

inline UN_SIZE_S SizeMake(const int nWidth, const int nHeight)
{
    UN_SIZE_S s;
    s.nWidth = nWidth;
    s.nHeight = nHeight;
    return s;
}

inline UN_RECT_S RectMake(const int nLeft, const int nTop,
                          const int nRight, const int nBottom)
{
    UN_RECT_S r;
    r.nLeft = nLeft;
    r.nRight = nRight;
    r.nTop = nTop;
    r.nBottom = nBottom;
    return r;
}

inline UN_RECT_S RectMake(const UN_POINT_S &stStart, const UN_SIZE_S &stSize)
{
    UN_RECT_S r;
    r.nLeft = stStart.nX;
    r.nRight = stStart.nX + stSize.nWidth;
    r.nTop = stStart.nY;
    r.nBottom = stStart.nY + stSize.nHeight;
    return r;
}

inline UN_CIRCLE_S CircleMake(const int nX, const int nY, const int nRadius)
{
    UN_CIRCLE_S c;
    c.nX = nX;
    c.nY = nY;
    c.nRadius = nRadius;
    return c;
}

inline UN_ELLIPSE_S EllipseMake(const int nLeft, const int nTop,
                                const int nRight, const int nBottom)
{
    UN_ELLIPSE_S e;
    e.nLeft = nLeft;
    e.nTop = nTop;
    e.nRight = nRight;
    e.nBottom = nBottom;
    return e;
}

inline UN_LINE_S LineMake(const int nA, const int nB, const int nC)
{
    UN_LINE_S l;
    l.nA = nA;
    l.nB = nB;
    l.nC = nC;
    return l;
}

inline UN_LINE_F_S LineMake(const float fA, const float fB, const float fC)
{
    UN_LINE_F_S l;
    l.fA = fA;
    l.fB = fB;
    l.fC = fC;
    return l;
}

inline UN_POINT_F_S PointMake(const float fX, const float fY)
{
    UN_POINT_F_S p;
    p.fX = fX;
    p.fY = fY;
    return p;
}

inline UN_SIZE_F_S SizeMake(const float fWidth, const float fHeight)
{
    UN_SIZE_F_S s;
    s.fWidth = fWidth;
    s.fHeight = fHeight;
    return s;
}

inline UN_RECT_F_S RectMake(const float fLeft, const float fTop,
                            const float fRight, const float fBottom)
{
    UN_RECT_F_S r;
    r.fLeft = fLeft;
    r.fRight = fRight;
    r.fTop = fTop;
    r.fBottom = fBottom;
    return r;
}

inline UN_RECT_F_S RectMake(const UN_POINT_F_S &stStart, const UN_SIZE_F_S &stSize)
{
    UN_RECT_F_S r;
    r.fLeft = stStart.fX;
    r.fRight = stStart.fX + stSize.fWidth;
    r.fTop = stStart.fY;
    r.fBottom = stStart.fY + stSize.fHeight;
    return r;
}

inline UN_CIRCLE_F_S CircleMake(const float fX, const float fY, const float fRadius)
{
    UN_CIRCLE_F_S c;
    c.fX = fX;
    c.fY = fY;
    c.fRadius = fRadius;
    return c;
}

inline UN_ELLIPSE_F_S EllipseMake(const float fLeft, const float fTop,
                                  const float fRight, const float fBottom)
{
    UN_ELLIPSE_F_S e;
    e.fLeft = fLeft;
    e.fTop = fTop;
    e.fRight = fRight;
    e.fBottom = fBottom;
    return e;
}

uint GetTypeLength(const UN_TYPE_MACRO_E eTypeMacro);
uint GetTypeLengthBasic(const UN_TYPE_MACRO_E eTypeMacro);
uint GetTypeLengthStruct(const UN_TYPE_MACRO_E eTypeMacro);
uint GetTypeLengthArray(const UN_TYPE_MACRO_E eTypeMacro);
uint GetTypeLengthUi(const UN_TYPE_MACRO_E eTypeMacro);
bool IsValidType(const int nType);

// class for packing and unpacking data
class CParaPack
{
 public:
    CParaPack(void);
    ~CParaPack(void);

    int StartPack(void);
    int AddGroup(const char *pcName);
    int AddElem(const void *pData, const uint unLength,
        const UN_TYPE_MACRO_E eType, const char *pcName);
    int StartUnpack(void);
    int GetNextGroup(char *pcName, int &nElem);
    int GetNextElemInfo(UN_TYPE_MACRO_E &eType, char *pcName);
    int GetNextElemData(void *pData, const int nLength);
    int SetValue(const char *pcGroupName, const char *pcElemName, const void *pData);
    int GetValue(const char *pcGroupName, const char *pcElemName, void *pData);
    int GetInfo(const char *pcGroupName, const char *pcElemName, UN_TYPE_MACRO_E &eType);

 private:
    int FindGroup(const char *pcGroupName);
    int FindElem(const char *pcGroupName, const char *pcElemName);

    // make it private to prevent copying
    CParaPack(const CParaPack &other); //lint !e1704

    // make it private to prevent copying
    CParaPack &operator=(const CParaPack &other);

    // member variables
    uchar *m_pucData;           // storing data
    uint m_unDataLen;           // length of whole memory
    uint m_unPackIndex;         // index of current packing position
    uint m_unUnpackIndex;       // index of current unpacking position
};

// class for managing ROIs
class CROI
{
 public:
    // types of supported ROI
    enum eType
    {
        TYPE_RECT = 0,
        TYPE_CIRCLE,
        TYPE_ELLIPSE,
        TYPE_POLYGON,
        TYPE_ALL
    };

    // include or exclude
    enum eStatus
    {
        STATUS_INCLUDE = 0,
        STATUS_EXCLUDE,
        STATUS_ALL
    };

    CROI(void);
    CROI(const CROI &other);
    CROI &operator=(const CROI &other);
    ~CROI(void);

    // get total number of ROIs
    int GetCount() const;
    // get number of include ROIs
    int GetIncludeCount() const;
    // get number of exclude ROIs
    int GetExcludeCount() const;
    // get the union of all include ROIs, return false if there is no include ROI
    bool GetIncludeUnion(int &nLeft, int &nTop, int &nRight, int &nBottom) const;
    // generate mask for all ROIs, nWidth and nHeight are the size of the mask you wanted
    uchar *GetMask(const int nWidth, const int nHeight);

    // add rectangle, circle or ellipse
    bool Add(const int nX, const int nY, const int nWidth,
        const int nHeight, const eType eT, const eStatus eS);
    // add polygon only
    bool Add(const int *pnXs, const int *pnYs, const int nPoints, const eStatus eS);
    // remove the nIndex-th ROI
    bool Remove(const int nIndex);
    // remove all ROIs
    void Clear(void);
    // get the information about the nIndex-th ROI
    bool GetInfo(const int nIndex, eType &eT, eStatus &eS, int &nPoints) const;
    // get the start point and the size of the nIndex-th ROI, POLYGON not supported
    bool GetROI(const int nIndex, int &nX, int &nY, int &nWidth, int &nHeight) const;
    // get all control points of polygon, nIndex-th ROI should be of the type POLYGON
    bool GetROI(const int nIndex, int *pnXs, int *pnYs, const int nPoints) const;
    // get the mask of the nIndex-th ROI, THE MEMORY SHOULD BE RELEASED BY USER
    uchar *GetSingleMask(const int nIndex, int &nStartX, int &nStartY,
        int &nWidth, int &nHeight) const;

 private:
    struct Point_
    {
        int nX, nY;
    };
    const int ConnectTwoPoints(const Point_ &stPoint1, const Point_ &stPoint2,
        Point_* pstPoints, const int nLength) const;
    bool GeneMaskCircle(const int nWidth, const int nHeight, uchar *pucMsk) const;
    bool GeneMaskEllipse(const int nWidth, const int nHeight, uchar *pucMsk) const;
    bool GeneMaskPolygon(const int *pnXs, const int *pnYs, const int nPoints,
        const int nLeft, const int nTop, const int nRight, const int nBottom,
        uchar *pucMsk) const;
    void GeneIncludeMask(uchar *pucMask, const int nWidth, const int nHeight) const;
    void GeneExcludeMask(uchar *pucMask, const int nWidth, const int nHeight) const;
    bool TraceOneRegion(const uchar *pucImg, uchar *pucMask,
        const int nWidth, const int nHeight, const int nX, const int nY,
        const int nMinTh, const int nMaxTh, const uchar ucLabel) const;
    bool ExtendMemory();

    struct Roi_
    {
        int nX, nY, nWidth, nHeight;
        eType eT;
        eStatus eS;
        Roi_()
        {
            memset(this, 0, sizeof(*this));
        }
    };

    // for saving ROIs, nX and nY are addresses index when comes to polygon type
    Roi_ *m_pstRois;
    int m_nMaxNum; // initial number 16
    int m_nCountInc; // number of include ROIs
    int m_nCountExc; // number of exclude ROIs
    uchar *m_pucMsk; // memory for saving mask
    int m_nMskSize; // size of the memory above
    bool m_bChanged; // ROIs have changed or not
    int *m_pnPolygon; // saving polygon points
    int m_nPolySize; // size of polygon memory
    int m_nPolyIndex; // current index of saved polygon points
};

// virtual base class for image processing algorithms
class CAlgoInterface
{
    // for UI
 public:
    CAlgoInterface(void);
    virtual ~CAlgoInterface(void);

    // image algorithm developer should implement these virtual functions
    // pack parameters for ui
    virtual int PackPara(void) = 0;
    // unpack parameters from ui
    virtual int UnpackPara(void) = 0;
    // set extra images (like template or something)
    virtual int SetImage(const int nIndex, const UN_IMAGE_INFO_S *pstImg) = 0;
    // do the main job
    virtual int DoInspect(const UN_IMAGE_INFO_S *pstImg) = 0;
    // pack results for displaying in text
    virtual int PackResult(void) = 0;
    // pack elements for display on image
    virtual int PackDisplay(void) = 0;

    // for setting roi drawn by user
    int SetROI(const CROI &roi);

    // parameters for ui
    int StartUnpackPara(void);
    int GetNextGroupPara(char *pcName, int &nElem);
    int GetNextElemInfoPara(UN_TYPE_MACRO_E &eType, char *pcName);
    int GetNextElemDataPara(void *pData, const int nLength);
    int SetValuePara(const char *pcGroupName, const char *pcElemName, const void *pData);
    int GetValueInfo(const char *pcGroupName, const char *pcElemName, UN_TYPE_MACRO_E &eType);
    int GetValuePara(const char *pcGroupName, const char *pcElemName, void *pData);

    // results for ui
    int StartUnpackResult(void);
    int GetNextGroupResult(char *pcName, int &nElem);
    int GetNextElemInfoResult(UN_TYPE_MACRO_E &eType, char *pcName);
    int GetNextElemDataResult(void *pData, const int nLength);
    int GetValueResult(const char *pcGroupName, const char *pcElemName, void *pData);

    // display for ui
    int StartUnpackDisp(void);
    int GetNextGroupDisp(char *pcName, int &nElem);
    int GetNextElemInfoDisp(UN_TYPE_MACRO_E &eType, char *pcName);
    int GetNextElemDataDisp(void *pData, const int nLength);

    // FOR DERIVED ALGORITHM CLASS
 protected:
    // for parameters
    int StartPackPara(void);
    int AddGroupPara(const char *pcName);
    int AddElemPara(const UI_EDIT_S &stEdit, const char *pcName);
    int AddElemPara(const UI_EDIT_F_S &stEdit, const char *pcName);
    int AddElemPara(const UI_CHECK_S &stCheck, const char *pcName);
    int AddElemPara(const UI_RADIO_S &stRadio, const char *pcName);
    int AddElemPara(const UI_IMAGE_S &stImage, const char *pcName);
    int AddElemPara(const UI_FILE_S &stFile, const char *pcName);
    int AddElemPara(const UI_FOLDER_S &stFolder, const char *pcName);
    int AddElemPara(const UI_STRING_S &stString, const char *pcName);
    int AddElemPara(const void *pData, const uint unLength,
        const UN_TYPE_MACRO_E eType, const char *pcName);

    // for results
    int StartPackResult(void);
    int AddGroupResult(const char *pcName);
    int AddElemResult(const bool bValue, const char *pcName);
    int AddElemResult(const char cValue, const char *pcName);
    int AddElemResult(const uchar ucValue, const char *pcName);
    int AddElemResult(const short sValue, const char *pcName);
    int AddElemResult(const ushort usValue, const char *pcName);
    int AddElemResult(const int nValue, const char *pcName);
    int AddElemResult(const uint unValue, const char *pcName);
    int AddElemResult(const float fValue, const char *pcName);
    int AddElemResult(const double dValue, const char *pcName);
    int AddElemResult(const int64 lValue, const char *pcName);
    int AddElemResult(const uint64 ulValue, const char *pcName);
    int AddElemResult(const UN_POINT_S &stPoint, const char *pcName);
    int AddElemResult(const UN_POINT_F_S &stPoint, const char *pcName);
    int AddElemResult(const UN_SIZE_S &stSize, const char *pcName);
    int AddElemResult(const UN_SIZE_F_S &stSize, const char *pcName);
    int AddElemResult(const UN_RECT_S &stRect, const char *pcName);
    int AddElemResult(const UN_RECT_F_S &stRect, const char *pcName);
    int AddElemResult(const UN_CIRCLE_S &stCircle, const char *pcName);
    int AddElemResult(const UN_CIRCLE_F_S &stCircle, const char *pcName);
    int AddElemResult(const UN_ELLIPSE_S &stEllipse, const char *pcName);
    int AddElemResult(const UN_ELLIPSE_F_S &stEllipse, const char *pcName);
    int AddElemResult(const UN_LINE_S &stLine, const char *pcName);
    int AddElemResult(const UN_LINE_F_S &stLine, const char *pcName);
    int AddElemResult(const UN_ARRAY_CHAR_S &stArrayChar, const char *pcName);
    int AddElemResult(const UN_ARRAY_INT_S &stArrayInt, const char *pcName);
    int AddElemResult(const UN_ARRAY_FLOAT_S &stArrayFloat, const char *pcName);
    int AddElemResult(const UN_ARRAY_DOUBLE_S &stArrayDouble, const char *pcName);
    int AddElemResult(const UN_ARRAY_POINT_S &stArrayPoint, const char *pcName);
    int AddElemResult(const UN_ARRAY_POINT_F_S &stArrayPoint, const char *pcName);
    int AddElemResult(const UN_ARRAY_RECT_S &stArrayRect, const char *pcName);
    int AddElemResult(const UN_ARRAY_RECT_F_S &stArrayRect, const char *pcName);
    int AddElemResult(const UN_ARRAY_CIRCLE_S &stArrayCircle, const char *pcName);
    int AddElemResult(const UN_ARRAY_CIRCLE_F_S &stArrayCircle, const char *pcName);
    int AddElemResult(const UN_ARRAY_ELLIPSE_S &stArrayEllipse, const char *pcName);
    int AddElemResult(const UN_ARRAY_ELLIPSE_F_S &stArrayEllipse, const char *pcName);
    int AddElemResult(const UN_ARRAY_LINE_S &stArrayLine, const char *pcName);
    int AddElemResult(const UN_ARRAY_LINE_F_S &stArrayLine, const char *pcName);
    int AddElemResult(const void *pData, const uint unLength,
        const UN_TYPE_MACRO_E eType, const char *pcName);

    // for display
    int StartPackDisp(void);
    int AddGroupDisp(const char *pcName);
    int AddElemDisp(const UN_POINT_S &stPoint, const char *pcName);
    int AddElemDisp(const UN_POINT_F_S &stPoint, const char *pcName);
    int AddElemDisp(const UN_RECT_S &stRect, const char *pcName);
    int AddElemDisp(const UN_RECT_F_S &stRect, const char *pcName);
    int AddElemDisp(const UN_CIRCLE_S &stCircle, const char *pcName);
    int AddElemDisp(const UN_CIRCLE_F_S &stCircle, const char *pcName);
    int AddElemDisp(const UN_ELLIPSE_S &stEllipse, const char *pcName);
    int AddElemDisp(const UN_ELLIPSE_F_S &stEllipse, const char *pcName);
    int AddElemDisp(const UN_LINE_S &stLine, const char *pcName);
    int AddElemDisp(const UN_LINE_F_S &stLine, const char *pcName);
    int AddElemDisp(const UN_IMAGE_INFO_S &stImage, const char *pcName);
    int AddElemDisp(const UN_ARRAY_CHAR_S &stArrayChar, const char *pcName);
    int AddElemDisp(const UN_ARRAY_POINT_S &stArrayPoint, const char *pcName);
    int AddElemDisp(const UN_ARRAY_POINT_F_S &stArrayPoint, const char *pcName);
    int AddElemDisp(const UN_ARRAY_RECT_S &stArrayRect, const char *pcName);
    int AddElemDisp(const UN_ARRAY_RECT_F_S &stArrayRect, const char *pcName);
    int AddElemDisp(const UN_ARRAY_CIRCLE_S &stArrayCircle, const char *pcName);
    int AddElemDisp(const UN_ARRAY_CIRCLE_F_S &stArrayCircle, const char *pcName);
    int AddElemDisp(const UN_ARRAY_ELLIPSE_S &stArrayEllipse, const char *pcName);
    int AddElemDisp(const UN_ARRAY_ELLIPSE_F_S &stArrayEllipse, const char *pcName);
    int AddElemDisp(const UN_ARRAY_LINE_S &stArrayLine, const char *pcName);
    int AddElemDisp(const UN_ARRAY_LINE_F_S &stArrayLine, const char *pcName);
    int AddElemDisp(const void *pData, const uint unLength,
        const UN_TYPE_MACRO_E eType, const char *pcName);

    // save image (support bmp only)
    void SaveImage(const uchar *pucImg, const int nWidth, const int nHeight,
        const char *pcFile) const;

    // check if the input ROI is the same as m_pRoi
    bool IsTheSame(const CROI &roi) const;

    // roi
    CROI* m_pRoi;

 private:
    // make it private to prevent copying
    CAlgoInterface(const CAlgoInterface &other); //lint !e1704

    // make it private to prevent copying
    CAlgoInterface &operator=(const CAlgoInterface &other);

    // parameters
    CParaPack* m_pPara;

    // results
    CParaPack* m_pResult;

    // displays
    CParaPack* m_pDisp;
};

#endif // __UNIC_ALGO_INTERFACE_H__
