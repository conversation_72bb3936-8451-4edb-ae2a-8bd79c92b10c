#include "DeblurReport.h"
#include <iostream>
#include "log/Logger4cpp.h"
using namespace std;


DeblurReport::DeblurReport()
{

}


DeblurReport::~DeblurReport()
{
}

std::string DeblurReport::type()
{
    return "Debluring";
}

void DeblurReport::process(PathologyImageItemPtr imgItem, const std::vector<WTensor>& outputs)
{
	std::cout << "outputs.size:" << outputs.size() << std::endl;
	if (outputs.size() < 1) { 
		LOG_ERROR("Error outputs num " << outputs.size());
		return;
	}

	auto result = outputs.at(0).template tensor<uint8_t, 4>();
	std::cout << result.dimension(0) << "," << result.dimension(1) << "," << result.dimension(2) << "," << result.dimension(3) << std::endl;
	if ((result.dimension(0) == 0) || (result.dimension(1) == 0) )
		return;

	m_rstImg = cv::Mat(result.dimension(2), result.dimension(1), CV_8UC3, (void*)result.data()).clone();
	
}

void DeblurReport::type(const std::string& typeName)
{

}
