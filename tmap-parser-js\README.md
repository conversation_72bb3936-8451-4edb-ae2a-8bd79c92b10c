# TMAP Parser JS

Node.js implementation of TMAP file parser for medical imaging, specifically designed to parse TMAP version 6.x files.

## Features

- ✅ **TMAP6 Support**: Parses TMAP version 6.x files with proper binary structure alignment
- ✅ **struct-compile Integration**: Uses mature third-party library for binary data parsing to handle alignment issues
- ✅ **Performance Monitoring**: Built-in performance metrics and monitoring
- ✅ **CLI Tool**: Command-line interface for easy file analysis
- ✅ **Comprehensive Parsing**: Extracts image dimensions, scan parameters, tile information, and metadata

## Installation

```bash
# Install dependencies using PNPM
pnpm install
```

## Usage

### CLI Tool

```bash
# Get basic file information (header only)
node src/cli.js info "path/to/file.tmap"

# Parse complete file with performance metrics
node src/cli.js parse "path/to/file.tmap" --performance

# Validate TMAP file
node src/cli.js validate "path/to/file.tmap"

# Batch process directory
node src/cli.js batch "path/to/directory" --recursive
```

### Programmatic API

```javascript
import { getTmapFileInfo, parseTmapFile } from './src/index.js';

// Quick header-only parsing
const basicInfo = await getTmapFileInfo('path/to/file.tmap');
console.log(`Version: ${basicInfo.versionString}`);
console.log(`Dimensions: ${basicInfo.totalWidth} × ${basicInfo.totalHeight}`);

// Full file parsing
const fullInfo = await parseTmapFile('path/to/file.tmap');
console.log('Complete file information:', fullInfo);
```

## Extracted Information

The parser extracts the following information from TMAP files:

### Basic Information
- **File Path & Size**: Original file location and size in bytes
- **Version**: TMAP version (e.g., "6.0")
- **Image Dimensions**: Total image width and height in pixels
- **Scan Parameters**: Magnification level, pixel size (μm/pixel)
- **Image Properties**: Color depth, slide type (H&E, IHC, FISH, etc.)

### Detailed Structure
- **Tile Information**: Tile dimensions, tile count, image grid layout
- **Layer Information**: Pyramid layers, focus layers, zoom levels
- **Extension Data**: Additional metadata and embedded images
- **Performance Metrics**: Parsing time and memory usage statistics

## Example Output

```
📄 TMAP File Info
══════════════════════════════
Version: 6.0
Dimensions: 109066 × 58476
Magnification: 40×
Pixel Size: 0.00010117647616425529 μm/pixel
Slide Type: H&E
Color Depth: 24 bits
```

## Architecture

### Core Components

1. **Structure Definitions** (`src/structures/tmap6-structures.js`)
   - Uses `struct-compile` library for proper binary alignment
   - Matches C++ structures from original iViewerSDK
   - Handles version-specific format differences

2. **Parser Engine** (`src/parsers/tmap6-parser.js`)
   - Binary file reading with proper endianness handling
   - Progressive parsing (header → extension info → image data)
   - Error handling and validation

3. **Performance Monitoring** (`src/utils/performance.js`)
   - Operation timing and memory usage tracking
   - Detailed metrics for optimization
   - Summary statistics

4. **CLI Interface** (`src/cli.js`)
   - Multiple command modes (info, parse, validate, batch)
   - Colored output and progress indicators
   - JSON export capabilities

### Technical Details

- **Binary Alignment**: Uses `struct-compile` to handle C-style struct alignment
- **Version Parsing**: Correctly interprets ASCII version bytes (e.g., '0', '6' → "6.0")
- **Memory Management**: Efficient buffer handling for large files
- **Error Handling**: Comprehensive validation and error reporting

## Testing

```bash
# Run basic functionality test
node test-basic.js

# Run complete test suite
node src/test.js
```

## Performance

The parser is optimized for:
- **Memory Efficiency**: Streaming file reading, minimal memory footprint
- **Speed**: Header-only parsing for quick file inspection
- **Scalability**: Batch processing capabilities for multiple files

## Compatibility

- **Node.js**: Requires Node.js >= 18.0.0
- **TMAP Versions**: Currently supports TMAP 6.x (can be extended for other versions)
- **Platforms**: Cross-platform (Windows, Linux, macOS)

## Development

### Project Structure
```
tmap-parser-js/
├── src/
│   ├── structures/     # Binary structure definitions
│   ├── parsers/        # File parsing logic
│   ├── utils/          # Utilities (performance, buffer reading)
│   ├── cli.js          # Command-line interface
│   ├── index.js        # Main API exports
│   └── test.js         # Test suite
├── package.json        # Dependencies and scripts
└── README.md          # This file
```

### Key Dependencies
- **struct-compile**: Binary structure compilation and alignment
- **commander**: CLI argument parsing
- **chalk**: Terminal colors and formatting
- **ora**: Progress spinners
- **table**: Formatted table output

## License

MIT License - See LICENSE file for details.

## Contributing

1. Follow the existing code style and patterns
2. Add tests for new functionality
3. Update documentation as needed
4. Ensure compatibility with the original iViewerSDK structures
