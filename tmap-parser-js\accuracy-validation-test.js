/**
 * Accuracy Validation Test
 * Test simple algorithm accuracy on different TMAP files
 */

import { Tmap6Parser } from './src/parsers/tmap6-parser.js';
import { 
  extractLabelRegion as opencvExtractLabel, 
  extractMacroRegion as opencvExtractMacro 
} from './src/utils/opencv-segmentation.js';
import sharp from 'sharp';
import fs from 'fs/promises';
import path from 'path';

class AccuracyValidator {
  constructor() {
    this.testResults = [];
  }

  /**
   * Simple algorithm implementation
   */
  async simpleSegmentation(imageBuffer, width, height, channels) {
    const labelWidth = Math.floor(width * 2 / 3);
    const macroLeft = labelWidth;
    const macroWidth = width - macroLeft;

    // Extract label (left 2/3)
    const labelBuffer = Buffer.alloc(labelWidth * height * channels);
    for (let y = 0; y < height; y++) {
      const srcOffset = y * width * channels;
      const dstOffset = y * labelWidth * channels;
      imageBuffer.copy(labelBuffer, dstOffset, srcOffset, srcOffset + labelWidth * channels);
    }

    // Extract macro (right 1/3)
    const macroBuffer = Buffer.alloc(macroWidth * height * channels);
    for (let y = 0; y < height; y++) {
      const srcOffset = y * width * channels + macroLeft * channels;
      const dstOffset = y * macroWidth * channels;
      imageBuffer.copy(macroBuffer, dstOffset, srcOffset, srcOffset + macroWidth * channels);
    }

    return {
      label: { buffer: labelBuffer, width: labelWidth, height },
      macro: { buffer: macroBuffer, width: macroWidth, height }
    };
  }

  /**
   * OpenCV algorithm wrapper
   */
  async opencvSegmentation(imageBuffer, width, height, channels) {
    try {
      const labelResult = await opencvExtractLabel(imageBuffer, width, height, channels);
      const macroResult = await opencvExtractMacro(imageBuffer, width, height, channels);
      
      return {
        label: labelResult,
        macro: macroResult
      };
    } catch (error) {
      console.warn('OpenCV segmentation failed:', error.message);
      return null;
    }
  }

  /**
   * Compare two segmentation results
   */
  compareResults(simple, opencv, originalWidth) {
    if (!opencv) {
      return {
        comparison: 'opencv_failed',
        accuracy: 0,
        pixelDifference: Infinity
      };
    }

    const simpleTotal = simple.label.width + simple.macro.width;
    const opencvTotal = opencv.label.width + opencv.macro.width;
    
    const simpleDiff = Math.abs(simpleTotal - originalWidth);
    const opencvDiff = Math.abs(opencvTotal - originalWidth);
    
    const labelWidthDiff = Math.abs(simple.label.width - opencv.label.width);
    const macroWidthDiff = Math.abs(simple.macro.width - opencv.macro.width);
    
    // Calculate accuracy based on how close simple algorithm is to OpenCV
    const maxDiff = Math.max(labelWidthDiff, macroWidthDiff);
    const accuracy = Math.max(0, 100 - (maxDiff / originalWidth) * 100);
    
    return {
      comparison: 'success',
      accuracy,
      pixelDifference: maxDiff,
      simpleTotal,
      opencvTotal,
      simpleCoverage: (simpleTotal / originalWidth) * 100,
      opencvCoverage: (opencvTotal / originalWidth) * 100,
      labelDiff: labelWidthDiff,
      macroDiff: macroWidthDiff
    };
  }

  /**
   * Test a single TMAP file
   */
  async testFile(filePath) {
    console.log(`\n🔍 Testing: ${path.basename(filePath)}`);
    
    try {
      const parser = new Tmap6Parser();
      await parser.parseFile(filePath);
      
      // Get raw image data
      const macroLabelData = await parser.getMacroLabelImage(true);
      if (!macroLabelData) {
        throw new Error('Failed to get macro-label image');
      }
      
      // Get metadata
      const macroLabelJpeg = await parser.getMacroLabelImage(false);
      const metadata = await sharp(macroLabelJpeg).metadata();
      const { width, height, channels } = metadata;
      
      console.log(`  📐 Dimensions: ${width}x${height}, ${channels} channels`);
      
      // Test both algorithms
      const startTime = Date.now();
      const simpleResult = await this.simpleSegmentation(macroLabelData, width, height, channels);
      const simpleTime = Date.now() - startTime;
      
      const opencvStartTime = Date.now();
      const opencvResult = await this.opencvSegmentation(macroLabelData, width, height, channels);
      const opencvTime = Date.now() - opencvStartTime;
      
      // Compare results
      const comparison = this.compareResults(simpleResult, opencvResult, width);
      
      const result = {
        file: path.basename(filePath),
        width,
        height,
        channels,
        simpleTime,
        opencvTime,
        ...comparison
      };
      
      this.testResults.push(result);
      
      // Print immediate results
      if (comparison.comparison === 'success') {
        console.log(`  ⚡ Simple: ${simpleTime}ms, OpenCV: ${opencvTime}ms`);
        console.log(`  🎯 Accuracy: ${comparison.accuracy.toFixed(1)}%`);
        console.log(`  📏 Pixel diff: ${comparison.pixelDifference} pixels`);
        console.log(`  📊 Coverage: Simple ${comparison.simpleCoverage.toFixed(1)}%, OpenCV ${comparison.opencvCoverage.toFixed(1)}%`);
        
        if (comparison.accuracy >= 95) {
          console.log(`  ✅ Excellent accuracy`);
        } else if (comparison.accuracy >= 90) {
          console.log(`  ✅ Good accuracy`);
        } else if (comparison.accuracy >= 80) {
          console.log(`  ⚠️  Acceptable accuracy`);
        } else {
          console.log(`  ❌ Poor accuracy`);
        }
      } else {
        console.log(`  ❌ OpenCV failed, cannot compare`);
      }
      
      return result;
      
    } catch (error) {
      console.log(`  ❌ Test failed: ${error.message}`);
      return {
        file: path.basename(filePath),
        error: error.message,
        comparison: 'failed'
      };
    }
  }

  /**
   * Find TMAP files in directory
   */
  async findTmapFiles(directory) {
    try {
      const files = await fs.readdir(directory);
      return files
        .filter(file => file.toLowerCase().endsWith('.tmap'))
        .map(file => path.join(directory, file));
    } catch (error) {
      console.warn(`Cannot read directory ${directory}: ${error.message}`);
      return [];
    }
  }

  /**
   * Run validation on multiple files
   */
  async runValidation() {
    console.log('🔬 TMAP Simple Algorithm Accuracy Validation');
    console.log('═'.repeat(60));
    
    // Test files
    const testFiles = [
      'E:\\TMAP\\Test_1.TMAP'
    ];
    
    // Try to find more TMAP files
    const additionalFiles = await this.findTmapFiles('E:\\TMAP');
    const allFiles = [...new Set([...testFiles, ...additionalFiles])];
    
    console.log(`📁 Found ${allFiles.length} TMAP files to test`);
    
    // Test each file
    for (const file of allFiles) {
      try {
        await this.testFile(file);
      } catch (error) {
        console.log(`❌ Failed to test ${path.basename(file)}: ${error.message}`);
      }
    }
    
    this.printSummary();
  }

  /**
   * Print validation summary
   */
  printSummary() {
    console.log('\n📊 Validation Summary');
    console.log('═'.repeat(60));
    
    const successfulTests = this.testResults.filter(r => r.comparison === 'success');
    const failedTests = this.testResults.filter(r => r.comparison === 'failed');
    const opencvFailedTests = this.testResults.filter(r => r.comparison === 'opencv_failed');
    
    console.log(`\n📈 Test Results:`);
    console.log(`  Total files tested: ${this.testResults.length}`);
    console.log(`  Successful comparisons: ${successfulTests.length}`);
    console.log(`  OpenCV failures: ${opencvFailedTests.length}`);
    console.log(`  Test failures: ${failedTests.length}`);
    
    if (successfulTests.length > 0) {
      const accuracies = successfulTests.map(r => r.accuracy);
      const avgAccuracy = accuracies.reduce((a, b) => a + b, 0) / accuracies.length;
      const minAccuracy = Math.min(...accuracies);
      const maxAccuracy = Math.max(...accuracies);
      
      const pixelDiffs = successfulTests.map(r => r.pixelDifference);
      const avgPixelDiff = pixelDiffs.reduce((a, b) => a + b, 0) / pixelDiffs.length;
      const maxPixelDiff = Math.max(...pixelDiffs);
      
      console.log(`\n🎯 Accuracy Analysis:`);
      console.log(`  Average accuracy: ${avgAccuracy.toFixed(1)}%`);
      console.log(`  Accuracy range: ${minAccuracy.toFixed(1)}% - ${maxAccuracy.toFixed(1)}%`);
      console.log(`  Average pixel difference: ${avgPixelDiff.toFixed(1)} pixels`);
      console.log(`  Maximum pixel difference: ${maxPixelDiff} pixels`);
      
      const excellentCount = accuracies.filter(a => a >= 95).length;
      const goodCount = accuracies.filter(a => a >= 90 && a < 95).length;
      const acceptableCount = accuracies.filter(a => a >= 80 && a < 90).length;
      const poorCount = accuracies.filter(a => a < 80).length;
      
      console.log(`\n📊 Accuracy Distribution:`);
      console.log(`  Excellent (≥95%): ${excellentCount} files`);
      console.log(`  Good (90-95%): ${goodCount} files`);
      console.log(`  Acceptable (80-90%): ${acceptableCount} files`);
      console.log(`  Poor (<80%): ${poorCount} files`);
      
      // Performance comparison
      const simpleTimes = successfulTests.map(r => r.simpleTime);
      const opencvTimes = successfulTests.map(r => r.opencvTime);
      const avgSimpleTime = simpleTimes.reduce((a, b) => a + b, 0) / simpleTimes.length;
      const avgOpencvTime = opencvTimes.reduce((a, b) => a + b, 0) / opencvTimes.length;
      const speedup = avgOpencvTime / avgSimpleTime;
      
      console.log(`\n⚡ Performance Comparison:`);
      console.log(`  Average simple time: ${avgSimpleTime.toFixed(2)}ms`);
      console.log(`  Average OpenCV time: ${avgOpencvTime.toFixed(2)}ms`);
      console.log(`  Speed improvement: ${speedup.toFixed(1)}x faster`);
    }
    
    console.log(`\n🏆 Recommendation:`);
    if (successfulTests.length === 0) {
      console.log(`❌ Cannot recommend simple algorithm - no successful tests`);
    } else {
      const avgAccuracy = successfulTests.map(r => r.accuracy).reduce((a, b) => a + b, 0) / successfulTests.length;
      if (avgAccuracy >= 95) {
        console.log(`✅ Simple algorithm is highly recommended (${avgAccuracy.toFixed(1)}% average accuracy)`);
      } else if (avgAccuracy >= 90) {
        console.log(`✅ Simple algorithm is recommended (${avgAccuracy.toFixed(1)}% average accuracy)`);
      } else if (avgAccuracy >= 80) {
        console.log(`⚠️  Simple algorithm is acceptable but consider OpenCV for critical applications`);
      } else {
        console.log(`❌ Simple algorithm is not recommended - use OpenCV for better accuracy`);
      }
    }
  }
}

// Run validation
async function runAccuracyValidation() {
  const validator = new AccuracyValidator();
  
  try {
    await validator.runValidation();
  } catch (error) {
    console.error('❌ Validation failed:', error.message);
    process.exit(1);
  }
}

runAccuracyValidation().catch(console.error);
