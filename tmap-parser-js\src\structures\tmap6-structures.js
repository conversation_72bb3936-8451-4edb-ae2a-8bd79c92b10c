/**
 * TMAP6 File Format Structures
 * Based on the C++ structures from iViewerSDK
 */

import { compile } from 'struct-compile';

// Constants
export const MAX_TILE_NUM = 16;
export const MAX_FILE_NUM = 8;
export const MAX_LAYER_SIZE = 16;

// Compile TMAP structures using struct-compile
const structDefinition = `
  // TMAP Header Structure (for version 6.0) - matches TMAP_HEADER from IS_KernelDefine.h
  struct TmapHeader {
    uint8_t header[4];        // must be "TMAP"
    uint8_t mainVersion[2];   // main version number
    uint8_t maxFocusNum;      // focus num = 2*n+1
    uint8_t imageFormat;      // reserved byte
    uint8_t fileNum;          // less than MAX_FILE_NUM
    uint8_t layerSize;        // less than MAX_LAYER_SIZE
    uint8_t imgColor;         // support 8, 24
    uint8_t checkSum;         // to check whether data error occurs
    uint8_t ratioStep;        // ratio step
    uint8_t maxLaySize;       // maximal layer
    uint8_t slideType;        // 0 HE
    uint8_t bkColor;          // background color
    float pixelSize;          // pixel size for 100X image (um/pixel)
    int32_t totalImgNum;      // total image number

    // Image data info
    int16_t maxZoomRate;      // the maximal image zoom rate, usually be 10, 20, 40, 100
    int16_t imgCol;           // the column number
    int16_t imgRow;           // the row number
    int16_t imgWidth;         // image width
    int16_t imgHeight;        // image height
    int16_t tileWidth;        // tile width
    int16_t tileHeight;       // tile height
    int16_t airImgWidth;      // air image width
    int16_t airImgHeight;     // air image height
    int32_t shrinkTileNum;    // shrink tile number
    int32_t totalImgWidth;    // total image width
    int32_t totalImgHeight;   // total image height
    int32_t airImgOffset;     // air image data offset (long in C++)
  };

  // TMAP Extension Info Structure
  struct TmapExtInfo {
    uint32_t maxExtDataLen;   // Maximal Extent data length
    uint32_t sumExtDataLen;   // sum of data length
    uint32_t tmapDataEndPos;  // TMAP data end position
    uint32_t dataType[8];     // Extension data types
    uint32_t dataOffset[8];   // Data offsets
    uint32_t dataLength[8];   // Data lengths
    uint8_t reserveByte[24];  // reserved bytes
  };

  // Tile Info Structure
  struct TileInfo {
    uint8_t layerNo;          // less than MAX_LAYER_SIZE
    uint8_t tileCol;          // tile col NO
    uint8_t tileRow;          // tile row NO
    int32_t fileOffset;       // tile data offset
    uint32_t length;          // tile data length
  };

  // Image Info Structure (for version 5.0 and 6.0)
  struct ImageInfo5 {
    uint8_t fileID;           // less than MAX_FILE_NUM
    int8_t layer;             // layer number, usually from -10 ~ 10
    uint8_t reversed[2];      // reverse two bytes
    int8_t topDx;             // Dx with the top image
    int8_t topDy;             // Dy with the top image
    int8_t leftDx;            // Dx with the left image
    int8_t leftDy;            // Dy with the left image
    int16_t imgCol;           // image col NO
    int16_t imgRow;           // image Row NO
    int32_t x;                // X coordinate in big image
    int32_t y;                // Y coordinate in big image
    struct TileInfo tiles[16]; // tile information array
  };

  // Shrink Tile Info Structure
  struct ShrinkTileInfo {
    uint8_t fileID;           // less than MAX_FILE_NUM
    uint8_t layerNo;          // less than MAX_LAYER_SIZE
    int32_t x;                // X coordinate in big image
    int32_t y;                // Y coordinate in big image
    int32_t fileOffset;       // tile data offset
    uint32_t length;          // tile data length
  };
`;

// Compile the structures
const compiledStructs = compile(structDefinition);

// Export the compiled structures
export const {
  TmapHeader,
  TmapExtInfo,
  TileInfo,
  ImageInfo5,
  ShrinkTileInfo
} = compiledStructs;

// Image Type Enumeration
export const ImageType = {
  THUMBNAIL: 0,
  NAVIGATE: 1,
  MACRO: 2,
  LABEL: 3,
  MACRO_LABEL: 4,
  TILE: 5,
  WHOLE: 6,
  ALL: 7
};

// Extension Type Enumeration
export const ExtensionType = {
  NONE: 0,
  MACRO_IMAGE: 1,
  THUMB_IMAGE: 2,
  CODE_ID: 3,
  SLIDE_INFO: 4,
  SYSTEM_INFO: 5,
  RUNTIME_INFO: 6,
  MARK_INFO: 7,
  TOTAL_TYPE: 8
};

// Compression Algorithm Enumeration
export const CompressionAlgo = {
  QUICK: 0,
  JPEG: 1,
  J2K: 2
};

// Slide Type Enumeration
export const SlideType = {
  HE: 0,
  IHC: 1,
  FISH: 2,
  OTHER: 3
};
