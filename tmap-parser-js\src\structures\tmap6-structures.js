/**
 * TMAP6 File Format Structures
 * Based on the C++ structures from iViewerSDK
 */

import { compile } from 'struct-compile';

// Constants
export const MAX_TILE_NUM = 16;
export const MAX_FILE_NUM = 8;
export const MAX_LAYER_SIZE = 16;

// Compile TMAP structures using struct-compile
const structDefinition = `
  struct TmapHeader {
    uint8_t header[4];
    uint8_t mainVersion[2];
    uint8_t maxFocusNum;
    uint8_t imageFormat;
    uint8_t fileNum;
    uint8_t layerSize;
    uint8_t imgColor;
    uint8_t checkSum;
    uint8_t ratioStep;
    uint8_t maxLaySize;
    uint8_t slideType;
    uint8_t bkColor;
    float pixelSize;
    int32_t totalImgNum;
    int16_t maxZoomRate;
    int16_t imgCol;
    int16_t imgRow;
    int16_t imgWidth;
    int16_t imgHeight;
    int16_t tileWidth;
    int16_t tileHeight;
    int16_t airImgWidth;
    int16_t airImgHeight;
    int32_t shrinkTileNum;
    int32_t totalImgWidth;
    int32_t totalImgHeight;
    int32_t airImgOffset;
  };

  struct TmapExtInfo {
    uint32_t maxExtDataLen;
    uint32_t sumExtDataLen;
    uint32_t tmapDataEndPos;
    uint32_t dataType[8];
    uint32_t dataOffset[8];
    uint32_t dataLength[8];
    uint8_t reserveByte[24];
  };

  struct TileInfo {
    uint8_t layerNo;
    uint8_t tileCol;
    uint8_t tileRow;
    int32_t fileOffset;
    uint32_t dataLength;
  };

  struct ImageInfo5 {
    uint8_t fileID;
    int8_t layer;
    uint8_t reversed[2];
    int8_t topDx;
    int8_t topDy;
    int8_t leftDx;
    int8_t leftDy;
    int16_t imgCol;
    int16_t imgRow;
    int32_t x;
    int32_t y;
  };

  struct ShrinkTileInfo {
    uint8_t fileID;
    uint8_t layerNo;
    int32_t x;
    int32_t y;
    int32_t fileOffset;
    uint32_t dataLength;
  };
`;

// Compile the structures
let compiledStructs;
try {
  compiledStructs = compile(structDefinition);
} catch (error) {
  console.error('Struct compilation error:', error.message);
  throw error;
}

// Export the compiled structures
export const {
  TmapHeader,
  TmapExtInfo,
  TileInfo,
  ImageInfo5,
  ShrinkTileInfo
} = compiledStructs;

// Image Type Enumeration (matches _TMAP_IMAGE_TYPE)
export const ImageType = {
  THUMBNAIL: 0,     // uImageThumbnail - 小图，用于浏览器显示图标，图像大小 <= 256
  NAVIGATE: 1,      // uImageNavigate - 导航图，用于显示缩略图，图像大小 <= 640
  MACRO: 2,         // uImageMacro - 切片组织部分整体图像
  LABEL: 3,         // uImageLabel - 切片标签图像
  MACRO_LABEL: 4,   // uImageMacroLabel - 整个切片大体图像
  TILE: 5,          // uImageTile - 组成切片的小图像块，每个图像块图像大小为256*256
  WHOLE: 6,         // uImageWhole - 切片数字图像，指扫描后的图像
  ALL: 7            // uImageAll
};

// Extension Type Enumeration
export const ExtensionType = {
  NONE: 0,
  MACRO_IMAGE: 1,
  THUMB_IMAGE: 2,
  CODE_ID: 3,
  SLIDE_INFO: 4,
  SYSTEM_INFO: 5,
  RUNTIME_INFO: 6,
  MARK_INFO: 7,
  TOTAL_TYPE: 8
};

// Compression Algorithm Enumeration
export const CompressionAlgo = {
  QUICK: 0,
  JPEG: 1,
  J2K: 2
};

// Slide Type Enumeration
export const SlideType = {
  HE: 0,
  IHC: 1,
  FISH: 2,
  OTHER: 3
};
