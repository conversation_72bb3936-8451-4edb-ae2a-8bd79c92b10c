/**
 * TMAP6 File Format Structures
 * Based on the C++ structures from iViewerSDK
 */

import { Struct } from 'struct-compile';

// Constants
export const MAX_TILE_NUM = 16;
export const MAX_FILE_NUM = 8;
export const MAX_LAYER_SIZE = 16;

// TMAP Header Structure (for version 6.0)
export const TmapHeaderStruct = Struct({
  header: 'char[4]',           // must be "TMAP"
  mainVersion: 'char[2]',      // main version number
  maxFocusNum: 'uint8',        // focus num = 2*n+1
  imageFormat: 'uint8',        // reserved byte
  fileNum: 'uint8',            // less than MAX_FILE_NUM
  layerSize: 'uint8',          // less than MAX_LAYER_SIZE
  imgColor: 'uint8',           // support 8, 24
  checkSum: 'uint8',           // to check whether data error occurs
  ratioStep: 'uint8',          // ratio step
  maxLaySize: 'uint8',         // maximal layer
  slideType: 'uint8',          // 0 HE
  bkColor: 'uint8',            // background color
  pixelSize: 'float32',        // pixel size for 100X image (um/pixel)
  totalImgNum: 'int32',        // total image number
  
  // Image data info
  maxZoomRate: 'int16',        // the maximal image zoom rate, usually be 10, 20, 40, 100
  imgCol: 'int16',             // the column number
  imgRow: 'int16',             // the row number
  imgWidth: 'int16',           // image width
  imgHeight: 'int16',          // image height
  tileWidth: 'int16',          // tile width
  tileHeight: 'int16',         // tile height
  airImgWidth: 'int16',        // air image width
  airImgHeight: 'int16',       // air image height
  shrinkTileNum: 'int32',      // shrink tile number
  totalImgWidth: 'int32',      // total image width
  totalImgHeight: 'int32',     // total image height
  airImgOffset: 'int32'        // air image data offset
});

// TMAP Extension Info Structure
export const TmapExtInfoStruct = Struct({
  maxExtDataLen: 'uint32',     // Maximal Extent data length
  sumExtDataLen: 'uint32',     // sum of data length
  tmapDataEndPos: 'uint32',    // TMAP data end position
  dataType: 'uint32[8]',       // Extension data types
  dataOffset: 'uint32[8]',     // Data offsets
  dataLength: 'uint32[8]',     // Data lengths
  reserveByte: 'uint8[24]'     // reserved bytes
});

// Tile Info Structure
export const TileInfoStruct = Struct({
  layerNo: 'uint8',            // less than MAX_LAYER_SIZE
  tileCol: 'uint8',            // tile col NO
  tileRow: 'uint8',            // tile row NO
  fileOffset: 'int32',         // tile data offset
  length: 'uint32'             // tile data length
});

// Image Info Structure (for version 5.0 and 6.0)
export const ImageInfo5Struct = Struct({
  fileID: 'uint8',             // less than MAX_FILE_NUM
  layer: 'int8',               // layer number, usually from -10 ~ 10
  reversed: 'uint8[2]',        // reverse two bytes
  topDx: 'int8',               // Dx with the top image
  topDy: 'int8',               // Dy with the top image
  leftDx: 'int8',              // Dx with the left image
  leftDy: 'int8',              // Dy with the left image
  imgCol: 'int16',             // image col NO
  imgRow: 'int16',             // image Row NO
  x: 'int32',                  // X coordinate in big image
  y: 'int32',                  // Y coordinate in big image
  tiles: `${TileInfoStruct.name}[${MAX_TILE_NUM}]`  // tile information array
});

// Shrink Tile Info Structure
export const ShrinkTileInfoStruct = Struct({
  fileID: 'uint8',             // less than MAX_FILE_NUM
  layerNo: 'uint8',            // less than MAX_LAYER_SIZE
  x: 'int32',                  // X coordinate in big image
  y: 'int32',                  // Y coordinate in big image
  fileOffset: 'int32',         // tile data offset
  length: 'uint32'             // tile data length
});

// Main TMAP Info Structure (for version 5.0 and 6.0)
export const TmapInfo5Struct = Struct({
  header: TmapHeaderStruct.name,
  extInfo: TmapExtInfoStruct.name
  // Note: pstImgInfo and pShrinkTile are dynamic arrays, handled separately
});

// Image Type Enumeration
export const ImageType = {
  THUMBNAIL: 0,
  NAVIGATE: 1,
  MACRO: 2,
  LABEL: 3,
  MACRO_LABEL: 4,
  TILE: 5,
  WHOLE: 6,
  ALL: 7
};

// Extension Type Enumeration
export const ExtensionType = {
  NONE: 0,
  MACRO_IMAGE: 1,
  THUMB_IMAGE: 2,
  CODE_ID: 3,
  SLIDE_INFO: 4,
  SYSTEM_INFO: 5,
  RUNTIME_INFO: 6,
  MARK_INFO: 7,
  TOTAL_TYPE: 8
};

// Compression Algorithm Enumeration
export const CompressionAlgo = {
  QUICK: 0,
  JPEG: 1,
  J2K: 2
};

// Slide Type Enumeration
export const SlideType = {
  HE: 0,
  IHC: 1,
  FISH: 2,
  OTHER: 3
};
