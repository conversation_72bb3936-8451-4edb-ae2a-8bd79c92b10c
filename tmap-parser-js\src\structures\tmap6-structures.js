/**
 * TMAP6 File Format Structures
 * Based on the C++ structures from iViewerSDK
 */

import { compile } from 'struct-compile';

// Constants
export const MAX_TILE_NUM = 16;
export const MAX_FILE_NUM = 8;
export const MAX_LAYER_SIZE = 16;

// Compile TMAP structures using struct-compile
const structDefinition = `
  struct TmapHeader {
    uint8_t header[4];
    uint8_t mainVersion[2];
    uint8_t maxFocusNum;
    uint8_t imageFormat;
    uint8_t fileNum;
    uint8_t layerSize;
    uint8_t imgColor;
    uint8_t checkSum;
    uint8_t ratioStep;
    uint8_t maxLaySize;
    uint8_t slideType;
    uint8_t bkColor;
    float pixelSize;
    int32_t totalImgNum;
    int16_t maxZoomRate;
    int16_t imgCol;
    int16_t imgRow;
    int16_t imgWidth;
    int16_t imgHeight;
    int16_t tileWidth;
    int16_t tileHeight;
    int16_t airImgWidth;
    int16_t airImgHeight;
    int32_t shrinkTileNum;
    int32_t totalImgWidth;
    int32_t totalImgHeight;
    int32_t airImgOffset;
  };

  struct TmapExtInfo {
    uint32_t maxExtDataLen;
    uint32_t sumExtDataLen;
    uint32_t tmapDataEndPos;
    uint32_t dataType[8];
    uint32_t dataOffset[8];
    uint32_t dataLength[8];
    uint8_t reserveByte[24];
  };

  struct TileInfo {
    uint8_t layerNo;
    uint8_t tileCol;
    uint8_t tileRow;
    int32_t fileOffset;
    uint32_t length;
  };

  struct ImageInfo5 {
    uint8_t fileID;
    int8_t layer;
    uint8_t reversed[2];
    int8_t topDx;
    int8_t topDy;
    int8_t leftDx;
    int8_t leftDy;
    int16_t imgCol;
    int16_t imgRow;
    int32_t x;
    int32_t y;
    struct TileInfo tiles[16];
  };

  struct ShrinkTileInfo {
    uint8_t fileID;
    uint8_t layerNo;
    int32_t x;
    int32_t y;
    int32_t fileOffset;
    uint32_t length;
  };
`;

// Compile the structures
const compiledStructs = compile(structDefinition);

// Export the compiled structures
export const {
  TmapHeader,
  TmapExtInfo,
  TileInfo,
  ImageInfo5,
  ShrinkTileInfo
} = compiledStructs;

// Image Type Enumeration
export const ImageType = {
  THUMBNAIL: 0,
  NAVIGATE: 1,
  MACRO: 2,
  LABEL: 3,
  MACRO_LABEL: 4,
  TILE: 5,
  WHOLE: 6,
  ALL: 7
};

// Extension Type Enumeration
export const ExtensionType = {
  NONE: 0,
  MACRO_IMAGE: 1,
  THUMB_IMAGE: 2,
  CODE_ID: 3,
  SLIDE_INFO: 4,
  SYSTEM_INFO: 5,
  RUNTIME_INFO: 6,
  MARK_INFO: 7,
  TOTAL_TYPE: 8
};

// Compression Algorithm Enumeration
export const CompressionAlgo = {
  QUICK: 0,
  JPEG: 1,
  J2K: 2
};

// Slide Type Enumeration
export const SlideType = {
  HE: 0,
  IHC: 1,
  FISH: 2,
  OTHER: 3
};
